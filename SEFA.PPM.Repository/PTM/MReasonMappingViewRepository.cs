using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// MReasonMappingViewRepository
	/// </summary>
    public class MReasonMappingViewRepository : BaseRepository<MReasonMappingViewEntity>, IMReasonMappingViewRepository
    {
        public MReasonMappingViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}