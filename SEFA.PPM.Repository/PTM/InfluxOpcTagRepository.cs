using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Repository.Base;
using SEFA.PPM.Model.Models.PTM;

namespace SEFA.PPM.Repository.PTM
{
    /// <summary>
    /// InfluxOpcTagRepository
    /// </summary>
    public class InfluxOpcTagRepository : BaseRepository<InfluxOpcTagEntity>, IInfluxOpcTagRepository
    {
        public InfluxOpcTagRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}