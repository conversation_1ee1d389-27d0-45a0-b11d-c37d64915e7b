using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Repository.Base;
using SEFA.PPM.Model.Models.PTM;

namespace SEFA.PPM.Repository.PTM
{
    /// <summary>
    /// SegmentlistRepository
    /// </summary>
    public class SegmentlistRepository : BaseRepository<SegmentlistEntity>, ISegmentlistRepository
    {
        public SegmentlistRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}