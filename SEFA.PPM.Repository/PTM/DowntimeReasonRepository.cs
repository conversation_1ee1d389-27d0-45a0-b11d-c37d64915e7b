using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// DowntimeReasonRepository
	/// </summary>
    public class DowntimeReasonRepository : BaseRepository<DowntimeReasonEntity>, IDowntimeReasonRepository
    {
        public DowntimeReasonRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}