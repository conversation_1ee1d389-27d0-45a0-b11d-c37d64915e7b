using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// LineAndEquipmentViewRepository
	/// </summary>
    public class LineAndEquipmentViewRepository : BaseRepository<LineAndEquipmentViewEntity>, ILineAndEquipmentViewRepository
    {
        public LineAndEquipmentViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}