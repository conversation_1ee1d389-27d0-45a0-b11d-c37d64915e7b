using SEFA.PTM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PTM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PTM.Repository
{
	/// <summary>
	/// EquipmentFunctionViewRepository
	/// </summary>
    public class EquipmentFunctionViewRepository : BaseRepository<EquipmentFunctionViewEntity>, IEquipmentFunctionViewRepository
    {
        public EquipmentFunctionViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }

    }
}