using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// ProduceActualViewRepository
	/// </summary>
    public class ProduceActualViewRepository : BaseRepository<ProduceActualViewEntity>, IProduceActualViewRepository
    {
        public ProduceActualViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}