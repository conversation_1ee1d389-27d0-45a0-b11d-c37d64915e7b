using SEFA.MKM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.MKM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.MKM.Repository
{
	/// <summary>
	/// MPreparationViewRepository
	/// </summary>
    public class MPreparationViewRepository : BaseRepository<MPreparationViewEntity>, IMPreparationViewRepository
    {
        public MPreparationViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}