using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// StorageInventoryRepository
	/// </summary>
    public class StorageInventoryRepository : BaseRepository<StorageInventoryEntity>, IStorageInventoryRepository
    {
        public StorageInventoryRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}