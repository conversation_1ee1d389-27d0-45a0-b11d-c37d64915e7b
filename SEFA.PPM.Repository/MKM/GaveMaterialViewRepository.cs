using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// GaveMaterialViewRepository
	/// </summary>
    public class GaveMaterialViewRepository : BaseRepository<GaveMaterialViewEntity>, IGaveMaterialViewRepository
    {
        public GaveMaterialViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}