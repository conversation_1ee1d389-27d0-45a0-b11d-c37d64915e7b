using SEFA.MKM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.MKM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.MKM.Repository
{
	/// <summary>
	/// MaterialDispositionRepository
	/// </summary>
    public class MaterialDispositionRepository : BaseRepository<MaterialDispositionEntity>, IMaterialDispositionRepository
    {
        public MaterialDispositionRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}