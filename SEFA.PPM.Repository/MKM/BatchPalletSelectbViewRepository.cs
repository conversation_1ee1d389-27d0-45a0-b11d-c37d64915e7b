using SEFA.MKM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.MKM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.MKM.Repository
{
	/// <summary>
	/// BatchPalletSelectbViewRepository
	/// </summary>
    public class BatchPalletSelectbViewRepository : BaseRepository<BatchPalletSelectbViewEntity>, IBatchPalletSelectbViewRepository
    {
        public BatchPalletSelectbViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}