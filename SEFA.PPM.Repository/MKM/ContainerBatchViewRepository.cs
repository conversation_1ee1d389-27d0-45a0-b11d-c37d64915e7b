using SEFA.MKM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.MKM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.MKM.Repository
{
	/// <summary>
	/// ContainerBatchViewRepository
	/// </summary>
    public class ContainerBatchViewRepository : BaseRepository<ContainerBatchViewEntity>, IContainerBatchViewRepository
    {
        public ContainerBatchViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}