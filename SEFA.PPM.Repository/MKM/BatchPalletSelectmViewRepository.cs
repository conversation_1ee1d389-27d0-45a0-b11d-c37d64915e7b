using SEFA.MKM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.MKM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.MKM.Repository
{
	/// <summary>
	/// BatchPalletSelectmViewRepository
	/// </summary>
    public class BatchPalletSelectmViewRepository : BaseRepository<BatchPalletSelectmViewEntity>, IBatchPalletSelectmViewRepository
    {
        public BatchPalletSelectmViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}