using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// ColdQuantityViewRepository
	/// </summary>
    public class ColdQuantityViewRepository : BaseRepository<ColdQuantityViewEntity>, IColdQuantityViewRepository
    {
        public ColdQuantityViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}