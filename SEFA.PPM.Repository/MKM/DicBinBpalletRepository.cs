using SEFA.MKM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.MKM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.MKM.Repository
{
	/// <summary>
	/// DicBinBpalletRepository
	/// </summary>
    public class DicBinBpalletRepository : BaseRepository<DicBinBpalletEntity>, IDicBinBpalletRepository
    {
        public DicBinBpalletRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}