using SEFA.MKM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.MKM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.MKM.Repository
{
	/// <summary>
	/// MaterialLotRepository
	/// </summary>
    public class MaterialLotRepository : BaseRepository<MaterialLotEntity>, IMaterialLotRepository
    {
        public MaterialLotRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}