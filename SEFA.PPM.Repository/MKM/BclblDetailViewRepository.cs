using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// BclblDetailViewRepository
	/// </summary>
    public class BclblDetailViewRepository : BaseRepository<BclblDetailViewEntity>, IBclblDetailViewRepository
    {
        public BclblDetailViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}