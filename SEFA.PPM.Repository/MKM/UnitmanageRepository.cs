using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// UnitmanageRepository
	/// </summary>
    public class UnitmanageRepository : BaseRepository<UnitmanageEntity>, IUnitmanageRepository
    {
        public UnitmanageRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}