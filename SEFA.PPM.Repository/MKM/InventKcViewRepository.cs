using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// InventKcViewRepository
	/// </summary>
    public class InventKcViewRepository : BaseRepository<InventKcViewEntity>, IInventKcViewRepository
    {
        public InventKcViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}