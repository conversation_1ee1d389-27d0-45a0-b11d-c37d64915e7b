using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// PackLastOverTimeViewRepository
	/// </summary>
    public class PackLastOverTimeViewRepository : BaseRepository<PackLastOverTimeViewEntity>, IPackLastOverTimeViewRepository
    {
        public PackLastOverTimeViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}