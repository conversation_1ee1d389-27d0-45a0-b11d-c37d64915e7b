using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// PackContainerViewRepository
	/// </summary>
    public class PackContainerViewRepository : BaseRepository<PackContainerViewEntity>, IPackContainerViewRepository
    {
        public PackContainerViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}