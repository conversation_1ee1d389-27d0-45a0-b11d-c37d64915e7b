using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// PackSpecIiViewRepository
	/// </summary>
    public class PackSpecIiViewRepository : BaseRepository<PackSpecIiViewEntity>, IPackSpecIiViewRepository
    {
        public PackSpecIiViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}