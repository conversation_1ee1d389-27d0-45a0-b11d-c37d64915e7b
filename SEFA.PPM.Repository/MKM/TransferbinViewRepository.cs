using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// TransferbinViewRepository
	/// </summary>
    public class TransferbinViewRepository : BaseRepository<TransferbinViewEntity>, ITransferbinViewRepository
    {
        public TransferbinViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}