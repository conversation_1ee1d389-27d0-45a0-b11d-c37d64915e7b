using SEFA.MKM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.MKM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.MKM.Repository
{
	/// <summary>
	/// BpalletDetailViewRepository
	/// </summary>
    public class BpalletDetailViewRepository : BaseRepository<BpalletDetailViewEntity>, IBpalletDetailViewRepository
    {
        public BpalletDetailViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}