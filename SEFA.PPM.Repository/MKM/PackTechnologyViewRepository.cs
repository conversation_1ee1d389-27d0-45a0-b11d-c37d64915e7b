using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// PackTechnologyViewRepository
	/// </summary>
    public class PackTechnologyViewRepository : BaseRepository<PackTechnologyViewEntity>, IPackTechnologyViewRepository
    {
        public PackTechnologyViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}