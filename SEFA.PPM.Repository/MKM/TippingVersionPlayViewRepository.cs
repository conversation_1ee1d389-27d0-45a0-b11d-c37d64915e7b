using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// TippingVersionPlayViewRepository
	/// </summary>
    public class TippingVersionPlayViewRepository : BaseRepository<TippingVersionPlayViewEntity>, ITippingVersionPlayViewRepository
    {
        public TippingVersionPlayViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}