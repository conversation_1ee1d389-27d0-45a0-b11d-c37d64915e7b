using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// CookEquipmentViewRepository
	/// </summary>
    public class CookEquipmentViewRepository : BaseRepository<CookEquipmentViewEntity>, ICookEquipmentViewRepository
    {
        public CookEquipmentViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}