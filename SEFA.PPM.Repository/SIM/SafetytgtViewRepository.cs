using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// SafetytgtViewRepository
	/// </summary>
    public class SafetytgtViewRepository : BaseRepository<SafetytgtViewEntity>, ISafetytgtViewRepository
    {
        public SafetytgtViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}