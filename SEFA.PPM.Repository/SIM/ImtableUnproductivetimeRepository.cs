using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// ImtableUnproductivetimeRepository
	/// </summary>
    public class ImtableUnproductivetimeRepository : BaseRepository<ImtableUnproductivetimeEntity>, IImtableUnproductivetimeRepository
    {
        public ImtableUnproductivetimeRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}