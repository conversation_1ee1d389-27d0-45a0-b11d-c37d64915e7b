using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// ImtableDowntimerateRepository
	/// </summary>
    public class ImtableDowntimerateRepository : BaseRepository<ImtableDowntimerateEntity>, IImtableDowntimerateRepository
    {
        public ImtableDowntimerateRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}