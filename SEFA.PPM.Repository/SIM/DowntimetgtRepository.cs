using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// DowntimetgtRepository
	/// </summary>
    public class DowntimetgtRepository : BaseRepository<DowntimetgtEntity>, IDowntimetgtRepository
    {
        public DowntimetgtRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}