using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// LogsheetDetailRepository
	/// </summary>
    public class LogsheetDetailRepository : BaseRepository<LogsheetDetailEntity>, ILogsheetDetailRepository
    {
        public LogsheetDetailRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}