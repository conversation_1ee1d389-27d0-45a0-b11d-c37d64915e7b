using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// BatchConsumeRequirementRepository
	/// </summary>
    public class BatchConsumeRequirementRepository : BaseRepository<BatchConsumeRequirementEntity>, IBatchConsumeRequirementRepository
    {
        public BatchConsumeRequirementRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}