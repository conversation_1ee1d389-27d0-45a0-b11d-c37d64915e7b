using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// BBatchListViewRepository
	/// </summary>
    public class BBatchListViewRepository : BaseRepository<BBatchListViewEntity>, IBBatchListViewRepository
    {
        public BBatchListViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}