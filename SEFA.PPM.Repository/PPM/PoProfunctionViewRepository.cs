using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// PoProfunctionViewRepository
	/// </summary>
    public class PoProfunctionViewRepository : BaseRepository<PoProfunctionViewEntity>, IPoProfunctionViewRepository
    {
        public PoProfunctionViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}