using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// UnproductiveSearchViewRepository
	/// </summary>
    public class UnproductiveSearchViewRepository : BaseRepository<UnproductiveSearchViewEntity>, IUnproductiveSearchViewRepository
    {
        public UnproductiveSearchViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}