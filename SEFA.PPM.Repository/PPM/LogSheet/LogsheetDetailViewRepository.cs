using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// LogsheetDetailViewRepository
	/// </summary>
    public class LogsheetDetailViewRepository : BaseRepository<LogsheetDetailViewEntity>, ILogsheetDetailViewRepository
    {
        public LogsheetDetailViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}