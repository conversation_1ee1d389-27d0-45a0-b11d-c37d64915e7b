using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// BatchProducedRequirementRepository
	/// </summary>
    public class BatchProducedRequirementRepository : BaseRepository<BatchProducedRequirementEntity>, IBatchProducedRequirementRepository
    {
        public BatchProducedRequirementRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}