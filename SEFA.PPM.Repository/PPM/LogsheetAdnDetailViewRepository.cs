using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// LogsheetAdnDetailViewRepository
	/// </summary>
    public class LogsheetAdnDetailViewRepository : BaseRepository<LogsheetAdnDetailViewEntity>, ILogsheetAdnDetailViewRepository
    {
        public LogsheetAdnDetailViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}