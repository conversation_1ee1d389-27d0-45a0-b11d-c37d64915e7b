using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// PoProducedActualRepository
	/// </summary>
    public class PoProducedActualRepository : BaseRepository<PoProducedActualEntity>, IPoProducedActualRepository
    {
        public PoProducedActualRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}