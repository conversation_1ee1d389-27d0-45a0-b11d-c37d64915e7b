using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// ProductionOrderRepository
	/// </summary>
    public class ProductionOrderRepository : BaseRepository<ProductionOrderEntity>, IProductionOrderRepository
    {
        public ProductionOrderRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}