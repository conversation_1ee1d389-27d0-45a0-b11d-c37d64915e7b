using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IDcsOpStatusServices
	/// </summary>	
    public interface IDcsOpStatusServices :IBaseServices<DcsOpStatusEntity>
	{
		Task<PageModel<DcsOpStatusEntity>> GetPageList(DcsOpStatusRequestModel reqModel);

        Task<List<DcsOpStatusEntity>> GetList(DcsOpStatusRequestModel reqModel);

		Task<bool> SaveForm(DcsOpStatusEntity entity);
    }
}