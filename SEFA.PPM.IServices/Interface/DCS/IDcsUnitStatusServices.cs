using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IDcsUnitStatusServices
	/// </summary>	
    public interface IDcsUnitStatusServices :IBaseServices<DcsUnitStatusEntity>
	{
		Task<PageModel<DcsUnitStatusEntity>> GetPageList(DcsUnitStatusRequestModel reqModel);

        Task<List<DcsUnitStatusEntity>> GetList(DcsUnitStatusRequestModel reqModel);

		Task<bool> SaveForm(DcsUnitStatusEntity entity);
    }
}