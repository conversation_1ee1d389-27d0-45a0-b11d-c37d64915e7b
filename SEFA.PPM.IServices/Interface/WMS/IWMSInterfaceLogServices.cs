using System.Collections.Generic;
using System.Threading.Tasks;
using SEFA.Base.Model;
using SEFA.PPM.Model.Models.Interface.WMS;

namespace SEFA.PPM.IServices.Interface.WMS
{
    /// <summary>
    /// WMS接口调用日志服务接口
    /// </summary>
    public interface IWMSInterfaceLogServices
    {
        /// <summary>
        /// 记录接口调用日志
        /// </summary>
        /// <param name="logEntity">日志信息</param>
        /// <returns>是否成功</returns>
        Task<bool> AddAsync(WmsInterfaceLogEntity logEntity);

        /// <summary>
        /// 获取日志列表（分页）
        /// </summary>
        /// <param name="interfaceName">接口名称</param>
        /// <param name="isSuccess">是否成功</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>日志列表和总记录数</returns>
        Task<PageModel<WmsInterfaceLogEntity>> GetPageListAsync(
            string interfaceName, bool? isSuccess,
            string startTime, string endTime,
            int pageIndex, int pageSize);

        /// <summary>
        /// 根据ID获取日志详情
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns>日志信息</returns>
        Task<WmsInterfaceLogEntity> GetByIdAsync(string id);
    }
}