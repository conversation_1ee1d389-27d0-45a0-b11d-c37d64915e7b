using SEFA.Base.Model;
using SEFA.PPM.Model.ViewModels.PTM;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.PPM.IServices
{
    /// <summary>
    /// IAndonServices
    /// </summary>	
    public interface IDcsDemoServices
    {
        Task<MessageModel> SendBatch(List<SendBatchModel> list);

        Task<MessageModel> ChargeConfirm(ChargeConfirmModel model);

        Task<MessageModel> PotCharge(ChargeModel model);

        Task<MessageModel> SampleStartComplete(SampleModel model);

        Task<MessageModel> SampleResult(SampleResultModel model);





        Task<MessageModel> BatchStateChange(BatchStateModel model);

        Task<MessageModel> UnitStateChange(UnitStateModel model);

        Task<MessageModel> OperationStateChange(OperationStateModel model);

        Task<MessageModel> ChargeRequest(ChargeModel model);

        Task<MessageModel> ChargeComplete(ChargeModel model);

        Task<MessageModel> StorageTankMaterialCharge(StorageTankChargeModel model);

        Task<MessageModel> MaterialTransfer(MaterialTransferModel model);

        Task<MessageModel> SampleRequest(SampleRequestModel model);

    }

}