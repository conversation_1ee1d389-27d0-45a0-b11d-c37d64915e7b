using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base.Common.Common;
using SEFA.DFM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.SIM.View;

namespace SEFA.PPM.IServices
{
    /// <summary>
    /// ILosstgtServicesPakMatGroup
    /// </summary>	
    public interface ILosstgtServicesPakMatGroup : IBaseServices<LosstgtEntity>
	{
		Task<PageModel<LosstgtEntity>> GetPageList(LosstgtRequestModel reqModel);

        Task<List<LosstgtGroupModel>> GetList(LosstgtRequestModel reqModel);

		Task<bool> SaveForm(LosstgtEntity entity);
		Task<ResultString> ImportData([FromForm] FileImportDto input);
        Task<List<string>> GetItem();
        Task<List<string>> GetItemGroup();
    }
}