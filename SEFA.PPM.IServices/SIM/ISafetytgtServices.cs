using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.MKM.View;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// ISafetytgtServices
	/// </summary>	
    public interface ISafetytgtServices :IBaseServices<SafetytgtEntity>
	{
		Task<PageModel<SafetytgtEntity>> GetPageList(SafetytgtRequestModel reqModel);

        Task<List<SafetytgtEntity>> GetList(SafetytgtRequestModel reqModel);
        //Task<List<object>> GetEventType();
        Task<bool> SaveForm(SafetytgtEntity entity);
        Task<bool> CheckModelRef(SafetytgtEntity entity);

        Task<SafetMsgModel> GetSafetMsg(SafetytgtRequestModel reqModel);
    }
}