using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.SIM.View;
using System;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IOeeReportViewServices
	/// </summary>	
    public interface IOeeReportViewServices :IBaseServices<OeeReportViewEntity>
	{
		Task<PageModel<OeeReportViewEntity>> GetPageList(OeeReportViewRequestModel reqModel);

        Task<List<OeeReportViewEntity>> GetList(OeeReportViewRequestModel reqModel);

		Task<bool> SaveForm(OeeReportViewEntity entity);
		Task<List<OEEDateMode>> GetOeeDate(OeeReportViewRequestModel reqModel);
    }
}