using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels.PTM;
using System;

namespace SEFA.PPM.IServices.PTM
{
	/// <summary>
	/// ICookConfirmationServices
	/// </summary>	
	public interface ICookConfirmationServices : IBaseServices<CookConfirmationEntity>
	{
		Task<PageModel<CookConfirmationEntity>> GetPageList(CookConfirmationRequestModel reqModel);

		Task<List<CookConfirmationEntity>> GetList(CookConfirmationRequestModel reqModel);

		Task<bool> SaveForm(CookConfirmationEntity entity);

		Task<MessageModel<string>> AddTime(string poId, int type, decimal duration = 0, DateTime? startTime = null);
	}
}