using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IConfirmationServices
	/// </summary>	
    public interface IConfirmationServices :IBaseServices<ConfirmationEntity>
	{
		Task<PageModel<ConfirmationEntity>> GetPageList(ConfirmationRequestModel reqModel);

        Task<List<ConfirmationEntity>> GetList(ConfirmationRequestModel reqModel);

		Task<bool> SaveForm(ConfirmationEntity entity);
		Task<ConfirmationEntity> GetEntityByOrderId(string OrderId);
		Task<ConfirmationEntityModel> GetEntity2(string Id);
		Task<bool> UpdateData(ConfirmationEntityModel reqModel);
	}
}