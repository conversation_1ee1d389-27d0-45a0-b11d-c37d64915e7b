using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels.PTM;

namespace SEFA.PPM.IServices.PTM
{
    /// <summary>
    /// IDowntimeHistoryServices
    /// </summary>	
    public interface IDowntimeHistoryServices : IBaseServices<DowntimeHistoryEntity>
    {
        Task<PageModel<DowntimeHistoryEntity>> GetPageList(DowntimeHistoryRequestModel reqModel);

        Task<List<DowntimeHistoryEntity>> GetList(DowntimeHistoryRequestModel reqModel);

        Task<bool> SaveForm(DowntimeHistoryEntity entity);
    }
}