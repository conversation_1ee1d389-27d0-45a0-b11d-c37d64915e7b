using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels.PTM;
using SEFA.PTM.Model.ViewModels;
using Microsoft.AspNetCore.Mvc;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{
	/// <summary>
	/// ITippingPrecheckViewServices
	/// </summary>	
	public interface ITippingPrecheckViewServices : IBaseServices<TippingPrecheckViewEntity>
	{
		Task<PageModel<TippingPrecheckViewEntity>> GetPageList(TippingPrecheckViewRequestModel reqModel);

		Task<List<TippingPrecheckViewEntity>> GetList(TippingPrecheckViewRequestModel reqModel);

       

        Task<MessageModel<string>> GetCount(TippingPrecheckViewRequestModel reqModel);

		Task<bool> SaveForm(TippingPrecheckViewEntity entity);

		Task<MessageModel<string>> Precheck(ConsolPoRequestModel reqModel);

		Task<MessageModel<string>> ScanTraceCode(string containerCode);

	}
}