using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices.PTM
{
    /// <summary>
    /// ISampleListVServices
    /// </summary>	
    public interface ISampleListVServices : IBaseServices<SampleListVEntity>
    {
        Task<PageModel<SampleListVModel>> GetPageList(SampleListVRequestModel reqModel);

        Task<List<SampleListVEntity>> GetList(SampleListVRequestModel reqModel);

        Task<bool> SaveForm(SampleListVEntity entity);
    }
}