using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IMReasonMappingViewServices
	/// </summary>	
    public interface IMReasonMappingViewServices :IBaseServices<MReasonMappingViewEntity>
	{
		Task<PageModel<MReasonMappingViewEntity>> GetPageList(MReasonMappingViewRequestModel reqModel);

        Task<List<MReasonMappingViewEntity>> GetList(MReasonMappingViewRequestModel reqModel);

		Task<bool> SaveForm(MReasonMappingViewEntity entity);
    }
}