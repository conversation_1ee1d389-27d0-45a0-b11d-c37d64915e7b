using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels.PTM;
using SEFA.PPM.Model.Models;
using SEFA.DFM.Model.Models;

namespace SEFA.PPM.IServices.PTM
{
	/// <summary>
	/// ISampleListServices
	/// </summary>	
	public interface ISampleListServices : IBaseServices<SampleListEntity>
	{
		Task<PageModel<SampleListEntity>> GetPageList(SampleListRequestModel reqModel);

		Task<List<SampleListEntity>> GetList(SampleListRequestModel reqModel);

		Task<bool> SaveForm(SampleListEntity entity);
		Task<MessageModel<EquipmentAndOrderEntity>> ScanSampleEquipment(string sampleEquipment);
		Task<MessageModel<SampleListEntity>> ScanContainerCode(string containerCode);
		Task<MessageModel<string>> UpdateStatus(SampleListEntity reqModel);
		Task<MessageModel<EquipmentEntity>> GetEquipmentFromSampleEquipment(string sampleEquipment);
		Task<MessageModel<PoProducedExecutionEntity>> GetRunOrder(string equipmentId);
		Task<MessageModel<LogsheetEntity>> CheckLogSheet(EquipmentAndOrderEntity reqModel);
		Task<MessageModel<string>> SaveSampling(EquipmentAndOrderEntity reqModel);
		Task<MessageModel<string>> StartDetection(string[] ids);
		Task<MessageModel<string>> AddSampling();
	}
}