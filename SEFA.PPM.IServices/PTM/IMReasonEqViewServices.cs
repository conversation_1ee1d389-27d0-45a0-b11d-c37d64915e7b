using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IMReasonEqViewServices
	/// </summary>	
    public interface IMReasonEqViewServices :IBaseServices<MReasonEqViewEntity>
	{
		Task<PageModel<MReasonEqViewEntity>> GetPageList(MReasonEqViewRequestModel reqModel);

        Task<List<MReasonEqViewEntity>> GetList(MReasonEqViewRequestModel reqModel);

		Task<bool> SaveForm(MReasonEqViewEntity entity);
    }
}