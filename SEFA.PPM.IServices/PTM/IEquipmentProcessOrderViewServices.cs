using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PTM.Model.Models;
using SEFA.PTM.Model.ViewModels;
using SEFA.PPM.Model.Models;

namespace SEFA.PTM.IServices
{	
	/// <summary>
	/// IEquipmentProcessOrderViewServices
	/// </summary>	
    public interface IEquipmentProcessOrderViewServices :IBaseServices<EquipmentProcessOrderViewEntity>
	{
		Task<PageModel<EquipmentProcessOrderViewEntity>> GetPageList(EquipmentProcessOrderViewRequestModel reqModel);

        Task<List<EquipmentProcessOrderViewEntity>> GetList(EquipmentProcessOrderViewRequestModel reqModel);

		Task<bool> SaveForm(EquipmentProcessOrderViewEntity entity);
    }
}