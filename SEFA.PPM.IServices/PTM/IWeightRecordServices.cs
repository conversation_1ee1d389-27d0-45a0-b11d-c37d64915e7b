using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels.PTM;

namespace SEFA.PPM.IServices.PTM
{
    /// <summary>
    /// IWeightRecordServices
    /// </summary>	
    public interface IWeightRecordServices : IBaseServices<WeightRecordEntity>
    {
        Task<PageModel<WeightRecordEntity>> GetPageList(WeightRecordRequestModel reqModel);

        Task<List<WeightRecordEntity>> GetList(WeightRecordRequestModel reqModel);

        Task<bool> SaveForm(WeightRecordEntity entity);
    }
}