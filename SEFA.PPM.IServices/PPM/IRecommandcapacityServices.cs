using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IRecommandcapacityServices
	/// </summary>	
    public interface IRecommandcapacityServices :IBaseServices<RecommandcapacityEntity>
	{
		Task<PageModel<RecommandcapacityEntity>> GetPageList(RecommandcapacityRequestModel reqModel);

        Task<List<RecommandcapacityEntity>> GetList(RecommandcapacityRequestModel reqModel);

		Task<bool> SaveForm(RecommandcapacityEntity entity);
    }
}