using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IBatchConsumeRequirementServices
	/// </summary>	
    public interface IBatchConsumeRequirementServices :IBaseServices<BatchConsumeRequirementEntity>
	{
		Task<PageModel<BatchConsumeRequirementEntity>> GetPageList(BatchConsumeRequirementRequestModel reqModel);

        Task<List<BatchConsumeRequirementEntity>> GetList(BatchConsumeRequirementRequestModel reqModel);
        //Task<List<BatchConsumeRequirementRequestModel>> GetConsumeMaterialList (BatchConsumeRequirementRequestModel reqModel);
        Task<bool> SaveForm(BatchConsumeRequirementEntity entity);
    }
}