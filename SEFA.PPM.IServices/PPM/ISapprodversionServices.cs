using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// ISapprodversionServices
	/// </summary>	
    public interface ISapprodversionServices :IBaseServices<SapprodversionEntity>
	{
		Task<PageModel<SapprodversionEntity>> GetPageList(SapprodversionRequestModel reqModel);

        Task<List<SapprodversionEntity>> GetList(SapprodversionRequestModel reqModel);

		Task<bool> SaveForm(SapprodversionEntity entity);
		Task<MessageModel<string>> GetSapProdVersion(string factory);
        Task<MessageModel<string>> GetSapProdVersionAsync(string factory);
    }
}