using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.PPM.IServices
{
    /// <summary>
    /// IWeekScheduleOrderNoServices
    /// </summary>	
    public interface IWeekScheduleOrderNoServices : IBaseServices<WeekScheduleOrderNoEntity>
    {
        Task<PageModel<WeekScheduleOrderNoEntity>> GetPageList(WeekScheduleOrderNoRequestModel reqModel);

        Task<List<WeekScheduleOrderNoEntity>> GetList(WeekScheduleOrderNoRequestModel reqModel);

        Task<bool> SaveForm(WeekScheduleOrderNoEntity entity);

        Task<string> AssignNumber(WeekScheduleOrderNoEntity entity);
    }
}