using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IProductionOrderPropertyServices
	/// </summary>	
    public interface IProductionOrderPropertyServices :IBaseServices<ProductionOrderPropertyEntity>
	{
		Task<PageModel<ProductionOrderPropertyEntity>> GetPageList(ProductionOrderPropertyRequestModel reqModel);

        Task<List<ProductionOrderPropertyEntity>> GetList(ProductionOrderPropertyRequestModel reqModel);

		Task<bool> SaveForm(ProductionOrderPropertyEntity entity);

		Task<List<ProductionOrderPropertyEntity>> GetProperty(ProductionOrderPropertyRequestModel reqModel);

	}
}