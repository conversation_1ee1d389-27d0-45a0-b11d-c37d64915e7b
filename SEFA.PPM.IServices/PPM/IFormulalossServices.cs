using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IFormulalossServices
	/// </summary>	
    public interface IFormulalossServices :IBaseServices<FormulalossEntity>
	{
		Task<PageModel<FormulalossEntity>> GetPageList(FormulalossRequestModel reqModel);

        Task<List<FormulalossEntity>> GetList(FormulalossRequestModel reqModel);

		Task<bool> SaveForm(FormulalossEntity entity);
    }
}