using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base.Common.Common;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IThroatadditionServices
	/// </summary>	
    public interface IThroatadditionServices :IBaseServices<ThroatadditionEntity>
	{
		Task<PageModel<ThroatadditionEntity>> GetPageList(ThroatadditionRequestModel reqModel);

        Task<List<ThroatadditionEntity>> GetList(ThroatadditionRequestModel reqModel);

		Task<bool> SaveForm(ThroatadditionEntity entity);
        Task<ResultString> ImportData( FileImportDto input);
    }
}