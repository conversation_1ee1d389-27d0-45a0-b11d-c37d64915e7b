using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.PPM;

namespace SEFA.PPM.IServices
{
    /// <summary>
    /// ILogsheetHistoryListViewServices
    /// </summary>	
    public interface ILogsheetHistoryListViewServices :IBaseServices<LogsheetHistoryListViewRequireEntity>
	{
		Task<PageModel<LogsheetHistoryListViewRequireEntity>> GetPageList(LogsheetHistoryListViewRequestModel reqModel);

        Task<List<LogsheetHistoryListViewRequireEntity>> GetList(LogsheetHistoryListViewRequestModel reqModel);

		Task<bool> SaveForm(LogsheetHistoryListViewRequireEntity entity);
    }
}