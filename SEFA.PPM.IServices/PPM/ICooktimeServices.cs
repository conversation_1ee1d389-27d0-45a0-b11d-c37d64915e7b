using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// ICooktimeServices
	/// </summary>	
    public interface ICooktimeServices :IBaseServices<CooktimeEntity>
	{
		Task<PageModel<CooktimeModel>> GetPageList(CooktimeRequestModel reqModel);

        Task<List<CooktimeModel>> GetList(CooktimeRequestModel reqModel);

		Task<bool> SaveForm(CooktimeEntity entity);
    }
}