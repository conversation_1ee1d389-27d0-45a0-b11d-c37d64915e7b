using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IBclblDetailViewServices
	/// </summary>	
    public interface IBclblDetailViewServices :IBaseServices<BclblDetailViewEntity>
	{
		Task<PageModel<BclblDetailViewEntity>> GetPageList(BclblDetailViewRequestModel reqModel);

        Task<List<BclblDetailViewEntity>> GetList(BclblDetailViewRequestModel reqModel);

		Task<bool> SaveForm(BclblDetailViewEntity entity);
    }
}