using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.MKM.View;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IMpreDayViewServices
	/// </summary>	
    public interface IMpreDayViewServices :IBaseServices<MpreDayViewEntity>
	{
        Task<List<MpreDayViewEntity>> GetList(MpreDayViewRequestModel reqModel);
        Task<List<MpreDayModels>> Preprogress(MpreDayViewRequestModel reqModel);

        Task<bool> SaveForm(MpreDayViewEntity entity);
    }
}