using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.Models;

namespace SEFA.PPM.IServices
{
    /// <summary>
    /// IBoardNumberViewServices
    /// </summary>	
    public interface IBoardNumberViewServices :IBaseServices<BoardNumberViewEntity>
	{
		Task<PageModel<BoardNumberViewEntity>> GetPageList(BoardNumberViewRequestModel reqModel);

        Task<List<BoardNumberViewEntity>> GetList(BoardNumberViewRequestModel reqModel);

		Task<bool> SaveForm(BoardNumberViewEntity entity);
    }
}