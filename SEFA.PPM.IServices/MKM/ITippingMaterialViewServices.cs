using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// ITippingMaterialViewServices
	/// </summary>	
    public interface ITippingMaterialViewServices :IBaseServices<TippingMaterialViewEntity>
	{
		Task<PageModel<TippingMaterialViewEntity>> GetPageList(TippingMaterialViewRequestModel reqModel);

        Task<List<TippingMaterialViewEntity>> GetList(TippingMaterialViewRequestModel reqModel);

		Task<bool> SaveForm(TippingMaterialViewEntity entity);
    }
}