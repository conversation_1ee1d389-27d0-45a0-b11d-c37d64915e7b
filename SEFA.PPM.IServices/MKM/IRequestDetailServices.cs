using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IRequestDetailServices
	/// </summary>	
    public interface IRequestDetailServices :IBaseServices<RequestDetailEntity>
	{
		Task<PageModel<RequestDetailEntity>> GetPageList(RequestDetailRequestModel reqModel);

        Task<List<RequestDetailEntity>> GetList(RequestDetailRequestModel reqModel);

		Task<bool> SaveForm(RequestDetailEntity entity);
    }
}