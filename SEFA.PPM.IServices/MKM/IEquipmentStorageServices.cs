using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;

namespace SEFA.MKM.IServices
{	
	/// <summary>
	/// IEquipmentStorageServices
	/// </summary>	
    public interface IEquipmentStorageServices :IBaseServices<EquipmentStorageEntity>
	{
		Task<PageModel<EquipmentStorageEntity>> GetPageList(EquipmentStorageRequestModel reqModel);

        Task<List<EquipmentStorageEntity>> GetList(EquipmentStorageRequestModel reqModel);

		Task<bool> SaveForm(EquipmentStorageEntity entity);
    }
}