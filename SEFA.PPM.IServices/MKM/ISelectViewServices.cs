using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// ISelectViewServices
	/// </summary>	
    public interface ISelectViewServices :IBaseServices<SelectViewEntity>
	{
		Task<PageModel<SelectViewEntity>> GetPageList(SelectViewRequestModel reqModel);

        Task<List<SelectViewEntity>> GetList(SelectViewRequestModel reqModel);

		Task<bool> SaveForm(SelectViewEntity entity);
    }
}