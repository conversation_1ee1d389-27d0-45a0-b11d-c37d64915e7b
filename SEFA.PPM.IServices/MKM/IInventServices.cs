using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IInventServices
	/// </summary>	
    public interface IInventServices :IBaseServices<InventEntity>
	{
		Task<PageModel<InventEntity>> GetPageList(InventRequestModel reqModel);

        Task<List<InventEntity>> GetList(InventRequestModel reqModel);

		Task<bool> SaveForm(InventEntity entity);

		 Task<bool> SaveMData();
    }
}