using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.MKM.View;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IPackIViewServices
	/// </summary>	
    public interface IPackIViewServices :IBaseServices<PackIViewEntity>
	{
		Task<PageModel<PackIViewEntity>> GetPageList(PackIViewRequestModel reqModel);

        Task<List<PackIViewEntity>> GetList(PackIViewRequestModel reqModel);

		Task<bool> SaveForm(PackIViewEntity entity);
		Task<AnnualOutModel> AnnualOutPut(PackIViewRequestModel reqModel);
		Task<AnnualOutModel> CertainYearMonth(PackFebruaryViewRequestModel reqModel);
		Task<EveryMonthModel> GetEveryMonth(PackEveryMonthViewRequestModel reqModel);
		Task<LineOutPutTonModel> LineOutPutTon(PackFebruaryViewRequestModel reqModel);
		Task<SpecOutPutModel> GetSpecOutPut(PackSpecIiViewRequestModel reqModel);
		Task<PackSpecModels> PackSpecOutPut(PackSpecIiViewRequestModel reqModel);
		Task<FormulaTypeModel> FormulaType(PackFormulaViewRequestModel reqModel);
		Task<List<DifferentWaysModel>> DifferentWays(PackRecipeViewRequestModel reqModel);
    }
}