using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;

namespace SEFA.MKM.IServices
{	
	/// <summary>
	/// IMMaterialPropertyViewServices
	/// </summary>	
    public interface IMMaterialPropertyViewServices :IBaseServices<MMaterialPropertyViewEntity>
	{
		Task<PageModel<MMaterialPropertyViewEntity>> GetPageList(MMaterialPropertyViewRequestModel reqModel);

        Task<List<MMaterialPropertyViewEntity>> GetList(MMaterialPropertyViewRequestModel reqModel);

		Task<bool> SaveForm(MMaterialPropertyViewEntity entity);
    }
}