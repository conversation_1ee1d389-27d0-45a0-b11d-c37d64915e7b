using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// ITippingVersionPlayViewServices
	/// </summary>	
    public interface ITippingVersionPlayViewServices :IBaseServices<TippingVersionPlayViewEntity>
	{
		Task<PageModel<TippingVersionPlayViewEntity>> GetPageList(TippingVersionPlayViewRequestModel reqModel);

        Task<List<TippingVersionPlayViewEntity>> GetList(TippingVersionPlayViewRequestModel reqModel);

		Task<bool> SaveForm(TippingVersionPlayViewEntity entity);
    }
}