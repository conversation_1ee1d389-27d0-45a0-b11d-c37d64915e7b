using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;

namespace SEFA.MKM.IServices
{	
	/// <summary>
	/// IBatchPalletSelectmViewServices
	/// </summary>	
    public interface IBatchPalletSelectmViewServices :IBaseServices<BatchPalletSelectmViewEntity>
	{
		Task<PageModel<BatchPalletSelectmViewEntity>> GetPageList(BatchPalletSelectmViewRequestModel reqModel);

        Task<List<BatchPalletSelectmViewEntity>> GetList(BatchPalletSelectmViewRequestModel reqModel);

		Task<bool> SaveForm(BatchPalletSelectmViewEntity entity);
    }
}