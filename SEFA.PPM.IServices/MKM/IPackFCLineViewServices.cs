using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IPackFCLineViewServices
	/// </summary>	
    public interface IPackFCLineViewServices :IBaseServices<PackFCLineViewEntity>
	{
		Task<PageModel<PackFCLineViewEntity>> GetPageList(PackFCLineViewRequestModel reqModel);

        Task<List<PackFCLineViewEntity>> GetList(PackFCLineViewRequestModel reqModel);

		Task<bool> SaveForm(PackFCLineViewEntity entity);
    }
}