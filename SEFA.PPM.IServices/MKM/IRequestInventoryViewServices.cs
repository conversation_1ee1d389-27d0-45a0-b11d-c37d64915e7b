using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.MKM.Model.ViewModels.View;
using SEFA.PPM.Model.ViewModels.MKM.View;
using SEFA.PPM.Model.ViewModels.MKM.InterfaceView;
using SEFA.Base.ESB;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IRequestInventoryViewServices
	/// </summary>	
    public interface IRequestInventoryViewServices :IBaseServices<RequestInventoryViewEntity>
	{
		Task<PageModel<RequestInventoryViewEntity>> GetPageList(RequestInventoryViewRequestModel reqModel);

        Task<List<RequestInventoryViewEntity>> GetList(RequestInventoryViewRequestModel reqModel);

		Task<bool> SaveForm(RequestInventoryViewEntity entity);

		 Task<MessageModel<string>> RequestMaterials(RequestMaterialsModel reqModel);
		Task<MessageModel<string>> updateStste(RequestMaterialsModel reqModel);

		Task<MessageModel<string>> AddRequestInventory(AddRequestInventory reqModel);

		Task<MessageModel<string>> JITRequestMaterials(JITRequestModel reqModel);
		Task<MessageModel<string>> SugarRequestMaterials(RequestMaterialsModel reqModel);

		 Task<MessageModel<string>> InterFaceTest();

		Task<LKKESBResponse<PackingSizeResult>> GePacktResult(PackingSizeSend model);

        /// <summary>
        /// 调用SAP转移接口
        /// </summary>
        /// <param name="sapModel"></param>
        /// <returns></returns>
        List<ReturnStockTran> Transfer(SapTransfer sapModel);

		/// <summary>
		/// 获取标签（同步）
		/// </summary>
		/// <param name="model"></param>
		/// <returns></returns>
		LKKESBResponse<ScanRootResult> GetWmsSSCC(ScanSend model);

        /// <summary>
        /// 打印标签同步
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<LKKESBResponse<PrintResult>> PrintLabelSynchro(List<PrintSendDataItems> model);
        Task<List<MCodeReturnModel>> SearchRequestInsertData(AddRequestInventory reqModel);
        Task<List<MCodeReturnModel>> SearchPULLInsertData(AddRequestInventory reqModel);
        Task<MessageModel<string>> AddRequestInven(List<MCodeReturnModel> reqModel);

        Task<MessageModel<string>> AddPULLInven(List<MCodeReturnModel> reqModel);
        
        #region 请料服务
        Task<MessageModel<string>> FixedTimeMPull_13();

		Task<MessageModel<string>> FixedTimeMRequest_8();

		Task<MessageModel<string>> FixedTimeMRequest_14();

		Task<MessageModel<string>> FixedTimeRawMPull_02();

		Task<MessageModel<string>> FixedTimeMRequest_0();

		Task<MessageModel<string>> FixedTimeMRequest_12();

        #endregion

    }
}