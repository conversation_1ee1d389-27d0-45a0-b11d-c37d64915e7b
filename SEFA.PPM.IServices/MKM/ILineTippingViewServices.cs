using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// ILineTippingViewServices
	/// </summary>	
    public interface ILineTippingViewServices :IBaseServices<LineTippingViewEntity>
	{
		Task<PageModel<LineTippingViewEntity>> GetPageList(LineTippingViewRequestModel reqModel);

        Task<List<LineTippingViewEntity>> GetList(LineTippingViewRequestModel reqModel);

		Task<bool> SaveForm(LineTippingViewEntity entity);
    }
}