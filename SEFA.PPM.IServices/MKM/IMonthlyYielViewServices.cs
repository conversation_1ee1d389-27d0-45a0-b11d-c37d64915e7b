using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IMonthlyYielViewServices
	/// </summary>	
    public interface IMonthlyYielViewServices :IBaseServices<MonthlyYielViewEntity>
	{
		Task<PageModel<MonthlyYielViewEntity>> GetPageList(MonthlyYielViewRequestModel reqModel);

        Task<List<MonthlyYielViewEntity>> GetList(MonthlyYielViewRequestModel reqModel);

		Task<bool> SaveForm(MonthlyYielViewEntity entity);
    }
}