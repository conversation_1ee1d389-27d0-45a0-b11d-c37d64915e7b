using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.MKM.View;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// ICookRecipeViewServices
	/// </summary>	
    public interface ICookRecipeViewServices :IBaseServices<CookRecipeViewEntity>
	{
		Task<PageModel<CookRecipeViewEntity>> GetPageList(CookRecipeViewRequestModel reqModel);

        Task<List<CookRecipeViewEntity>> GetList(CookRecipeViewRequestModel reqModel);

		Task<bool> SaveForm(CookRecipeViewEntity entity);
        /*Task<List<CookRecipeModel>> GetRecipe();*/
        //Task<List<CoodEquipmentModel>> GetEquipmentList();
        Task<MessageModel<List<CookRecipeModel>>> GetSchedule();
    }
}