using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.MKM.Model.ViewModels.View;
using SEFA.MKM.Model.Models.MKM;

namespace SEFA.MKM.IServices
{	
	/// <summary>
	/// IBatchPalletViewServices
	/// </summary>	
    public interface IBatchPalletViewServices :IBaseServices<BatchPalletViewEntity>
	{
		Task<PageModel<BatchPalletViewEntity>> GetPageList(BatchPalletModel reqModel);

        Task<List<BatchPalletViewEntity>> GetList(BatchPalletViewRequestModel reqModel);

		Task<bool> SaveForm(BatchPalletViewEntity entity);


		Task<List<DicBinBpalletEntity>> QueryBin();
		 Task<List<Select>> QueryBinS();

        Task<List<Select>> GetMachine(BatchPalletModel reqModel);
    }
}