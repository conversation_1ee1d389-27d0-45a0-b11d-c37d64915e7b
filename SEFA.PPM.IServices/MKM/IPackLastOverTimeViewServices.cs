using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IPackLastOverTimeViewServices
	/// </summary>	
    public interface IPackLastOverTimeViewServices :IBaseServices<PackLastOverTimeViewEntity>
	{
		Task<PageModel<PackLastOverTimeViewEntity>> GetPageList(PackLastOverTimeViewRequestModel reqModel);

        Task<List<PackLastOverTimeViewEntity>> GetList(PackLastOverTimeViewRequestModel reqModel);

		Task<bool> SaveForm(PackLastOverTimeViewEntity entity);
    }
}