
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.PPM.Services
{
    public class TippingVersionPlayViewServices : BaseServices<TippingVersionPlayViewEntity>, ITippingVersionPlayViewServices
    {
        private readonly IBaseRepository<TippingVersionPlayViewEntity> _dal;
        public TippingVersionPlayViewServices(IBaseRepository<TippingVersionPlayViewEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<TippingVersionPlayViewEntity>> GetList(TippingVersionPlayViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<TippingVersionPlayViewEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<TippingVersionPlayViewEntity>> GetPageList(TippingVersionPlayViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<TippingVersionPlayViewEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

        public async Task<bool> SaveForm(TippingVersionPlayViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}