using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using SEFA.Base.Services.BASE;
using SEFA.MKM.Model.Models;
using SEFA.PPM.Model.Models.Interface.WMS;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Model.BASE;
using SEFA.Base.Common.Helper;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.IRepository.Base;
using SEFA.DFM.Model.Models;
using SEFA.MKM.IRepository;
using SEFA.PPM.IRepository;
using SEFA.PPM.IServices.MKM;
using SEFA.PPM.Repository.Interface.WMS;
using EquipmentEntity = SEFA.PPM.Model.Models.MKM.EquipmentEntity;

namespace SEFA.PPM.Services.MKM
{
    /// <summary>
    /// 优化版物料库存操作服务
    /// </summary>
    public class MaterialInventoryOperationServices : BaseServices<MaterialInventoryEntity>,
        IMaterialInventoryOperationServices
    {
        private readonly IMaterialInventoryRepository _materialInventoryRepository;
        private readonly IDistributionMaterialDetailRepository _distributionMaterialDetailRepository;
        private readonly IMaterialLotRepository _materialLotRepository;
        private readonly IMaterialSubLotRepository _materialSubLotRepository;
        private readonly IContainerRepository _containerRepository;
        private readonly IContainerHistoryRepository _containerHistoryRepository;
        private readonly IBaseRepository<UnitmanageEntity> _unitmanageRepository;
        private readonly IMaterialTransferRepository _materialTransferRepository;
        private readonly IEquipmentRepository _equipmentRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IUser _user;

        // 配置常量
        private static class Constants
        {
            public const string DefaultIsPrechecked = "0";
            public const string DefaultLotType = "Standard";
            public const string DefaultLotExternalStatus = "U";
            public const string DefaultSubLotType = "Standard";
            public const string DefaultSubLotExternalStatus = "U";
            public const string DefaultContainerStatus = "Active";
            public const string DefaultContainerClass = "Pallet";
            public const string TransferTypeCreateInventory = "Create Inventory";
            public const string TransferStatusCompleted = "Completed";
            public const string ContainerHistoryTypeInstock = "Instock";
            public const string TransferComment = "线边仓入库创建库存";
            public const string ContainerHistoryComment = "线边仓入库操作";
            public const int DefaultExpirationYears = 1;
        }

        public MaterialInventoryOperationServices(
            IMaterialInventoryRepository materialInventoryRepository,
            IDistributionMaterialDetailRepository distributionMaterialDetailRepository,
            IMaterialLotRepository materialLotRepository,
            IMaterialSubLotRepository materialSubLotRepository,
            IContainerRepository containerRepository,
            IContainerHistoryRepository containerHistoryRepository,
            IBaseRepository<UnitmanageEntity> unitmanageRepository,
            IMaterialTransferRepository materialTransferRepository,
            IEquipmentRepository equipmentRepository,
            IUser user,
            IUnitOfWork unitOfWork)
        {
            _materialInventoryRepository = materialInventoryRepository;
            _distributionMaterialDetailRepository = distributionMaterialDetailRepository;
            _materialLotRepository = materialLotRepository;
            _materialSubLotRepository = materialSubLotRepository;
            _containerRepository = containerRepository;
            _containerHistoryRepository = containerHistoryRepository;
            _unitmanageRepository = unitmanageRepository;
            _materialTransferRepository = materialTransferRepository;
            _equipmentRepository = equipmentRepository;
            _user = user;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// PDA线边仓扫码确认入库 - 优化版本
        /// </summary>
        public async Task<MessageModel<string>> SideWarehousePutInStorage(
            DistributionMaterialRequest reqModel)
        {
            // 参数验证
            var validationResult = ValidateRequest(reqModel);
            if (!validationResult.success)
            {
                return validationResult;
            }

            _unitOfWork.BeginTran();

            try
            {
                // 批量预加载数据以减少数据库查询
                var preloadedData = await PreloadRequiredData(reqModel);

                var inventoriesToAdd = new List<MaterialInventoryEntity>();
                var transfersToAdd = new List<MaterialTransferEntity>();
                var lotsToAdd = new List<MaterialLotEntity>();
                var subLotsToAdd = new List<MaterialSubLotEntity>();
                var containersToAdd = new List<ContainerEntity>();
                var containerHistoriesToAdd = new List<ContainerHistoryEntity>();

                foreach (var detail in reqModel.DetailsList)
                {
                    // 处理批次、子批次、容器
                    var processResult = await ProcessDetailEntities(detail, preloadedData,
                        lotsToAdd, subLotsToAdd, containersToAdd, containerHistoriesToAdd);

                    // 创建库存记录
                    var inventory = CreateInventoryEntity(detail, processResult, preloadedData);
                    inventoriesToAdd.Add(inventory);

                    // 创建转移记录
                    var transfer = CreateTransferEntity(detail, processResult, preloadedData);
                    transfersToAdd.Add(transfer);
                }

                // 批量插入数据
                await BatchInsertEntities(lotsToAdd, subLotsToAdd, containersToAdd,
                    containerHistoriesToAdd, inventoriesToAdd, transfersToAdd);

                _unitOfWork.CommitTran();
                return MessageModel<string>.Success("入库操作成功完成");
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                SerilogServer.LogError(ex, $"线边仓入库操作失败: {ex.Message}");
                return MessageModel<string>.Fail($"入库操作失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证请求参数
        /// </summary>
        private static MessageModel<string> ValidateRequest(DistributionMaterialRequest reqModel)
        {
            if (reqModel == null)
                return MessageModel<string>.Fail("请求参数不能为空");

            if (reqModel.DetailsList == null || !reqModel.DetailsList.Any())
                return MessageModel<string>.Fail("请求明细不能为空");

            if (string.IsNullOrWhiteSpace(reqModel.LineWarehouseCode))
                return MessageModel<string>.Fail("线边仓编码不能为空");

            // 验证明细数据
            for (int i = 0; i < reqModel.DetailsList.Count; i++)
            {
                var detail = reqModel.DetailsList[i];
                if (string.IsNullOrWhiteSpace(detail.MaterialCode))
                    return MessageModel<string>.Fail($"第{i + 1}条明细的物料编码不能为空");

                if (detail.Quantity <= 0)
                    return MessageModel<string>.Fail($"第{i + 1}条明细的数量必须大于0");

                if (string.IsNullOrWhiteSpace(detail.BatchNo))
                    return MessageModel<string>.Fail($"第{i + 1}条明细的批次号不能为空");
            }

            return MessageModel<string>.Success("");
        }

        /// <summary>
        /// 预加载所需数据
        /// </summary>
        private async Task<PreloadedData> PreloadRequiredData(DistributionMaterialRequest reqModel)
        {
            var data = new PreloadedData();

            // 获取仓库信息
            if (!string.IsNullOrEmpty(reqModel.LineWarehouseCode))
            {
                data.Equipment = await _equipmentRepository.FindEntity(x =>
                    x.EquipmentCode == reqModel.LineWarehouseCode);
            }

            // 批量获取单位信息
            var unitNames = reqModel.DetailsList
                .Where(d => !string.IsNullOrEmpty(d.Unit))
                .Select(d => d.Unit)
                .Distinct()
                .ToList();

            if (unitNames.Any())
            {
                data.Units = await _unitmanageRepository.FindList(x => unitNames.Contains(x.Name));
            }

            // 批量获取已存在的批次
            var batchNos = reqModel.DetailsList
                .Where(d => !string.IsNullOrEmpty(d.BatchNo))
                .Select(d => d.BatchNo)
                .Distinct()
                .ToList();

            if (batchNos.Any())
            {
                data.ExistingLots = await _materialLotRepository.FindList(x => batchNos.Contains(x.LotId));
            }

            // 批量获取已存在的子批次
            var barCodes = reqModel.DetailsList
                .Where(d => !string.IsNullOrEmpty(d.BarCode))
                .Select(d => d.BarCode)
                .Distinct()
                .ToList();

            if (barCodes.Any())
            {
                data.ExistingSubLots = await _materialSubLotRepository.FindList(x => barCodes.Contains(x.SubLotId));
            }

            // 批量获取已存在的容器
            var palletNos = reqModel.DetailsList
                .Where(d => !string.IsNullOrEmpty(d.PalletNo))
                .Select(d => d.PalletNo)
                .Distinct()
                .ToList();

            if (palletNos.Any())
            {
                data.ExistingContainers = await _containerRepository.FindList(x => palletNos.Contains(x.Name));
            }

            return data;
        }

        /// <summary>
        /// 预加载的数据容器
        /// </summary>
        private class PreloadedData
        {
            public EquipmentEntity Equipment { get; set; }
            public List<UnitmanageEntity> Units { get; set; } = new List<UnitmanageEntity>();
            public List<MaterialLotEntity> ExistingLots { get; set; } = new List<MaterialLotEntity>();
            public List<MaterialSubLotEntity> ExistingSubLots { get; set; } = new List<MaterialSubLotEntity>();
            public List<ContainerEntity> ExistingContainers { get; set; } = new List<ContainerEntity>();
        }

        /// <summary>
        /// 处理结果
        /// </summary>
        private class ProcessResult
        {
            public string LotId { get; set; }
            public string SubLotId { get; set; }
            public string ContainerId { get; set; }
            public string QuantityUomId { get; set; }
        }

        /// <summary>
        /// 处理明细实体（批次、子批次、容器）
        /// </summary>
        private async Task<ProcessResult> ProcessDetailEntities(
            DistributionMaterialDetail detail,
            PreloadedData preloadedData,
            List<MaterialLotEntity> lotsToAdd,
            List<MaterialSubLotEntity> subLotsToAdd,
            List<ContainerEntity> containersToAdd,
            List<ContainerHistoryEntity> containerHistoriesToAdd)
        {
            var result = new ProcessResult();

            // 处理批次
            result.LotId = ProcessLot(detail, preloadedData, lotsToAdd);

            // 处理子批次
            result.SubLotId = ProcessSubLot(detail, preloadedData, subLotsToAdd);

            // 处理容器
            result.ContainerId = ProcessContainer(detail, preloadedData, containersToAdd, containerHistoriesToAdd);

            // 获取单位ID
            result.QuantityUomId = GetQuantityUomId(detail.Unit, preloadedData);

            return result;
        }

        /// <summary>
        /// 处理批次
        /// </summary>
        private string ProcessLot(DistributionMaterialDetail detail, PreloadedData preloadedData,
            List<MaterialLotEntity> lotsToAdd)
        {
            if (string.IsNullOrEmpty(detail.BatchNo))
                return null;

            // 检查是否已存在
            var existingLot = preloadedData.ExistingLots.FirstOrDefault(x => x.LotId == detail.BatchNo);
            if (existingLot != null)
                return existingLot.ID;

            // 检查是否已在待添加列表中
            var lotInList = lotsToAdd.FirstOrDefault(x => x.LotId == detail.BatchNo);
            if (lotInList != null)
                return lotInList.ID;

            // 创建新批次
            var newLot = new MaterialLotEntity
            {
                LotId = detail.BatchNo,
                MaterialId = detail.MaterialCode,
                MaterialVersionId = detail.MaterialVersionCode,
                ExpirationDate = DateTime.Now.AddYears(Constants.DefaultExpirationYears),
                ProductionDateLocal = DateTime.Now,
                Type = Constants.DefaultLotType,
                ExternalStatus = Constants.DefaultLotExternalStatus
            };

            newLot.CreateCustomGuid(_user.Name);
            lotsToAdd.Add(newLot);
            return newLot.ID;
        }

        /// <summary>
        /// 处理子批次
        /// </summary>
        private string ProcessSubLot(DistributionMaterialDetail detail, PreloadedData preloadedData,
            List<MaterialSubLotEntity> subLotsToAdd)
        {
            if (string.IsNullOrEmpty(detail.BarCode))
                return null;

            // 检查是否已存在
            var existingSubLot = preloadedData.ExistingSubLots.FirstOrDefault(x => x.SubLotId == detail.BarCode);
            if (existingSubLot != null)
                return existingSubLot.ID;

            // 检查是否已在待添加列表中
            var subLotInList = subLotsToAdd.FirstOrDefault(x => x.SubLotId == detail.BarCode);
            if (subLotInList != null)
                return subLotInList.ID;

            // 创建新子批次
            var newSubLot = new MaterialSubLotEntity
            {
                SubLotId = detail.BarCode,
                Type = Constants.DefaultSubLotType,
                ExternalStatus = Constants.DefaultSubLotExternalStatus
            };

            newSubLot.CreateCustomGuid(_user.Name);
            subLotsToAdd.Add(newSubLot);
            return newSubLot.ID;
        }

        /// <summary>
        /// 处理容器
        /// </summary>
        private string ProcessContainer(
            DistributionMaterialDetail detail,
            PreloadedData preloadedData,
            List<ContainerEntity> containersToAdd,
            List<ContainerHistoryEntity> containerHistoriesToAdd)
        {
            if (string.IsNullOrEmpty(detail.PalletNo))
                return null;

            string containerId;

            // 检查是否已存在
            var existingContainer = preloadedData.ExistingContainers.FirstOrDefault(x => x.Name == detail.PalletNo);
            if (existingContainer != null)
            {
                containerId = existingContainer.ID;
            }
            else
            {
                // 检查是否已在待添加列表中
                var containerInList = containersToAdd.FirstOrDefault(x => x.Name == detail.PalletNo);
                if (containerInList != null)
                {
                    containerId = containerInList.ID;
                }
                else
                {
                    // 创建新容器
                    var newContainer = new ContainerEntity
                    {
                        Name = detail.PalletNo,
                        Status = Constants.DefaultContainerStatus,
                        Class = Constants.DefaultContainerClass
                    };

                    newContainer.CreateCustomGuid(_user.Name);
                    containersToAdd.Add(newContainer);
                    containerId = newContainer.ID;
                }
            }

            // 添加容器历史记录
            var containerHistory = new ContainerHistoryEntity
            {
                ContainerId = containerId,
                Type = Constants.ContainerHistoryTypeInstock,
                Comment = Constants.ContainerHistoryComment,
                MaterialId = detail.MaterialCode,
                Quantity = detail.Quantity.ToString(),
                ContainerCode = detail.PalletNo
            };

            containerHistory.CreateCustomGuid(_user.Name);
            containerHistoriesToAdd.Add(containerHistory);

            return containerId;
        }

        /// <summary>
        /// 获取单位ID
        /// </summary>
        private string GetQuantityUomId(string unitName, PreloadedData preloadedData)
        {
            if (string.IsNullOrEmpty(unitName))
                return null;

            return preloadedData.Units.FirstOrDefault(x => x.Name == unitName)?.ID;
        }

        /// <summary>
        /// 创建库存实体
        /// </summary>
        private MaterialInventoryEntity CreateInventoryEntity(
            DistributionMaterialDetail detail,
            ProcessResult processResult,
            PreloadedData preloadedData)
        {
            var inventory = new MaterialInventoryEntity
            {
                LotId = processResult.LotId,
                SublotId = processResult.SubLotId,
                Quantity = detail.Quantity,
                QuantityUomId = processResult.QuantityUomId,
                StorageLocation = preloadedData.Equipment?.EquipmentCode,
                EquipmentId = preloadedData.Equipment?.ID,
                IsPrechecked = Constants.DefaultIsPrechecked,
                ContainerId = processResult.ContainerId,
                Remark = detail.Remark,
                Createtime = DateTime.Now,
                Plant = detail.Plant,
                Density = detail.Density,
                CoAContent = detail.CoAContent
            };

            inventory.CreateCustomGuid(_user.Name);
            return inventory;
        }

        /// <summary>
        /// 创建转移实体
        /// </summary>
        private MaterialTransferEntity CreateTransferEntity(
            DistributionMaterialDetail detail,
            ProcessResult processResult,
            PreloadedData preloadedData)
        {
            var transfer = new MaterialTransferEntity
            {
                Type = Constants.TransferTypeCreateInventory,
                NewStorageLocation = preloadedData.Equipment?.EquipmentCode,
                NewLotId = processResult.LotId,
                NewSublotId = processResult.SubLotId,
                NewContainerId = processResult.ContainerId,
                Quantity = detail.Quantity,
                QuantityUomId = processResult.QuantityUomId,
                NewMaterialId = detail.MaterialCode,
                NewEquipmentId = preloadedData.Equipment?.ID,
                Comment = Constants.TransferComment,
                Status = Constants.TransferStatusCompleted
            };

            transfer.CreateCustomGuid(_user.Name);
            return transfer;
        }

        /// <summary>
        /// 批量插入实体
        /// </summary>
        private async Task BatchInsertEntities(
            List<MaterialLotEntity> lotsToAdd,
            List<MaterialSubLotEntity> subLotsToAdd,
            List<ContainerEntity> containersToAdd,
            List<ContainerHistoryEntity> containerHistoriesToAdd,
            List<MaterialInventoryEntity> inventoriesToAdd,
            List<MaterialTransferEntity> transfersToAdd)
        {
            // 批量插入批次
            if (lotsToAdd.Any())
            {
                await _materialLotRepository.Add(lotsToAdd);
            }

            // 批量插入子批次
            if (subLotsToAdd.Any())
            {
                await _materialSubLotRepository.Add(subLotsToAdd);
            }

            // 批量插入容器
            if (containersToAdd.Any())
            {
                await _containerRepository.Add(containersToAdd);
            }

            // 批量插入容器历史
            if (containerHistoriesToAdd.Any())
            {
                await _containerHistoryRepository.Add(containerHistoriesToAdd);
            }

            // 批量插入库存
            if (inventoriesToAdd.Any())
            {
                await _materialInventoryRepository.Add(inventoriesToAdd);
            }

            // 批量插入转移记录
            if (transfersToAdd.Any())
            {
                await _materialTransferRepository.Add(transfersToAdd);
            }
        }
    }
}