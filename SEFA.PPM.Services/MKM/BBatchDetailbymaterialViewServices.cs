
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.MKM.Services
{
    public class BBatchDetailbymaterialViewServices : BaseServices<BBatchDetailbymaterialViewEntity>, IBBatchDetailbymaterialViewServices
    {
        private readonly IBaseRepository<BBatchDetailbymaterialViewEntity> _dal;
        public BBatchDetailbymaterialViewServices(IBaseRepository<BBatchDetailbymaterialViewEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<BBatchDetailbymaterialViewEntity>> GetList(BBatchDetailbymaterialViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<BBatchDetailbymaterialViewEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<BBatchDetailbymaterialViewEntity>> GetPageList(BBatchDetailbymaterialViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<BBatchDetailbymaterialViewEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

        public async Task<bool> SaveForm(BBatchDetailbymaterialViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}