
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.MKM.Services
{
    public class ContainerHistoryServices : BaseServices<ContainerHistoryEntity>, IContainerHistoryServices
    {
        private readonly IBaseRepository<ContainerHistoryEntity> _dal;
        public ContainerHistoryServices(IBaseRepository<ContainerHistoryEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<ContainerHistoryEntity>> GetList(ContainerHistoryRequestModel reqModel)
        {
            List<ContainerHistoryEntity> result = new List<ContainerHistoryEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<ContainerHistoryEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<ContainerHistoryEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        public async Task<PageModel<ContainerHistoryEntity>> GetPageList(ContainerHistoryRequestModel reqModel)
        {
            PageModel<ContainerHistoryEntity> result = new PageModel<ContainerHistoryEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<ContainerHistoryEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<ContainerHistoryEntity>()
                .Where(whereExpression)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        public async Task<bool> SaveForm(ContainerHistoryEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}