
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using System.Linq;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.IRepository.UnitOfWork;

namespace SEFA.PPM.Services
{
    public class InventServices : BaseServices<InventEntity>, IInventServices
    {
        private readonly IBaseRepository<InventEntity> _dal;
        private readonly IBaseRepository<PvEntity> _dalPvEntity;
        public IUser _user;
        private readonly IUnitOfWork _unitOfWork;

        public InventServices(IBaseRepository<InventEntity> dal, IBaseRepository<PvEntity> dalPvEntity, IUser user, IUnitOfWork unitOfWork)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _dalPvEntity = dalPvEntity;
            _user = user;
            _unitOfWork = unitOfWork;
        }

        public async Task<List<InventEntity>> GetList(InventRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<InventEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);

            return data;
        }

        public async Task<PageModel<InventEntity>> GetPageList(InventRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<InventEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);

            return data;
        }

        public async Task<bool> SaveMData()
        {
            try
            {
                //查询所有数据
                var result = await _dalPvEntity.FindList(p => p.MaterialId != null);
                if (result != null)
                {
                    //获取所有需要的属性
                    //    List<InventEntity> data = await _dal.FindList(p => p.ID != null);

                    var dataResult = await _dal.Db.Queryable<InventEntity>().ToListAsync();

                    string ID = string.Empty;
                    string CODE = string.Empty;
                    string NAME = string.Empty;
                    string FullBagWeight = string.Empty;
                    string NeedWeighingCheck = string.Empty;
                    string PreweighToleranceMaxPercent = string.Empty;
                    string PreweighToleranceMinPercent = string.Empty;
                    string RequiresPreWeigh = string.Empty;
                    string WeighingCheck_HighTolerance = string.Empty;
                    string WeighingCheck_LowTolerance = string.Empty;
                    string PrepByPO = string.Empty;


                    List<InventEntity> upList = new List<InventEntity>();
                    List<InventEntity> insertList = new List<InventEntity>();

                    for (int i = 0; i < result.Count; i++)
                    {
                        //获取属性
                        ID = result[i].MaterialId;
                        CODE = result[i].Code;
                        NAME = result[i].Name;
                        FullBagWeight = result[i].Fullbagweight;
                        NeedWeighingCheck = result[i].Needweighingcheck;
                        PreweighToleranceMaxPercent = result[i].Preweightolerancemaxpercent;
                        PreweighToleranceMinPercent = result[i].Preweightoleranceminpercent;
                        RequiresPreWeigh = result[i].Requirespreweigh;
                        WeighingCheck_HighTolerance = result[i].WeighingcheckHightolerance;
                        WeighingCheck_LowTolerance = result[i].WeighingcheckLowtolerance;
                        PrepByPO = result[i].Prepbypo;

                        var model = dataResult.Where(x => x.ID == ID).FirstOrDefault();
                        if (model == null)
                        {
                            //新增
                            InventEntity modelIn = new InventEntity();
                            modelIn.Create(_user.Name);
                            modelIn.MId = ID;
                            modelIn.Code = CODE;
                            modelIn.Name = NAME;
                            modelIn.Fullbagweight = FullBagWeight;
                            modelIn.Needweighingcheck = NeedWeighingCheck;
                            modelIn.Preweightolerancemaxpercent = PreweighToleranceMaxPercent;
                            modelIn.Preweightoleranceminpercent = PreweighToleranceMinPercent;
                            modelIn.Requirespreweigh = RequiresPreWeigh;
                            modelIn.WeighingcheckHightolerance = WeighingCheck_HighTolerance;
                            modelIn.WeighingcheckLowtolerance = WeighingCheck_LowTolerance;
                            modelIn.Prepbypo = PrepByPO;
                            insertList.Add(modelIn);
                        }
                        else
                        {
                            //更新
                            if (model.Fullbagweight != FullBagWeight || model.Needweighingcheck != NeedWeighingCheck
                                || model.Preweightolerancemaxpercent != PreweighToleranceMaxPercent || model.Preweightoleranceminpercent != PreweighToleranceMinPercent
                                || model.Requirespreweigh != RequiresPreWeigh || model.WeighingcheckHightolerance != WeighingCheck_HighTolerance
                                 || model.WeighingcheckLowtolerance != WeighingCheck_HighTolerance || model.Prepbypo != PrepByPO)
                            {

                                model.Modify(model.ID, _user.Name);
                                model.Code = CODE;
                                model.Name = NAME;
                                model.Fullbagweight = FullBagWeight;
                                model.Needweighingcheck = NeedWeighingCheck;
                                model.Preweightolerancemaxpercent = PreweighToleranceMaxPercent;
                                model.Preweightoleranceminpercent = PreweighToleranceMinPercent;

                                model.Requirespreweigh = RequiresPreWeigh;
                                model.WeighingcheckHightolerance = WeighingCheck_HighTolerance;
                                model.WeighingcheckLowtolerance = WeighingCheck_LowTolerance;
                                model.Prepbypo = PrepByPO;

                                upList.Add(model);
                            }
                        }
                    }

                    _unitOfWork.BeginTran();
                    if (upList.Count > 0)
                    {
                        bool r = await _dal.Update(upList);
                        if (r == false)
                        {
                            _unitOfWork.RollbackTran();
                        }
                    }
                    if (insertList.Count > 0)
                    {
                        bool r = await _dal.Add(insertList) > 0;
                        if (r == false) { _unitOfWork.RollbackTran(); }
                    }
                    _unitOfWork.CommitTran();
                }

                return false;
            }
            catch (System.Exception)
            {
                _unitOfWork.RollbackTran();
                return false;
            }

        }

        public async Task<bool> SaveForm(InventEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }

    }
}