
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.PPM.Services
{
    public class RequestIiiViewServices : BaseServices<RequestIiiViewEntity>, IRequestIiiViewServices
    {
        private readonly IBaseRepository<RequestIiiViewEntity> _dal;
        public RequestIiiViewServices(IBaseRepository<RequestIiiViewEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<RequestIiiViewEntity>> GetList(RequestIiiViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<RequestIiiViewEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<RequestIiiViewEntity>> GetPageList(RequestIiiViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<RequestIiiViewEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

        public async Task<bool> SaveForm(RequestIiiViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}