
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Collections.Generic;
using System.Threading.Tasks;
using SEFA.MKM.Model.ViewModels;

namespace SEFA.MKM.Services
{
    public class MaterialSubLotServices : BaseServices<MaterialSubLotEntity>, IMaterialSubLotServices
    {
        private readonly IBaseRepository<MaterialSubLotEntity> _dal;
        public MaterialSubLotServices(IBaseRepository<MaterialSubLotEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }


        public async Task<List<MaterialSubLotEntity>> GetList(MaterialSubLotRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<MaterialSubLotEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<MaterialSubLotEntity>> GetPageList(MaterialSubLotRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<MaterialSubLotEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);

            return data;
        }

        public async Task<bool> SaveForm(MaterialSubLotEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}