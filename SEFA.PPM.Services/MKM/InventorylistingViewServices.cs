
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using System;
using Microsoft.AspNetCore.Mvc;
using MongoDB.Bson.Serialization.IdGenerators;
using System.Linq;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.DFM.Model.ViewModels;
using System.Reflection;
using System.Text.RegularExpressions;
using SEFA.DFM.Model.Models;
using StackExchange.Profiling.Internal;
using ActionPropertyEntity = SEFA.PPM.Model.Models.PTM.ActionPropertyEntity;
using EquipmentEntity = SEFA.DFM.Model.Models.EquipmentEntity;
using SEFA.PPM.Model.Models;
using SEFA.MKM.Model.ViewModels.View;
using SEFA.PPM.Model.ViewModels.MKM.InterfaceView;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Logical;
using NodaTime.Calendars;
using static SEFA.PPM.Services.TippingMlistViewServices;
using Microsoft.Extensions.Logging;

namespace SEFA.MKM.Services
{
    public class InventorylistingViewServices : BaseServices<InventorylistingViewEntity>, IInventorylistingViewServices
    {
        private readonly IBaseRepository<VerifiyDetailEntity> _dalVerifiyDetailEntity;

        private readonly IBaseRepository<InventorylistingViewEntity> _dal;
        private readonly IBaseRepository<DFM.Model.Models.LabelEquipmentPrinterEntity> _dalLabelEquipmentPrinterEntity;
        private readonly IBaseRepository<DFM.Model.Models.LabelPrinterEntity> _dalLabelPrinterEntity;
        private readonly IBaseRepository<MaterialSubLotEntity> _dalMaterialSubLotEntity;
        private readonly IBaseRepository<MaterialLotEntity> _dalMaterialLotEntity;
        private readonly IBaseRepository<ActionPropertyEntity> _dalactionProperty;
        private readonly IBaseRepository<EquipmentEntity> _dalEquipment;

        private readonly IBaseRepository<PrintSelectViewEntity> _PrintSelectViewEntityDal;

        public IUser _user;
        public InventorylistingViewServices(IBaseRepository<InventorylistingViewEntity> dal, IBaseRepository<DFM.Model.Models.LabelEquipmentPrinterEntity> dalLabelEquipmentPrinterEntity, IBaseRepository<DFM.Model.Models.LabelPrinterEntity> dalLabelPrinterEntity, IUser user, IBaseRepository<MaterialSubLotEntity> dalMaterialSubLotEntity, IBaseRepository<MaterialLotEntity> dalMaterialLotEntity, IBaseRepository<ActionPropertyEntity> dalactionProperty, IBaseRepository<EquipmentEntity> dalEquipment, IBaseRepository<PrintSelectViewEntity> printSelectViewEntityDal, IBaseRepository<VerifiyDetailEntity> dalVerifiyDetailEntity)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _dalLabelEquipmentPrinterEntity = dalLabelEquipmentPrinterEntity;
            _dalLabelPrinterEntity = dalLabelPrinterEntity;
            _user = user;
            _dalMaterialSubLotEntity = dalMaterialSubLotEntity;
            _dalMaterialLotEntity = dalMaterialLotEntity;
            _dalactionProperty = dalactionProperty;
            _dalEquipment = dalEquipment;
            _PrintSelectViewEntityDal = printSelectViewEntityDal;
            _dalVerifiyDetailEntity = dalVerifiyDetailEntity;
        }

        /// <summary>
        ///  默认打印机
        /// </summary>
        /// <param name="eqpmentID"></param>
        /// <returns></returns>
        public async Task<List<DFM.Model.Models.LabelPrinterEntity>> GetSelectPrinit(string eqpmentID)
        {
            if (!string.IsNullOrEmpty(eqpmentID))
            {
                var data1 = await _dalLabelPrinterEntity.FindList(a => a.EquipmentId == eqpmentID);
                return data1;
            }
            var data = await _dalLabelPrinterEntity.FindList(a => a.ID != string.Empty);
            return data;
        }




        public async Task<List<InventorylistingViewEntity>> GetList(InventorylistingViewRequestModel reqModel)
        {
            List<InventorylistingViewEntity> result = new List<InventorylistingViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<InventorylistingViewEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<InventorylistingViewEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        /// <summary>
        /// 根据ID集合获取对应的数据集合
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<List<InventorylistingViewEntity>> GetInventoryListByID(String[] id)
        {
            List<InventorylistingViewEntity> result = new List<InventorylistingViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<InventorylistingViewEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<InventorylistingViewEntity>().In(P => P.ID, id)
                .Where(whereExpression).ToListAsync();
            return data;
        }

        public async Task<List<InventoryModel>> GetExportList(InventorylistingViewRequestModel reqModel)
        {
            List<InventorylistingViewEntity> result = new List<InventorylistingViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<InventorylistingViewEntity>()
                  //加入查询条件(时间)
                  .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
                  .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
                  //原料
                  .AndIF(!string.IsNullOrEmpty(reqModel.Material), a => a.MaterialCode.Contains(reqModel.Material)
                                                     || a.MaterialName.Contains(reqModel.Material))
                  //批次
                  .AndIF(!string.IsNullOrEmpty(reqModel.Batch), a => a.BatchId.Contains(reqModel.Batch))
                  //SSCC
                  .AndIF(!string.IsNullOrEmpty(reqModel.Sscc), a => a.Sscc.Contains(reqModel.Sscc))
                  //批次状态
                  .AndIF(!string.IsNullOrEmpty(reqModel.Statusf), a => a.StatusF == reqModel.Statusf)
                  //sscc状态
                  .AndIF(!string.IsNullOrEmpty(reqModel.Statuss), a => a.StatusS == reqModel.Statuss)
                  //新批子批次
                  .AndIF(!string.IsNullOrEmpty(reqModel.Location), a => a.LocationF.Contains(reqModel.Location))
                  //Bin
                  .AndIF(!string.IsNullOrEmpty(reqModel.Bin), a => a.LocationS.Contains(reqModel.Bin))
                   .AndIF(!string.IsNullOrEmpty(reqModel.ProOrderNo), a => a.ProductionOrderNo.Contains(reqModel.ProOrderNo))
                   //Container     
                   .AndIF(!string.IsNullOrEmpty(reqModel.Container), a => a.ContainerName.Contains(reqModel.Container))
                   .AndIF(!string.IsNullOrEmpty(reqModel.ProcessOrder), a => a.ProductionOrderNo.Contains(reqModel.ProcessOrder))
                   .AndIF(!string.IsNullOrEmpty(reqModel.ContainerID), a => a.ContainerId.Contains(reqModel.ContainerID))
                   .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId == reqModel.EquipmentId)
                               .ToExpression();
            var data = await _dal.Db.Queryable<InventorylistingViewEntity>()
                .Where(whereExpression).ToListAsync();

            var results = (from a in data
                           select new InventoryModel
                           {
                               MaterialCode = a.MaterialCode,
                               MaterialName = a.MaterialName,
                               ProBatch = a.ProBatch,
                               ClassDec = a.ClassDec,
                               StatusFl = a.StatusFl,
                               BatchId = a.BatchId,
                               StatusSl = a.StatusSl,
                               Sscc = a.Sscc,
                               Quantity = a.Quantity.Value,
                               MaxUnit = a.MaxUnit,
                               LocationF = a.LocationF,
                               LocationS = a.LocationS,
                               ExpirationDate = a.ExpirationDate,
                               CreatedDate = a.CreatedDate,
                               Sapformula = a.Sapformula
                           }).ToList();
            return results;
        }

        //查询
        public async Task<PageModel<InventorylistingViewEntity>> GetPageList(InventorylistingViewRequestModel reqModel)
        {

            PageModel<InventorylistingViewEntity> result = new PageModel<InventorylistingViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<InventorylistingViewEntity>()
                //加入查询条件(时间)
                .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
                .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
                //原料
                .AndIF(!string.IsNullOrEmpty(reqModel.Material), a => a.MaterialCode.Contains(reqModel.Material)
                                                   || a.MaterialName.Contains(reqModel.Material))
                //批次
                .AndIF(!string.IsNullOrEmpty(reqModel.Batch), a => a.BatchId.Contains(reqModel.Batch))
                //SSCC
                .AndIF(!string.IsNullOrEmpty(reqModel.Sscc), a => a.Sscc.Contains(reqModel.Sscc))
                //批次状态
                .AndIF(!string.IsNullOrEmpty(reqModel.Statusf), a => a.StatusF == reqModel.Statusf)
                //sscc状态
                .AndIF(!string.IsNullOrEmpty(reqModel.Statuss), a => a.StatusS == reqModel.Statuss)
                //新批子批次
                .AndIF(!string.IsNullOrEmpty(reqModel.Location), a => a.LocationF.Contains(reqModel.Location))
                 .AndIF(!string.IsNullOrEmpty(reqModel.Formula), a => a.Sapformula.Contains(reqModel.Formula))
                //Bin
                .AndIF(!string.IsNullOrEmpty(reqModel.Bin), a => a.LocationS.Contains(reqModel.Bin))
                 .AndIF(!string.IsNullOrEmpty(reqModel.ProOrderNo), a => a.ProductionOrderNo.Contains(reqModel.ProOrderNo))
                 //桶号
                 .AndIF(!string.IsNullOrEmpty(reqModel.Bucketnum), a => a.Bucketnum.Contains(reqModel.Bucketnum))
                 .AndIF(!string.IsNullOrEmpty(reqModel.typeSearch), a => a.MaterialCode.Contains(reqModel.typeSearch) || a.BatchId.Contains(reqModel.typeSearch) || a.Sscc.Contains(reqModel.typeSearch))
                 //Container     
                 .AndIF(!string.IsNullOrEmpty(reqModel.Container), a => a.ContainerName.Contains(reqModel.Container))
                 .AndIF(!string.IsNullOrEmpty(reqModel.ProcessOrder), a => a.ProductionOrderNo.Contains(reqModel.ProcessOrder))
                 .AndIF(!string.IsNullOrEmpty(reqModel.ContainerID), a => a.ContainerId.Contains(reqModel.ContainerID))
                 .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId == reqModel.EquipmentId)
                             .ToExpression();

            //原料标签
            if (reqModel.typeRoom == "YLABLE")
            {
                //var data1 = await _dal.Db.Queryable<InventorylistingViewEntity>()
                //    .Where(whereExpression).Where(p => p.ProBatch == "" || p.ProBatch == null).OrderByDescending(p => p.CreateDate)
                //    .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
                //result.dataCount = dataCount;
                //result.data = data1;
                //return result;

                var data2 = await _dal.Db.Queryable<InventorylistingViewEntity>()
                   .Where(whereExpression).ToListAsync();
                data2 = data2.Where(p => p.ProBatch == "" || p.ProBatch == null && (p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04")).ToList();
                int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
                var rDat = data2.OrderByDescending(p => p.CreateDate).Skip(startIndex).Take(reqModel.pageSize).ToList();
                result.dataCount = data2.Count;
                result.data = rDat;
                return result;

            }
            //原料加工厂 
            else if (reqModel.typeRoom == "JGC")
            {
                //var data2 = await _dal.Db.Queryable<InventorylistingViewEntity>()
                //.Where(whereExpression).Where(p => p.ProBatch != "" || p.ProBatch != null).OrderByDescending(p => p.CreateDate)
                //.ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
                //result.dataCount = dataCount;
                //result.data = data2;
                //return result;

                var data2 = await _dal.Db.Queryable<InventorylistingViewEntity>()
                .Where(whereExpression).ToListAsync();
                data2 = data2.Where(p => p.ProBatch != "" || p.ProBatch != null && (p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04")).ToList();
                int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
                var rDat = data2.OrderByDescending(p => p.CreateDate).Skip(startIndex).Take(reqModel.pageSize).ToList();
                result.dataCount = data2.Count;
                result.data = rDat;
                return result;
            }
            else if (reqModel.typeRoom == "CYC")
            {

                //var data2 = await _dal.Db.Queryable<InventorylistingViewEntity>()
                //.Where(whereExpression).OrderByDescending(p => p.CreateDate)
                //.ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
                //result.dataCount = dataCount;
                //result.data = data2;
                //return result;

                var data2 = await _dal.Db.Queryable<InventorylistingViewEntity>()
                .Where(whereExpression).ToListAsync();
                data2 = data2.Where(p => p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04").ToList();
                int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
                var rDat = data2.OrderByDescending(p => p.CreateDate).Skip(startIndex).Take(reqModel.pageSize).ToList();
                result.dataCount = data2.Count;
                result.data = rDat;
                return result;
            }
            else if (reqModel.typeRoom == "SC")
            {
                whereExpression = Expressionable.Create<InventorylistingViewEntity>()
               //加入查询条件(时间)
               .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
               .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
               //原料
               .AndIF(!string.IsNullOrEmpty(reqModel.Material), a => a.MaterialCode.Contains(reqModel.Material)
                                                  || a.MaterialName.Contains(reqModel.Material))
               .AndIF(!string.IsNullOrEmpty(reqModel.typeSearch), a => a.MaterialCode.Contains(reqModel.typeSearch) || a.BatchId.Contains(reqModel.typeSearch) || a.Sscc.Contains(reqModel.typeSearch))
               //批次
               .AndIF(!string.IsNullOrEmpty(reqModel.Batch), a => a.BatchId.Contains(reqModel.Batch))
               //SSCC
               .AndIF(!string.IsNullOrEmpty(reqModel.Sscc), a => a.Sscc.Contains(reqModel.Sscc))
               //批次状态
               .AndIF(!string.IsNullOrEmpty(reqModel.Statusf), a => a.StatusF == reqModel.Statusf)
               //sscc状态
               .AndIF(!string.IsNullOrEmpty(reqModel.Statuss), a => a.StatusS == reqModel.Statuss)
               //新批子批次
               .AndIF(!string.IsNullOrEmpty(reqModel.Location), a => a.LocationF.Contains(reqModel.Location))
               //Bin
               .AndIF(!string.IsNullOrEmpty(reqModel.Bin), a => a.LocationS.Contains(reqModel.Bin))
                .AndIF(!string.IsNullOrEmpty(reqModel.ProOrderNo), a => a.ProductionOrderNo.Contains(reqModel.ProOrderNo))
                //桶号
                .AndIF(!string.IsNullOrEmpty(reqModel.Bucketnum), a => a.Bucketnum.Contains(reqModel.Bucketnum))
                //Container     
                .AndIF(!string.IsNullOrEmpty(reqModel.Container), a => a.ContainerName.Contains(reqModel.Container))
                .AndIF(!string.IsNullOrEmpty(reqModel.ProcessOrder), a => a.ProductionOrderNo.Contains(reqModel.ProcessOrder))
                .AndIF(!string.IsNullOrEmpty(reqModel.ContainerID), a => a.ContainerId.Contains(reqModel.ContainerID))
                .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId == reqModel.EquipmentId)
                            // .And(a => !(a.LocationS.Contains("MFG3") && !a.LocationS.Contains("PKG3") && !a.LocationS.Contains("SUR3")))
                            .ToExpression();
                //var data2 = await _dal.Db.Queryable<InventorylistingViewEntity>()
                //.Where(whereExpression).OrderByDescending(p => p.CreateDate)
                //.ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
                //result.dataCount = dataCount;
                //result.data = data2;
                //return result;

                var data2 = await _dal.Db.Queryable<InventorylistingViewEntity>()
                .Where(whereExpression).ToListAsync();
                data2 = data2.Where(p => p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04").ToList();
                int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
                var rDat = data2.OrderByDescending(p => p.CreateDate).Skip(startIndex).Take(reqModel.pageSize).ToList();
                result.dataCount = data2.Count;
                result.data = rDat;
                return result;
            }


            var data3 = await _dal.Db.Queryable<InventorylistingViewEntity>()
                .Where(whereExpression).ToListAsync();
            data3 = data3.Where(p => p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04" && !string.IsNullOrEmpty(p.Sscc)).ToList();
            int startIndex3 = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
            var rDat3 = data3.OrderByDescending(p => p.CreateDate).Skip(startIndex3).Take(reqModel.pageSize).ToList();
            result.dataCount = data3.Count;
            result.data = rDat3;
            return result;


            //var data3 = await _dal.Db.Queryable<InventorylistingViewEntity>()
            //    .Where(whereExpression).OrderByDescending(p => p.CreateDate)
            //    .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            //result.dataCount = dataCount;
            //result.data = data3;
            //return result;
        }


        public async Task<PageModel<InventorylistingViewEntity>> GetPageList_Partial(InventorylistingViewRequestModel reqModel)
        {
            //物料的saptype是 PKG3
            PageModel<InventorylistingViewEntity> result = new PageModel<InventorylistingViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<InventorylistingViewEntity>()
                //加入查询条件(时间)
                .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
                .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
                //原料
                .AndIF(!string.IsNullOrEmpty(reqModel.Material), a => a.MaterialCode.Contains(reqModel.Material)
                                                   || a.MaterialName.Contains(reqModel.Material))
                //批次
                .AndIF(!string.IsNullOrEmpty(reqModel.Batch), a => a.BatchId.Contains(reqModel.Batch))
                //SSCC
                .AndIF(!string.IsNullOrEmpty(reqModel.Sscc), a => a.Sscc.Contains(reqModel.Sscc))
                //批次状态
                .AndIF(!string.IsNullOrEmpty(reqModel.Statusf), a => a.StatusF == reqModel.Statusf)
                //sscc状态
                .AndIF(!string.IsNullOrEmpty(reqModel.Statuss), a => a.StatusS == reqModel.Statuss)
                //新批子批次
                .AndIF(!string.IsNullOrEmpty(reqModel.Location), a => a.LocationF.Contains(reqModel.Location))
                 //Partial
                 .And(a => a.InventoryType.Contains("Partial"))
                 .AndIF(!string.IsNullOrEmpty(reqModel.ProOrderNo), a => a.ProductionOrderNo.Contains(reqModel.ProOrderNo))
                 //桶号
                 .AndIF(!string.IsNullOrEmpty(reqModel.Bucketnum), a => a.Bucketnum.Contains(reqModel.Bucketnum))
                 //桶号
                 .AndIF(!string.IsNullOrEmpty(reqModel.Formula), a => a.Sapformula.Contains(reqModel.Formula))
                 .AndIF(!string.IsNullOrEmpty(reqModel.typeSearch), a => a.MaterialCode.Contains(reqModel.typeSearch)
                 || a.BatchId.Contains(reqModel.typeSearch) || a.Sscc.Contains(reqModel.typeSearch) || a.ClassDec.Contains(reqModel.typeSearch))
                 //Container     
                 .AndIF(!string.IsNullOrEmpty(reqModel.Container), a => a.ContainerName.Contains(reqModel.Container))
                 .AndIF(!string.IsNullOrEmpty(reqModel.ProcessOrder), a => a.ProductionOrderNo.Contains(reqModel.ProcessOrder))
                 .AndIF(!string.IsNullOrEmpty(reqModel.ContainerID), a => a.ContainerId.Contains(reqModel.ContainerID))
                 .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId == reqModel.EquipmentId)
                             .ToExpression();

            var data3 = await _dal.Db.Queryable<InventorylistingViewEntity>()
              .Where(whereExpression).ToListAsync();

            int startIndex3 = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
            var rDat3 = data3.OrderBy(p => p.ExpirationDate).ThenByDescending(p => p.CreateDate).Skip(startIndex3).Take(reqModel.pageSize).ToList();
            result.dataCount = data3.Count;
            result.data = rDat3;
            return result;
        }

        //查询
        public async Task<PageModel<InventorylistingViewEntity>> GetPageList_WL(InventorylistingViewRequestModel reqModel)
        {
            //物料的saptype是 PKG3
            PageModel<InventorylistingViewEntity> result = new PageModel<InventorylistingViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<InventorylistingViewEntity>()
                //加入查询条件(时间)
                .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
                .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
                //原料
                .AndIF(!string.IsNullOrEmpty(reqModel.Material), a => a.MaterialCode.Contains(reqModel.Material)
                                                   || a.MaterialName.Contains(reqModel.Material))
                //批次
                .AndIF(!string.IsNullOrEmpty(reqModel.Batch), a => a.BatchId.Contains(reqModel.Batch))
                //SSCC
                .AndIF(!string.IsNullOrEmpty(reqModel.Sscc), a => a.Sscc.Contains(reqModel.Sscc))
                //批次状态
                .AndIF(!string.IsNullOrEmpty(reqModel.Statusf), a => a.StatusF == reqModel.Statusf)
                //sscc状态
                .AndIF(!string.IsNullOrEmpty(reqModel.Statuss), a => a.StatusS == reqModel.Statuss)
                //新批子批次
                .AndIF(!string.IsNullOrEmpty(reqModel.Location), a => a.LocationF.Contains(reqModel.Location))
                //Bin
                .And(a => a.LocationS.Contains("PKG3"))
                .AndIF(!string.IsNullOrEmpty(reqModel.ProOrderNo), a => a.ProductionOrderNo.Contains(reqModel.ProOrderNo))
                .AndIF(!string.IsNullOrEmpty(reqModel.classType), a => a.ClassDec.Contains(reqModel.classType))
                //桶号
                .AndIF(!string.IsNullOrEmpty(reqModel.Bucketnum), a => a.Bucketnum.Contains(reqModel.Bucketnum))
                 //桶号
                 .AndIF(!string.IsNullOrEmpty(reqModel.Formula), a => a.Sapformula.Contains(reqModel.Formula))
                 .AndIF(!string.IsNullOrEmpty(reqModel.typeSearch), a => a.MaterialCode.Contains(reqModel.typeSearch)
                 || a.BatchId.Contains(reqModel.typeSearch) || a.Sscc.Contains(reqModel.typeSearch) || a.ClassDec.Contains(reqModel.typeSearch))
                 //Container     
                 .AndIF(!string.IsNullOrEmpty(reqModel.Container), a => a.ContainerName.Contains(reqModel.Container))
                 .AndIF(!string.IsNullOrEmpty(reqModel.ProcessOrder), a => a.ProductionOrderNo.Contains(reqModel.ProcessOrder))
                 .AndIF(!string.IsNullOrEmpty(reqModel.ContainerID), a => a.ContainerId.Contains(reqModel.ContainerID))
                 .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId == reqModel.EquipmentId)
                             .ToExpression();

            //原料标签
            if (reqModel.typeRoom == "YLABLE")
            {
                //var data1 = await _dal.Db.Queryable<InventorylistingViewEntity>()
                //    .Where(whereExpression).Where(p => p.ProBatch == "" || p.ProBatch == null).OrderByDescending(p => p.CreateDate)
                //    .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
                //result.dataCount = dataCount;
                //result.data = data1;
                //return result;

                var data2 = await _dal.Db.Queryable<InventorylistingViewEntity>()
                    .Where(whereExpression).ToListAsync();
                data2 = data2.Where(p => p.ProBatch == "" || p.ProBatch == null).Where(p => p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04").OrderByDescending(p => p.CreateDate)
                 .ToList();

                int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
                var rDat = data2.OrderByDescending(p => p.CreateDate).Skip(startIndex).Take(reqModel.pageSize).ToList();
                result.dataCount = data2.Count;
                result.data = rDat;
                return result;

            }
            //原料加工厂 
            else if (reqModel.typeRoom == "JGC")
            {
                //var data2 = await _dal.Db.Queryable<InventorylistingViewEntity>()
                //.Where(whereExpression).Where(p => p.ProBatch != "" || p.ProBatch != null).OrderByDescending(p => p.CreateDate)
                //.ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
                //result.dataCount = dataCount;
                //result.data = data2;
                //return result;

                var data2 = await _dal.Db.Queryable<InventorylistingViewEntity>()
                .Where(whereExpression).ToListAsync();
                data2 = data2.Where(p => p.ProBatch != "" || p.ProBatch != null).
                Where(p => p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04").ToList();


                int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
                var rDat = data2.OrderByDescending(p => p.CreateDate).Skip(startIndex).Take(reqModel.pageSize).ToList();
                result.dataCount = data2.Count;
                result.data = rDat;
                return result;
            }

            var data3 = await _dal.Db.Queryable<InventorylistingViewEntity>()
              .Where(whereExpression).ToListAsync();

            if (string.IsNullOrEmpty(reqModel.IsBand))
            {
                data3 = data3.Where(p => p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04").ToList();
            }
            else if (reqModel.IsBand == "OK")
            {
                data3 = data3.Where(p => p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04" && !string.IsNullOrEmpty(p.ProductionRequestId)).ToList();
            }
            else if (reqModel.IsBand == "NOK")
            {
                data3 = data3.Where(p => p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04" && string.IsNullOrEmpty(p.ProductionRequestId)).ToList();
            }

            int startIndex3 = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
            var rDat3 = data3.OrderBy(p => p.ExpirationDate).ThenByDescending(p => p.CreateDate).Skip(startIndex3).Take(reqModel.pageSize).ToList();
            result.dataCount = data3.Count;
            result.data = rDat3;
            return result;

            //var data3 = await _dal.Db.Queryable<InventorylistingViewEntity>()
            //    .Where(whereExpression).OrderByDescending(p => p.CreateDate)
            //    .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            //result.dataCount = dataCount;
            //result.data = data3;
            //return result;
        }

        //查询
        public async Task<PageModel<InventorylistingViewEntity>> GetPageList_YL(InventorylistingViewRequestModel reqModel)
        {
            /// 原料的saptYPE  MFG3、SUR03
            PageModel<InventorylistingViewEntity> result = new PageModel<InventorylistingViewEntity>();
            RefAsync<int> dataCount = 0;


            if (reqModel.typeRoom == "JGC")
            {
                var whereExpression1 = Expressionable.Create<InventorylistingViewEntity>()
               //加入查询条件(时间)
               .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
               .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
               //原料
               .AndIF(!string.IsNullOrEmpty(reqModel.Material), a => a.MaterialCode.Contains(reqModel.Material)
                                                  || a.MaterialName.Contains(reqModel.Material))
               //批次
               .AndIF(!string.IsNullOrEmpty(reqModel.Batch), a => a.BatchId.Contains(reqModel.Batch))
               //SSCC
               .AndIF(!string.IsNullOrEmpty(reqModel.Sscc), a => a.Sscc.Contains(reqModel.Sscc))
               //批次状态
               .AndIF(!string.IsNullOrEmpty(reqModel.Statusf), a => a.StatusF == reqModel.Statusf)
               //sscc状态
               .AndIF(!string.IsNullOrEmpty(reqModel.Statuss), a => a.StatusS == reqModel.Statuss)
               //新批子批次
               .AndIF(!string.IsNullOrEmpty(reqModel.Location), a => a.LocationF.Contains(reqModel.Location))
               .AndIF(!string.IsNullOrEmpty(reqModel.typeSearch), a => a.MaterialCode.Contains(reqModel.typeSearch) || a.ClassDec.Contains(reqModel.typeSearch) || a.BatchId.Contains(reqModel.typeSearch) || a.Sscc.Contains(reqModel.typeSearch))

               //Bin
               // .AndIF(!string.IsNullOrEmpty(reqModel.Bin), a => a.LocationS.Contains(reqModel.Bin))
               .And(a => (a.LocationS.Contains("MFG3") || a.LocationS.Contains("SUR3")))
                .AndIF(!string.IsNullOrEmpty(reqModel.ProOrderNo), a => a.ProductionOrderNo.Contains(reqModel.ProOrderNo))
                //桶号
                .AndIF(!string.IsNullOrEmpty(reqModel.Bucketnum), a => a.Bucketnum.Contains(reqModel.Bucketnum))
                //配方号(by工单单独用)
                .AndIF(!string.IsNullOrEmpty(reqModel.Formula), a => a.Formula.Contains(reqModel.Formula))
                //Container     
                .AndIF(!string.IsNullOrEmpty(reqModel.Container), a => a.ContainerName.Contains(reqModel.Container))
                .AndIF(!string.IsNullOrEmpty(reqModel.ProcessOrder), a => a.ProductionOrderNo.Contains(reqModel.ProcessOrder))
                .AndIF(!string.IsNullOrEmpty(reqModel.ContainerID), a => a.ContainerId.Contains(reqModel.ContainerID))
                .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId == reqModel.EquipmentId)
                            .ToExpression();

                var data2 = await _dal.Db.Queryable<InventorylistingViewEntity>().Where(whereExpression1).ToListAsync();
                data2 = data2.Where(p => p.ProBatch != "" || p.ProBatch != null)
                    .Where(p => p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04").ToList();
                int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
                var rDat = data2.OrderByDescending(p => p.CreateDate).Skip(startIndex).Take(reqModel.pageSize).ToList();
                result.dataCount = data2.Count;
                result.data = rDat;
                return result;
            }

            var whereExpression = Expressionable.Create<InventorylistingViewEntity>()
                //加入查询条件(时间)
                .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
                .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
                //原料
                .AndIF(!string.IsNullOrEmpty(reqModel.Material), a => a.MaterialCode.Contains(reqModel.Material)
                                                   || a.MaterialName.Contains(reqModel.Material))
                //批次
                .AndIF(!string.IsNullOrEmpty(reqModel.Batch), a => a.BatchId.Contains(reqModel.Batch))
                //SSCC
                .AndIF(!string.IsNullOrEmpty(reqModel.Sscc), a => a.Sscc.Contains(reqModel.Sscc))
                //批次状态
                .AndIF(!string.IsNullOrEmpty(reqModel.Statusf), a => a.StatusF == reqModel.Statusf)
                //sscc状态
                .AndIF(!string.IsNullOrEmpty(reqModel.Statuss), a => a.StatusS == reqModel.Statuss)
                //新批子批次
                .AndIF(!string.IsNullOrEmpty(reqModel.Location), a => a.LocationF.Contains(reqModel.Location))
                .AndIF(!string.IsNullOrEmpty(reqModel.typeSearch), a => a.MaterialCode.Contains(reqModel.typeSearch) || a.ClassDec.Contains(reqModel.typeSearch) || a.BatchId.Contains(reqModel.typeSearch) || a.Sscc.Contains(reqModel.typeSearch))

                //Bin
                // .AndIF(!string.IsNullOrEmpty(reqModel.Bin), a => a.LocationS.Contains(reqModel.Bin))
                .And(a => (a.LocationS.Contains("MFG3") || a.LocationS.Contains("MFG3") || a.LocationS.Contains("SUR3")))
                 .AndIF(!string.IsNullOrEmpty(reqModel.ProOrderNo), a => a.ProductionOrderNo.Contains(reqModel.ProOrderNo))
                 //桶号
                 .AndIF(!string.IsNullOrEmpty(reqModel.Bucketnum), a => a.Bucketnum.Contains(reqModel.Bucketnum))
                 //配方号
                 .AndIF(!string.IsNullOrEmpty(reqModel.Formula), a => a.Sapformula.Contains(reqModel.Formula))
                 //Container     
                 .AndIF(!string.IsNullOrEmpty(reqModel.Container), a => a.ContainerName.Contains(reqModel.Container))
                 .AndIF(!string.IsNullOrEmpty(reqModel.ProcessOrder), a => a.ProductionOrderNo.Contains(reqModel.ProcessOrder))
                 .AndIF(!string.IsNullOrEmpty(reqModel.ContainerID), a => a.ContainerId.Contains(reqModel.ContainerID))
                 .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId == reqModel.EquipmentId)
                             .ToExpression();



            //原料标签
            if (reqModel.typeRoom == "YLABLE")
            {
                //var data1 = await _dal.Db.Queryable<InventorylistingViewEntity>()
                //    .Where(whereExpression).Where(p => p.ProBatch == "" || p.ProBatch == null).OrderByDescending(p => p.CreateDate)
                //    .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
                //result.dataCount = dataCount;
                //result.data = data1;
                //return result;

                var data2 = await _dal.Db.Queryable<InventorylistingViewEntity>().Where(whereExpression).ToListAsync();

                data2 = data2.Where(p => p.ProBatch == "" || p.ProBatch == null).Where(p => p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04")
                    .ToList();

                int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
                var rDat = data2.OrderByDescending(p => p.CreateDate).Skip(startIndex).Take(reqModel.pageSize).ToList();
                result.dataCount = data2.Count;
                result.data = rDat;
                return result;
            }
            //原料加工厂 
            //else if (reqModel.typeRoom == "JGC")
            //{
            //    //var data2 = await _dal.Db.Queryable<InventorylistingViewEntity>()
            //    //.Where(whereExpression).Where(p => p.ProBatch != "" || p.ProBatch != null).OrderByDescending(p => p.CreateDate)
            //    //.ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            //    //result.dataCount = dataCount;
            //    //result.data = data2;
            //    //return result;

            //    var data2 = await _dal.Db.Queryable<InventorylistingViewEntity>()
            //    .Where(whereExpression).Where(p => p.ProBatch != "" || p.ProBatch != null).Where(p => p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04").OrderByDescending(p => p.CreateDate).ToListAsync();

            //    int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
            //    var rDat = data2.OrderBy(p => p.CreateDate).Skip(startIndex).Take(reqModel.pageSize).ToList();
            //    result.dataCount = data2.Count;
            //    result.data = rDat;
            //    return result;
            //}

            //这里排除原料（四场、豉油厂、原料加工厂节）
            // var eList = await _dalEquipment.FindList(p => p.EquipmentCode != "SaucePlant" && p.EquipmentCode != "ProcessPlant" && p.EquipmentCode != "Plant4");
            //List<string> eIDS = eList.GroupBy(P => P.ID).Select(g => g.Key).ToList();

            //if (eIDS != null && eIDS.Count > 0)
            //{
            //    //var data2 = await _dal.Db.Queryable<InventorylistingViewEntity>().In(p => p.EquipmentId, eIDS)
            // .Where(whereExpression).OrderByDescending(p => p.CreateDate)
            // .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            //result.dataCount = dataCount;
            //result.data = data2;
            //return result;


            //查询全集并分页
            var data3 = await _dal.Db.Queryable<InventorylistingViewEntity>()//.In(p => p.EquipmentId, eIDS)
            .Where(whereExpression).ToListAsync();
            if (string.IsNullOrEmpty(reqModel.IsBand))
            {
                data3 = data3.Where(p => p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04"
                && p.LocationFcode != "SaucePlant" && p.LocationFcode != "ProcessPlant" && p.LocationFcode != "Plant4").ToList();
            }
            else if (reqModel.IsBand == "OK")
            {
                data3 = data3.Where(p => p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04"
                   && p.LocationFcode != "SaucePlant" && p.LocationFcode != "ProcessPlant" && p.LocationFcode != "Plant4" && !string.IsNullOrEmpty(p.ProductionRequestId)).ToList();
            }
            else if (reqModel.IsBand == "NOK")
            {
                data3 = data3.Where(p => p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04"
                   && p.LocationFcode != "SaucePlant" && p.LocationFcode != "ProcessPlant" && p.LocationFcode != "Plant4" && string.IsNullOrEmpty(p.ProductionRequestId)).ToList();
            }
            int startIndex1 = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
                                                                            // 获取指定页的数据
            var rDat1 = data3.OrderBy(p => p.ExpirationDate).ThenByDescending(p => p.CreateDate).Skip(startIndex1).Take(reqModel.pageSize).ToList();
            result.dataCount = data3.Count;
            result.data = rDat1;
            return result;


            //}
            ////查询全集并分页
            //var data3 = await _dal.Db.Queryable<InventorylistingViewEntity>().Where(whereExpression).Where(p => p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04").OrderByDescending(p => p.CreateDate)
            //  .ToListAsync();

            //int startIndex3 = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
            //                                                                // 获取指定页的数据
            //var rDat3 = data3.OrderBy(p => p.CreateDate).Skip(startIndex3).Take(reqModel.pageSize).ToList();
            //result.dataCount = data3.Count;
            //result.data = rDat3;
            //return result;

            //var data3 = await _dal.Db.Queryable<InventorylistingViewEntity>()
            //    .Where(whereExpression).OrderByDescending(p => p.CreateDate)
            //    .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            //result.dataCount = dataCount;
            //result.data = data3;
            //return result;
        }
        //查询
        public async Task<PageModel<InventorylistingViewEntity>> GetPageList2(InventorylistingViewRequestModel reqModel)
        {
            var list = await _dalactionProperty.FindList(x => x.EquipmentId == reqModel.EquipmentId && x.ActionCode == "InventorySource" && x.PropertyCode == "Associated Node");
            var equipmentCodes = list?.Select(x =>
            {
                return !string.IsNullOrEmpty(x.PropertyValue) ? x.PropertyValue : x.DefaultValue;
            });
            if (equipmentCodes == null)
            {
                equipmentCodes = new List<string>();
            }
            var equipmentIds = (await _dalEquipment.FindList(x => equipmentCodes.Contains(x.EquipmentCode)))?.Select(x => x.ID) ?? new List<string>();

            PageModel<InventorylistingViewEntity> result = new PageModel<InventorylistingViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<InventorylistingViewEntity>()
                //加入查询条件(时间)
                .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
                .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
                //原料
                .AndIF(!string.IsNullOrEmpty(reqModel.Material), a => a.MaterialCode.Contains(reqModel.Material)
                                                   || a.MaterialName.Contains(reqModel.Material))
                //批次
                .AndIF(!string.IsNullOrEmpty(reqModel.Batch), a => a.BatchId.Contains(reqModel.Batch))
                //SSCC
                .AndIF(!string.IsNullOrEmpty(reqModel.Sscc), a => a.Sscc.Contains(reqModel.Sscc))
                //批次状态
                .AndIF(!string.IsNullOrEmpty(reqModel.Statusf), a => a.StatusF == reqModel.Statusf)
                //sscc状态
                .AndIF(!string.IsNullOrEmpty(reqModel.Statuss), a => a.StatusS == reqModel.Statuss)
                //新批子批次
                .AndIF(!string.IsNullOrEmpty(reqModel.Location), a => a.LocationF.Contains(reqModel.Location))
                 .AndIF(!string.IsNullOrEmpty(reqModel.Formula), a => a.Sapformula.Contains(reqModel.Formula))
                //Bin
                .AndIF(!string.IsNullOrEmpty(reqModel.Bin), a => a.LocationS.Contains(reqModel.Bin))
                 .AndIF(!string.IsNullOrEmpty(reqModel.ProOrderNo), a => a.ProductionOrderNo.Contains(reqModel.ProOrderNo))
                 //Container     
                 .AndIF(!string.IsNullOrEmpty(reqModel.Container), a => a.ContainerName.Contains(reqModel.Container))
                 .AndIF(!string.IsNullOrEmpty(reqModel.ProcessOrder), a => a.ProductionOrderNo.Contains(reqModel.ProcessOrder))
                 .AndIF(!string.IsNullOrEmpty(reqModel.ContainerID), a => a.ContainerId.Contains(reqModel.ContainerID))
                 .And(a => equipmentIds.Contains(a.EquipmentId))
                             .ToExpression();
            var data = await _dal.Db.Queryable<InventorylistingViewEntity>()
                .Where(whereExpression).Where(p => p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04" && !string.IsNullOrEmpty(p.Sscc)).OrderByDescending(p => p.CreateDate)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        /// <summary>
        /// 查询物料库存
        /// </summary>
        /// <param name="reqModel">物料</param>
        /// <param name="isnull">是否存在容器</param>
        /// <returns></returns>
        public async Task<PageModel<InventorylistingViewEntity>> GetPageListByContainerID(InventorylistingViewRequestModel reqModel)
        {
            PageModel<InventorylistingViewEntity> result = new PageModel<InventorylistingViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<InventorylistingViewEntity>().AndIF(!string.IsNullOrEmpty(reqModel.ContainerID), a => a.ContainerId.Contains(reqModel.ContainerID))
                             .ToExpression();

            var data = await _dal.Db.Queryable<InventorylistingViewEntity>()
                .Where(whereExpression).OrderByDescending(p => p.CreateDate)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        public async Task<InventorylistingViewEntity> QueryById(string id)
        {
            List<InventorylistingViewEntity> result = new List<InventorylistingViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<InventorylistingViewEntity>()
                             .AndIF(!string.IsNullOrEmpty(id), a => a.ID == id)
                             .ToExpression();
            InventorylistingViewEntity data = await _dal.Db.Queryable<InventorylistingViewEntity>()
                .Where(whereExpression).FirstAsync();
            return data;
        }

        /// <summary>
        /// 根据容器ID获取库存信息
        /// </summary>
        /// <param name="containerID"></param>
        /// <returns></returns>
        public async Task<InventorylistingViewEntity> QueryByContairID(string containerID)
        {
            List<InventorylistingViewEntity> result = new List<InventorylistingViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<InventorylistingViewEntity>()
                             .AndIF(!string.IsNullOrEmpty(containerID), a => a.ContainerId == containerID)
                             .ToExpression();
            InventorylistingViewEntity data = await _dal.Db.Queryable<InventorylistingViewEntity>()
                .Where(whereExpression).FirstAsync();
            return data;
        }

        /// <summary>
        /// 根据物料ID获取物料库存信息
        /// </summary>
        /// <param name="matarialID"></param>
        /// <returns></returns>
        public async Task<InventorylistingViewEntity> QueryByMatarialID(string matarialID)
        {
            List<InventorylistingViewEntity> result = new List<InventorylistingViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<InventorylistingViewEntity>()
                             .AndIF(!string.IsNullOrEmpty(matarialID), a => a.MaterialId == matarialID)
                             .ToExpression();
            InventorylistingViewEntity data = await _dal.Db.Queryable<InventorylistingViewEntity>()
                .Where(whereExpression).FirstAsync();
            return data;
        }

        /// <summary>
        /// 根据库存ID判断子批次是否可操作（批次，子批次，有效期）
        /// </summary>
        /// <param name="inventID"></param>
        /// <returns></returns>
        public async Task<string> GetStateByInventID(string[] inventID)
        {
            if (inventID == null || inventID.Length <= 0)
            {
                return "库存信息不存在";
            }
            for (int i = 0; i < inventID.Length; i++)
            {
                string id = inventID[i];
                if (string.IsNullOrEmpty(id))
                {
                    return "不存在库存信息";
                }

                //获取库存
                var result = await _dal.FindEntity(p => p.InventoryId == id);
                if (result == null)
                {
                    return "不存在库存信息";
                }

                string lotID = result.LotId;
                if (string.IsNullOrEmpty(lotID))
                {
                    return "不存在批次信息";
                }
                var lotResult = await _dalMaterialLotEntity.FindEntity(p => p.ID == lotID);
                if (lotResult == null)
                {
                    return "不存在批次信息";
                }
                if (lotResult.ExternalStatus != "2")
                {
                    return "批次状态为锁定，请先解锁";
                }
                if (lotResult.ExpirationDate < DateTime.Now)
                {
                    return "该批次已过有效期";
                }
                string subID = result.SlotId;
                if (string.IsNullOrEmpty(subID))
                {
                    return "不存在子批次信息";
                }
                var subLotResult = await _dalMaterialSubLotEntity.FindEntity(p => p.ID == subID);
                if (subLotResult == null)
                {
                    return "不存在子批次信息";
                }
                if (subLotResult.ExternalStatus != "3")
                {
                    if (subLotResult.ExternalStatus == "2")
                    {
                        return "子批次状态为质检，请先解锁";
                    }
                    return "子批次状态为锁定，请先解锁";
                }


            }
            return "";
        }



        public async Task<string> GetProductionByInventID(string[] inventID)
        {
            if (inventID == null || inventID.Length <= 0)
            {
                return "库存信息不存在";
            }

            var inventDatas = await _dal.Db.Queryable<InventorylistingViewEntity>().In(p => p.InventoryId, inventID).Where(p => !string.IsNullOrEmpty(p.ProductionOrderNo) || !string.IsNullOrEmpty(p.ContainerId)).ToListAsync();

            if (inventDatas != null && inventDatas.Count > 0)
            {
                return "存在带工单号和容器码库存数据";
            }
            return "";
        }


        /// <summary>
        /// 根据库存ID集合判断返回数据是否可以进行退货
        /// </summary>
        /// <param name="inventID">库存集合</param>
        /// <returns></returns>
        public async Task<string> CherkReturnData(string[] inventID)
        {
            if (inventID == null || inventID.Length <= 0)
            {
                return "库存信息不存在";
            }
            //查询数据
            var result = await _dal.Db.Queryable<InventorylistingViewEntity>().In(p => p.InventoryId, inventID).ToListAsync();
            if (result == null || inventID.Count() != result.Count)
            {
                return "失败;库存信息为空或缺失";
            }
            string[] stoType = result.Select(p => p.LocationS).Distinct().ToArray();
            if (stoType == null || stoType.Length != 1)
            {
                return "失败;物料信息不属于同一个位置";
            }
            string locationType = stoType[0];//MFG3/PKG3

            //筛选ZRAW/ZPKG,ZLBS/ZSFG
            string[] ClassType = result.Select(p => p.ClassCode).Distinct().ToArray();
            if (ClassType == null)
            {
                return "失败;物料类型不存在";
            }
            if (ClassType.Length != 1)
            {
                if (ClassType.Where(p => p.Contains("ZRAW") || p.Contains("ZSFG")).Count() > 0)
                {
                    return "失败;物料类型不匹配;";
                }
            }
            string mType = string.Empty;

            for (int i = 0; i < ClassType.Length; i++)
            {
                mType += ClassType[i] + ";";
            }
            //批次
            string[] lotType = result.Select(p => p.StatusFl).Distinct().ToArray();
            if (lotType == null || lotType.Length != 1)
            {
                return "失败;批次状态不统一";
            }
            //当前批次状态
            string nowState = lotType[0];//U/B
            //子批次
            string[] subType = result.Select(p => p.StatusSl).Distinct().ToArray();

            //U/B
            if (nowState == "U")
            {
                //不相等提示
                if (subType.Where(P => P.Contains("U")).Count() > 0)
                {
                    return "失败:请确认子批次和批次状态其中之一包含锁定状态（B）";
                }

                ////不相等提示
                //if (subType.Where(P => P.Contains("U")).Count() != subType.Length)
                //{
                //    return "失败;批次状态和子批次状态不统一";
                //}
            }
            else
            {
                //if (subType.Where(P => P.Contains("U")).Count() > 0)
                //{
                //    return "失败;批次状态和子批次状态不统一";
                //}
            }

            string subTypes = string.Empty;
            for (int i = 0; i < subType.Length; i++)
            {
                subTypes += subType[i] + ";";
            }

            //这里判断物料的类型
            if (mType.Contains("ZRAW"))
            {
                //判断输入哪个规则 P1/P2/P3/P4/P5
                if (locationType == "MFG3")
                {
                    if (nowState == "U" && subTypes.Contains("U") == true)
                    {
                        return "311;RS01Location";
                    }
                    else if (nowState == "B" && (subTypes.Contains("Q") == true || subTypes.Contains("B") == true))
                    {
                        return "325;RSBKLocation";
                    }
                }
                return "失败，不满足规则ZRAW-MFG3- U/或B/Q";
            }
            else if (mType.Contains("ZSFG"))
            {
                //判断输入哪个规则 P1/P2/P3/P4/P5
                if (locationType == "MFG3")
                {
                    if (nowState == "B" && (subTypes.Contains("Q") == true || subTypes.Contains("B") == true))
                    {
                        return "325;WP04QALocation";
                    }
                    else if (nowState == "U" && subTypes.Contains("U") == true)
                    {
                        return "311;WP04Location";
                    }
                }
                return "失败，不满足规则ZSFG-MFG3- U/或B/Q";
            }
            else if (mType.Contains("ZPKG") || mType.Contains("ZLBS"))
            {
                //判断输入哪个规则 P1/P2/P3/P4/P5
                if (locationType == "PKG3")
                {
                    if (nowState == "U" && subTypes.Contains("U") == true)
                    {
                        return "311;RS01Location";
                    }
                    else if (nowState == "B" && (subTypes.Contains("Q") == true || subTypes.Contains("B") == true))
                    {
                        return "325;RSBKLocation";
                    }
                }
                return "失败，不满足规则ZPKG/ZLBS-PKG3- U/或B/Q";
            }
            return "失败,type为" + mType;
        }


        /// <summary>
        /// 根据退货盘存ID集合判断返回数据是否可以进行退货
        /// </summary>
        /// <param name="inventID">库存集合</param>
        /// <returns></returns>
        public async Task<string> CherkReturnDataByVeriyDetail(string[] vID)
        {
            //查询数据
            var vList = await _dalVerifiyDetailEntity.Db.Queryable<VerifiyDetailEntity>().In(P => P.ID, vID).ToListAsync();

            string[] SublotIds = vList.GroupBy(p => p.SublotId).Select(p => p.Key).ToArray();
            if (SublotIds == null || SublotIds.Length <= 0)
            {
                return "库存信息不存在";
            }

            var inventList = await _dal.Db.Queryable<InventorylistingViewEntity>().In(P => P.SlotId, SublotIds).ToListAsync();
            string[] inventID = inventList.GroupBy(p => p.ID).Select(p => p.Key).ToArray();
            if (inventID == null || inventID.Length <= 0)
            {
                return "库存信息不存在";
            }
            //查询数据
            var result = await _dal.Db.Queryable<InventorylistingViewEntity>().In(p => p.InventoryId, inventID).ToListAsync();
            if (result == null || inventID.Count() != result.Count)
            {
                return "失败;库存信息为空或缺失";
            }
            string[] stoType = result.Select(p => p.LocationS).Distinct().ToArray();
            if (stoType == null || stoType.Length != 1)
            {
                return "失败;物料信息不属于同一个位置";
            }
            string locationType = stoType[0];//MFG3/PKG3

            //筛选ZRAW/ZPKG,ZLBS/ZSFG
            string[] ClassType = result.Select(p => p.ClassCode).Distinct().ToArray();
            if (ClassType == null)
            {
                return "失败;物料类型不存在";
            }
            if (ClassType.Length != 1)
            {
                if (ClassType.Where(p => p.Contains("ZRAW") || p.Contains("ZSFG")).Count() > 0)
                {
                    return "失败;物料类型不匹配;";
                }
            }
            string mType = string.Empty;

            for (int i = 0; i < ClassType.Length; i++)
            {
                mType += ClassType[i] + ";";
            }
            //批次
            string[] lotType = result.Select(p => p.StatusFl).Distinct().ToArray();
            if (lotType == null || lotType.Length != 1)
            {
                return "失败;批次状态不统一";
            }
            //当前批次状态
            string nowState = lotType[0];//U/B
            //子批次
            string[] subType = result.Select(p => p.StatusSl).Distinct().ToArray();

            //U/B
            if (nowState == "U")
            {
                //不相等提示
                if (subType.Where(P => P.Contains("U")).Count() != subType.Length)
                {
                    return "失败;批次状态和子批次状态不统一";
                }
            }
            else
            {
                if (subType.Where(P => P.Contains("U")).Count() > 0)
                {
                    return "失败;批次状态和子批次状态不统一";
                }
            }
            string subTypes = string.Empty;
            for (int i = 0; i < subType.Length; i++)
            {
                subTypes += subType[i] + ";";
            }

            //这里判断物料的类型
            if (mType.Contains("ZRAW"))
            {
                //判断输入哪个规则 P1/P2/P3/P4/P5
                if (locationType == "MFG3")
                {
                    if (nowState == "U" && subTypes.Contains("U") == true)
                    {
                        return "311;RS01Location";
                    }
                    else if (nowState == "B" && (subTypes.Contains("Q") == true || subTypes.Contains("B") == true))
                    {
                        return "325;RSBKLocation";
                    }
                }
                return "失败，不满足规则ZRAW-MFG3- U/或B/Q";
            }
            else if (mType.Contains("ZSFG"))
            {
                //判断输入哪个规则 P1/P2/P3/P4/P5
                if (locationType == "MFG3")
                {
                    if (nowState == "B" && (subTypes.Contains("Q") == true || subTypes.Contains("B") == true))
                    {
                        return "325;WP04QALocation";
                    }
                    else if (nowState == "U" && subTypes.Contains("U") == true)
                    {
                        return "311;WP04Location";
                    }
                }
                return "失败，不满足规则ZSFG-MFG3- U/或B/Q";
            }
            else if (mType.Contains("ZPKG") || mType.Contains("ZLBS"))
            {
                //判断输入哪个规则 P1/P2/P3/P4/P5
                if (locationType == "PKG3")
                {
                    if (nowState == "U" && subTypes.Contains("U") == true)
                    {
                        return "311;RS01Location";
                    }
                    else if (nowState == "B" && (subTypes.Contains("Q") == true || subTypes.Contains("B") == true))
                    {
                        return "325;RSBKLocation";
                    }
                }
                return "失败，不满足规则ZPKG/ZLBS-PKG3- U/或B/Q";
            }
            return "失败，当前物料类型为" + mType;
        }

        public async Task<string> GetStateByID(string lotID, string subLotID)
        {

            if (string.IsNullOrEmpty(lotID))
            {
                return "不存在批次信息";
            }
            var lotResult = await _dalMaterialLotEntity.FindEntity(p => p.ID == lotID);
            if (lotResult == null)
            {
                return "不存在批次信息";
            }
            if (lotResult.ExternalStatus != "2")
            {
                return "批次状态为锁定，请先解锁";
            }
            string subID = subLotID;
            if (string.IsNullOrEmpty(subID))
            {
                return "不存在子批次信息";
            }
            var subLotResult = await _dalMaterialSubLotEntity.FindEntity(p => p.ID == subID);
            if (subLotResult == null)
            {
                return "不存在子批次信息";
            }
            if (subLotResult.ExternalStatus != "3")
            {
                if (subLotResult.ExternalStatus == "2")
                {
                    return "子批次状态为质检，请先解锁";
                }
                return "子批次状态为锁定，请先解锁";
            }
            return "";
        }

        /// <summary>
        /// 获取库存分组出来
        /// </summary>
        /// <param name="tranModel"></param>
        /// <returns></returns>
        public async Task<List<InventorylistingViewEntity>> GetInvnetGroup(TransferModel tranModel)
        {
            RefAsync<int> dataCount = 0;
            var data = await _dal.Db.Queryable<InventorylistingViewEntity>().In(p => p.ID, tranModel.ID).OrderByDescending(p => p.CreateDate).ToListAsync();
            List<InventorylistingViewEntity> result = (from a in data
                                                       group a by new
                                                       {
                                                           Suppiername = a.Suppiername,
                                                           MaterialCode = a.MaterialCode,
                                                           MaterialName = a.MaterialName,
                                                           BatchId = a.BatchId,
                                                           MaxUnit = a.MaxUnit,
                                                           MinUnit = a.MinUnit,
                                                           LocationS = a.LocationS,

                                                       } into g
                                                       select new InventorylistingViewEntity
                                                       {
                                                           Suppiername = g.Key.Suppiername,
                                                           MaterialCode = g.Key.MaterialCode,
                                                           MaterialName = g.Key.MaterialName,
                                                           BatchId = g.Key.BatchId,
                                                           MaxUnit = g.Key.MaxUnit,
                                                           MinUnit = g.Key.MinUnit,
                                                           LocationS = g.Key.LocationS,
                                                           Quantity = g.Sum(p => p.Quantity)
                                                       }).ToList();
            return result;
        }

        #region 获取属性

        /// <summary>
        /// 根据节点属性查询是否包含
        /// </summary>
        /// <param name="equipmentID"></param>
        /// <param name="Property"></param>
        /// <returns></returns>
        public async Task<bool> GetEquipmentPropertyInclude(string equipmentID, string Property)
        {

            MessageModel<EquipmentAllDataModel> apiResult_equipmentAllData = await HttpHelper.PostAsync<EquipmentAllDataModel>("DFM", "api/Equipment/GetEquipmentAllData?EquipmentId=" + equipmentID, _user.GetToken(), new { });
            var equipmentAllData = apiResult_equipmentAllData.response;
            if (equipmentAllData == null)
            {
                return false;
            }
            var item = equipmentAllData.EquipmentFunctionPropertyList?.Find(x => x.FunctionCode == Property);

            if (item == null)
            {

                return false;
            }

            return true;

        }


        #endregion

    }
}