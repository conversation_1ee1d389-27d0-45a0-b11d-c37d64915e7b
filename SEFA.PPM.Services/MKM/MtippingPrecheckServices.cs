

using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels.PTM;
using System;
using SEFA.Base.Common.HttpContextUser;
using SEFA.PPM.IServices.PTM;
using SEFA.PPM.Services.PTM;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using Newtonsoft.Json.Linq;
using SEFA.PTM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels;
using System.Linq;
using System.Drawing.Drawing2D;

namespace SEFA.MKM.Services
{
    public class MtippingPrecheckServices : BaseServices<MtippingPrecheckEntity>, IMtippingPrecheckServices
    {
        private readonly IBaseRepository<MtippingPrecheckEntity> _dal;
        private readonly IBaseRepository<MtippingPrecheckViewEntity> _MtippingPrecheckViewEntity;

        private readonly IBaseRepository<BatchEntity> _dal2;
        private readonly IBaseRepository<ContainerHistoryEntity> _dal3;
        private readonly IBaseRepository<MaterialInventoryEntity> _dal4;
        private readonly IBaseRepository<TPrecheckDetailViewEntity> _daTPrecheckDetailViewEntity;
        private readonly IBaseRepository<ContainerEntity> _dal5;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IUser _user;
        private readonly IProcessDataViewServices _processDataViewServices;

        public MtippingPrecheckServices(IBaseRepository<MtippingPrecheckEntity> dal, IBaseRepository<MtippingPrecheckViewEntity> mtippingPrecheckViewEntity, IBaseRepository<BatchEntity> dal2, IBaseRepository<ContainerHistoryEntity> dal3, IBaseRepository<MaterialInventoryEntity> dal4, IBaseRepository<ContainerEntity> dal5, IUnitOfWork unitOfWork, IUser user, IProcessDataViewServices processDataViewServices, IBaseRepository<TPrecheckDetailViewEntity> daTPrecheckDetailViewEntity)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _MtippingPrecheckViewEntity = mtippingPrecheckViewEntity;
            this._dal2 = dal2;
            this._dal3 = dal3;
            this._dal4 = dal4;
            this._dal5 = dal5;
            base.BaseDal = dal;
            _unitOfWork = unitOfWork;
            _user = user;
            _processDataViewServices = processDataViewServices;
            _daTPrecheckDetailViewEntity = daTPrecheckDetailViewEntity;
        }

        #region 使用函数

        #region 查询
        public async Task<List<MtippingPrecheckEntity>> GetList(MtippingPrecheckRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<MtippingPrecheckEntity>()
               //.AndIF(!string.IsNullOrEmpty(reqModel.Status), a => a.Status.Equals(reqModel.Status))
               .AndIF(!string.IsNullOrEmpty(reqModel.Name), a => a.Name.Contains(reqModel.Name))
               .AndIF(!string.IsNullOrEmpty(reqModel.LineName), a => a.Name.Contains(reqModel.LineName))
               .AndIF(!string.IsNullOrEmpty(reqModel.BatchCode), a => a.BatchCode.Contains(reqModel.BatchCode))
               .AndIF(!string.IsNullOrEmpty(reqModel.ProductionOrderNo), a => a.ProductionOrderNo.Contains(reqModel.ProductionOrderNo))
               .AndIF(!string.IsNullOrEmpty(reqModel.Machine), a => a.ProductionOrderNo.Contains(reqModel.Machine))
               .AndIF(reqModel.StartTime.HasValue, a => a.Reviewtime >= reqModel.StartTime.Value)
               .AndIF(reqModel.EndTime.HasValue, a => a.Reviewtime <= reqModel.EndTime.Value)
               .AndIF(reqModel.ShowCompleted == true, a => Convert.ToInt32(a.PrepStatus) >= 7)
               .AndIF(reqModel.ShowCompleted == false, a => Convert.ToInt32(a.PrepStatus) >= 4 && Convert.ToInt32(a.PrepStatus) < 7)
               .ToExpression();
            var data = await _dal.Db.Queryable<MtippingPrecheckEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        public async Task<PageModel<MtippingPrecheckEntity>> GetPageList(MtippingPrecheckRequestModel reqModel)
        {
            PageModel<MtippingPrecheckEntity> result = new PageModel<MtippingPrecheckEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<MtippingPrecheckEntity>()
               .AndIF(!string.IsNullOrEmpty(reqModel.Name), a => a.Name.Contains(reqModel.Name))
               .AndIF(!string.IsNullOrEmpty(reqModel.LineName), a => a.Name.Contains(reqModel.LineName))
               .AndIF(!string.IsNullOrEmpty(reqModel.BatchCode), a => a.BatchCode.Contains(reqModel.BatchCode))
               .AndIF(!string.IsNullOrEmpty(reqModel.ProductionOrderNo), a => a.ProductionOrderNo.Contains(reqModel.ProductionOrderNo))
               .AndIF(!string.IsNullOrEmpty(reqModel.Machine), a => a.ProductionOrderNo.Contains(reqModel.Machine))
               .AndIF(reqModel.StartTime.HasValue, a => a.PlanStartTime >= reqModel.StartTime.Value)
               .AndIF(reqModel.EndTime.HasValue, a => a.PlanStartTime <= reqModel.EndTime.Value)
               .AndIF(reqModel.ShowCompleted == true, a => Convert.ToInt32(a.PrepStatus) >= 4 && Convert.ToInt32(a.PrepStatus) <= 9)
               .AndIF(reqModel.ShowCompleted == false, a => Convert.ToInt32(a.PrepStatus) <= 3)
               .ToExpression();
            var data = await _dal.Db.Queryable<MtippingPrecheckEntity>()
                .Where(whereExpression).OrderBy(a => a.LineCode).OrderBy(p => p.PlanStartTime).OrderBy(a => a.ProductionOrderNo)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        #endregion

        #region Action

        /// <summary>
        /// 预检查扫描备料大标签
        /// </summary>
        /// <param name="containerCode"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> ScanContainerCode(string containerCode)
        {
            var result = new MessageModel<string>
            {
                msg = "操作失败！",
                success = false
            };
            string code = string.Empty;
            var containerEntity = await _dal5.FindEntity(x => x.Name == containerCode);
            if (containerEntity == null)
            {
                result.msg = "未找到ContainerCode";
                return result;
            }
            BatchEntity batchEntity = await _dal2.FindEntity(containerEntity.ProductionBatchId);
            if (batchEntity == null)
            {
                result.msg = "未找到BatchId";
                return result;
            }
            result.response = batchEntity.ID;
            //var messageModel = await _processDataViewServices.GetLastProcessData(batchEntity.MaterialVersionId, null);
            ////var processData = messageModel.response;
            //if (processData == null)
            //{
            //    result.msg = "通过MaterialVersionId未找到ProcessData";
            //    return result;
            //}
            //if (processData.Status == "3")
            //{
            //    result.msg = "长文本状态为Pending_Release";
            //    //result.response = batchEntity.MaterialVersionId;
            //    return result;
            //}
            int.TryParse(batchEntity.PrepStatus, out int status);
            _unitOfWork.BeginTran();
            try
            {
                if (status == 3)
                {
                    //更新Batch表PrepStatus
                    //  batchEntity.PrepStatus = "3";//START_PRECHECK
                    batchEntity.Modify(batchEntity.ID, _user.Name.ToString());
                    await _dal2.Update(batchEntity);
                    _unitOfWork.CommitTran();
                }
                else if (status < 3)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "未完成拼锅";
                    return result;
                }
                else if (status > 9&& status<12)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "未完成拼锅";
                    return result;
                }
                else if (status == 12)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "未完成拼锅";
                    return result;
                }
                else if (status > 4)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "备料后核对已完毕";
                    return result;
                }
                result.msg = "跳转成功！";
                result.success = true;
            }
            catch (Exception ex)
            {
                result.msg = ex.Message;
                _unitOfWork.RollbackTran();
            }
            return result;
        }

        #endregion


        #endregion

        #region 使用函数View


        public async Task<List<MtippingPrecheckViewEntity>> GetListView(MtippingPrecheckViewRequestModel reqModel)
        {
            string PrepStatus = string.Empty;
            var conMode = await _dal5.FindEntity(p => p.Name.Contains(reqModel.ContainerName));
            if (conMode != null)
            {
                if (!string.IsNullOrEmpty(conMode.ProductionBatchId))
                {
                    var batchData = await _dal2.FindEntity(p => p.ID == conMode.ProductionBatchId);
                    PrepStatus = batchData.PrepStatus;
                }
            }
            var whereExpression = Expressionable.Create<MtippingPrecheckViewEntity>()
           .AndIF(!string.IsNullOrEmpty(reqModel.Status), a => a.Status.Equals(reqModel.Status))
           .AndIF(!string.IsNullOrEmpty(reqModel.ContainerName), a => a.ContainerName.Equals(reqModel.ContainerName))
           //.AndIF(!string.IsNullOrEmpty(reqModel.BatchId), a => a.Status.Equals(reqModel.BatchId))
           .ToExpression();
            var data = await _MtippingPrecheckViewEntity.Db.Queryable<MtippingPrecheckViewEntity>()
                .Where(whereExpression).ToListAsync();

            if (PrepStatus == "4" || PrepStatus == "6" || PrepStatus == "7" || PrepStatus == "8" || PrepStatus == "9")
            {
                for (int i = 0; i < data.Count; i++)
                {
                    data[i].Status = "1";
                }
            }
            return data.OrderBy(p => p.Status).ThenBy(p => p.ModifyDate).ToList();
        }
        public async Task<List<TPrecheckDetailViewEntity>> GetListDetial(MtippingPrecheckViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<TPrecheckDetailViewEntity>()
            .AndIF(!string.IsNullOrEmpty(reqModel.Status), a => a.Status.Equals(reqModel.Status))
            .AndIF(!string.IsNullOrEmpty(reqModel.ContainerName), a => a.ContainerName.Equals(reqModel.ContainerName))
            //.AndIF(!string.IsNullOrEmpty(reqModel.BatchId), a => a.Status.Equals(reqModel.BatchId))
               .ToExpression();
            var data = await _dal.Db.Queryable<TPrecheckDetailViewEntity>()
                .Where(whereExpression).ToListAsync();
            return data.OrderBy(p => p.Status).ThenBy(p => p.ModifyDate).ToList(); ;
        }

        public async Task<MessageModel<string>> GetCount(MtippingPrecheckViewRequestModel reqModel)
        {
            var result = new MessageModel<string>
            {
                msg = "获取成功！",
                success = true,
                response = "0/0"
            };
            try
            {
                var whereExpression = Expressionable.Create<MtippingPrecheckViewEntity>()
               .AndIF(!string.IsNullOrEmpty(reqModel.ContainerName), a => a.ContainerName.Equals(reqModel.ContainerName))
               //.AndIF(!string.IsNullOrEmpty(reqModel.BatchId), a => a.Status.Equals(reqModel.BatchId))
               .ToExpression();
                var data = await _MtippingPrecheckViewEntity.Db.Queryable<MtippingPrecheckViewEntity>()
                    .Where(whereExpression).ToListAsync();
                var allCount = data?.Count ?? 0;
                var isCheckCount = data?.FindAll(x => x.Status == "1")?.Count ?? 0;

                //这里判断当前容器的批次状态
                var conMode = await _dal5.FindEntity(p => p.Name.Contains(reqModel.ContainerName));
                if (conMode != null)
                {
                    if (!string.IsNullOrEmpty(conMode.ProductionBatchId))
                    {
                        var batchData = await _dal2.FindEntity(p => p.ID == conMode.ProductionBatchId);
                        if (batchData.PrepStatus == "4" || batchData.PrepStatus == "6" || batchData.PrepStatus == "7" || batchData.PrepStatus == "8" || batchData.PrepStatus == "9")
                        {
                            isCheckCount = allCount;
                        }
                    }
                }
                else
                {

                }

                result.response = isCheckCount + "/" + allCount;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = ex.Message;
                throw;
            }
            return result;
        }

        public async Task<PageModel<MtippingPrecheckViewEntity>> GetPageListView(MtippingPrecheckViewRequestModel reqModel)
        {
            PageModel<MtippingPrecheckViewEntity> result = new PageModel<MtippingPrecheckViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<MtippingPrecheckViewEntity>()
               .AndIF(!string.IsNullOrEmpty(reqModel.Status), a => a.Status.Equals(reqModel.Status))
               .AndIF(!string.IsNullOrEmpty(reqModel.ContainerName), a => a.Status.Equals(reqModel.ContainerName))
               //.AndIF(!string.IsNullOrEmpty(reqModel.BatchId), a => a.Status.Equals(reqModel.BatchId))
               .ToExpression();
            var data = await _MtippingPrecheckViewEntity.Db.Queryable<MtippingPrecheckViewEntity>()
                .Where(whereExpression).OrderByDescending(x => x.CreateDate)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        /// <summary>
		/// 预检查操作：通过key值判断执行不同逻辑 key=1开始预检查,key=2扫描库存
		/// Body需要根据key传不同格式的数据
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> Precheck(ConsolPoRequestModel reqModel)
        {
            var result = new MessageModel<string>
            {
                success = false
            };
            string batchId = string.Empty;

            JObject obj;
            try
            {
                obj = JObject.Parse(reqModel.Body);
                batchId = obj["BatchId"].ToString();
            }
            catch (Exception ex)
            {
                result.msg = "缺少参数[BatchId]";
                return result;
            }
            BatchEntity entity = await _dal2.FindEntity(batchId);
            bool updateBatch = false;
            if (entity == null)
            {
                result.msg = "BatchId未找到";
                return result;
            }
            //写入容器记录表
            ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
            switch (reqModel.Key)
            {
                //1开始预检查
                case "1":
                    updateBatch = true;
                    //更新Batch表PrepStatus
                    entity.PrepStatus = "3";//START_PRECHECK
                                            //hisModel.ContainerId = obj["ContainerId"].ToString();
                                            //hisModel.Type = "Inventory pre-check";
                                            //hisModel.Comment = "Inventory pre-check Start";
                                            //hisModel.EquipmentId = obj["EquipmentId"].ToString();
                                            //hisModel.EquipmentRequirementId = obj["EquipmentRequirementId"].ToString();
                                            //hisModel.State = "";
                                            //hisModel.MaterialId = obj["MaterialId"].ToString();
                                            //hisModel.SublotId = obj["SublotId"].ToString();
                                            //hisModel.Quantity = obj["Quantity"].ToString();
                                            //hisModel.QuantityUomId = obj["QuantityUomId"].ToString();
                                            //hisModel.LotId = obj["LotId"].ToString();
                                            //hisModel.ExpirationDate = DateTime.Parse(obj["ExpirationDate"].ToString());
                    break;
                //2扫描库存
                case "2":
                    string traceCode = string.Empty;
                    try
                    {
                        traceCode = obj["TraceCode"].ToString();
                    }
                    catch (Exception ex)
                    {
                        result.msg = "缺少参数[TraceCode]";
                        return result;
                    }

                    var precheckViewEntity = await _MtippingPrecheckViewEntity.FindEntity(x => x.BatchId == batchId && x.Tracecode == traceCode);
                    if (precheckViewEntity == null)
                    {
                        result.msg = "该批次下未找到此TraceCode：" + traceCode;
                        return result;
                    }
                    MaterialInventoryEntity inventoryModel = await _dal4.FindEntity(precheckViewEntity.ID);
                    hisModel.Type = "Inventory pre-check";
                    hisModel.Comment = "Inventory pre-check Done";
                    hisModel.ContainerId = inventoryModel.ContainerId;
                    //hisModel.EquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    //hisModel.MaterialId = inventoryModel.MaterialId;
                    hisModel.SublotId = inventoryModel.SublotId;
                    hisModel.Quantity = inventoryModel.Quantity.ToString();
                    hisModel.QuantityUomId = inventoryModel.QuantityUomId;
                    hisModel.LotId = inventoryModel.LotId;
                    //hisModel.ExpirationDate = inventoryModel.ExpirationDate;
                    var data = await _MtippingPrecheckViewEntity.Db.Queryable<MtippingPrecheckViewEntity>()
                    .Where(x => x.BatchId == batchId && x.SublotId != inventoryModel.SublotId && x.Status == "0").ToListAsync();
                    if (data.Count == 0)
                    {
                        updateBatch = true;
                        entity.PrepStatus = "4";//PRECHECK_DONE
                    }
                    break;
                default:
                    break;
            }
            _unitOfWork.BeginTran();
            try
            {
                if (updateBatch)
                {
                    entity.Modify(entity.ID, _user.Name.ToString());
                    await _dal2.Update(entity);
                }
                hisModel.CreateCustomGuid(_user.Name);
                await _dal3.Add(hisModel);
                result.success = true;
                _unitOfWork.CommitTran();
            }
            catch (Exception ex)
            {
                result.msg = ex.Message;
                _unitOfWork.RollbackTran();
            }
            return result;
        }

        /// <summary>
        /// 预检查扫描库存
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> ScanTraceCode(string traceCode)
        {
            var result = new MessageModel<string>
            {
                msg = "操作失败！",
                success = false
            };
            var precheckViewEntity = await _MtippingPrecheckViewEntity.FindEntity(x => x.Tracecode == traceCode);
            if (precheckViewEntity == null)
            {
                result.msg = "未找到子批次：" + traceCode;
                return result;
            }
            BatchEntity batchEntity = await _dal2.FindEntity(precheckViewEntity.BatchId);
            if (batchEntity == null)
            {
                result.msg = "未找到BatchId";
                return result;
            }
            if (precheckViewEntity.Status == "1")
            {
                result.msg = "已复检，无需重复提交";
                return result;
            }
            //写入容器记录表
            MaterialInventoryEntity inventoryModel = await _dal4.FindEntity(precheckViewEntity.ID);
            ContainerHistoryEntity hisModel = new()
            {
                Type = "Inventory double_checked",
                Comment = "Inventory double_checked Done",
                ContainerId = inventoryModel.ContainerId,
                //hisModel.EquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //hisModel.MaterialId = inventoryModel.MaterialId;
                SublotId = inventoryModel.SublotId,
                Quantity = inventoryModel.Quantity.ToString(),
                QuantityUomId = inventoryModel.QuantityUomId,
                LotId = inventoryModel.LotId
            };
            //hisModel.ExpirationDate = inventoryModel.ExpirationDate;
            var data = await _MtippingPrecheckViewEntity.Db.Queryable<MtippingPrecheckViewEntity>()
            .Where(x => x.BatchId == precheckViewEntity.BatchId && x.SublotId != inventoryModel.SublotId && x.Status == "0").ToListAsync();
            _unitOfWork.BeginTran();
            try
            {
                //插入容器记录表
                hisModel.CreateCustomGuid(_user.Name.ToString());
                bool rsult = await _dal3.Add(hisModel) > 0;

                if (rsult == false)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "操作失败";
                    return result;
                }
                if (data.Count == 0)
                {
                    //所有库存都检查完，更新批次投料状态
                    batchEntity.PrepStatus = "4";//PRECHECK_DONE
                    batchEntity.Modify(batchEntity.ID, _user.Name);
                    await _dal2.Update(batchEntity);
                }
                result.msg = "操作成功！";
                result.success = true;
                _unitOfWork.CommitTran();
            }
            catch (Exception ex)
            {
                result.msg = ex.Message;
                _unitOfWork.RollbackTran();
            }
            return result;
        }

        #endregion

        public async Task<bool> SaveForm(MtippingPrecheckEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}