
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.PPM.Services
{
    public class TransferbinViewServices : BaseServices<TransferbinViewEntity>, ITransferbinViewServices
    {
        private readonly IBaseRepository<TransferbinViewEntity> _dal;
        public TransferbinViewServices(IBaseRepository<TransferbinViewEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<TransferbinViewEntity>> GetListALL()
        {
            List<TransferbinViewEntity> result = new List<TransferbinViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<TransferbinViewEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<TransferbinViewEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        public async Task<List<TransferbinViewEntity>> GetList(TransferbinViewRequestModel reqModel)
        {
            List<TransferbinViewEntity> result = new List<TransferbinViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<TransferbinViewEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<TransferbinViewEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        public async Task<PageModel<TransferbinViewEntity>> GetPageList(TransferbinViewRequestModel reqModel)
        {
            PageModel<TransferbinViewEntity> result = new PageModel<TransferbinViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<TransferbinViewEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<TransferbinViewEntity>()
                .Where(whereExpression)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        public async Task<bool> SaveForm(TransferbinViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}