
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.MKM.Services
{
    public class MEquipmentroomViewServices : BaseServices<MEquipmentroomViewEntity>, IMEquipmentroomViewServices
    {
        private readonly IBaseRepository<MEquipmentroomViewEntity> _dal;
        public MEquipmentroomViewServices(IBaseRepository<MEquipmentroomViewEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<MEquipmentroomViewEntity>> GetList(MEquipmentroomViewRequestModel reqModel)
        {
            List<MEquipmentroomViewEntity> result = new List<MEquipmentroomViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<MEquipmentroomViewEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<MEquipmentroomViewEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        public async Task<List<MEquipmentroomViewEntity>> GetSelectList()
        {
            List<MEquipmentroomViewEntity> result = new List<MEquipmentroomViewEntity>();
            RefAsync<int> dataCount = 0;
            var data = await _dal.Db.Queryable<MEquipmentroomViewEntity>().ToListAsync();               
            return data;
        }

        public async Task<PageModel<MEquipmentroomViewEntity>> GetPageList(MEquipmentroomViewRequestModel reqModel)
        {
            PageModel<MEquipmentroomViewEntity> result = new PageModel<MEquipmentroomViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<MEquipmentroomViewEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<MEquipmentroomViewEntity>()
                .Where(whereExpression)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        public async Task<bool> SaveForm(MEquipmentroomViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}