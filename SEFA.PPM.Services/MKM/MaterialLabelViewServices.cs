
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.ViewModels.MKM.View;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.IRepository.UnitOfWork;
using System.Linq;
using SEFA.MKM.Model.Models;
using SEFA.Base.Common.Common;
using SEFA.MKM.Services;
using System;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Information;

namespace SEFA.PPM.Services
{
    public class MaterialLabelViewServices : BaseServices<MaterialLabelViewEntity>, IMaterialLabelViewServices
    {
        private readonly IBaseRepository<MaterialLabelViewEntity> _dal;
        private readonly IBaseRepository<DFM.Model.Models.MaterialGroupMappingEntity> _dalMaterialGroupMappingEntity;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IUser _uIser;
        private readonly IBaseRepository<DFM.Model.Models.EquipmentMaterialEntity> _dalEquipmentMaterialEntity;
        private readonly IBaseRepository<MaterialPropertyValueEntity> _MaterialPropertyValueEntityDal;
        public MaterialLabelViewServices(IBaseRepository<MaterialLabelViewEntity> dal, IBaseRepository<DFM.Model.Models.MaterialGroupMappingEntity> dalMaterialGroupMappingEntity, IUnitOfWork unitOfWork, IUser user, IBaseRepository<DFM.Model.Models.EquipmentMaterialEntity> dalEquipmentMaterialEntity, IBaseRepository<MaterialPropertyValueEntity> materialPropertyValueEntityDal)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _dalMaterialGroupMappingEntity = dalMaterialGroupMappingEntity;
            _unitOfWork = unitOfWork;
            _uIser = user;
            _dalEquipmentMaterialEntity = dalEquipmentMaterialEntity;
            _MaterialPropertyValueEntityDal = materialPropertyValueEntityDal;
        }

        public async Task<List<MaterialLabelViewEntity>> GetList(MaterialLabelViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<MaterialLabelViewEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<MaterialLabelViewEntity>> GetPageList(MaterialLabelViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<MaterialLabelViewEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);

            return data;
        }

        public async Task<bool> SaveForm(MaterialLabelViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }

        #region 新增函数



        #region 关联筛选数据


        /// <summary>
        /// 筛选当前节点下可用物料 02406241-3593-4874-163e-0370f6000000
        /// </summary>
        /// <param name="eqpID"></param>
        /// <returns></returns>
        public async Task<List<MpModel>> Get_DisMaterial(string eqpID)
        {
            List<MpModel> list = new List<MpModel>();
            ///查询数据
            var eqmMaterial = await _dalEquipmentMaterialEntity.FindList(P => P.EquipmentId == eqpID);

            if (eqmMaterial != null && eqmMaterial.Count > 0)
            {
                for (int i = 0; i < eqmMaterial.Count; i++)
                {
                    string mID = eqmMaterial[i].MaterialId;

                    if (string.IsNullOrEmpty(mID))
                    {
                        continue;
                    }

                    MpModel model = new MpModel();
                    model.MaterialID = mID;
                    model.EquipmentId = eqmMaterial[i].EquipmentId;
                    list.Add(model);
                }
            }

            var materialGropu = await _dalMaterialGroupMappingEntity.FindList(p => p.ID != null);

            List<MpModel> result = (from a in eqmMaterial
                                    join b in materialGropu on a.GroupId equals b.MaterialGroupId
                                    where a.Type == "Include" && a.GroupId != null && a.GroupId != ""
                                    group a by new
                                    {
                                        a.EquipmentId,
                                        b.MaterialId
                                    } into g
                                    select new MpModel
                                    {
                                        EquipmentId = g.Key.EquipmentId,
                                        MaterialID = g.Key.MaterialId

                                    }).ToList();

            //合并
            list.AddRange(result);
            //去重复
            list = list.Distinct().ToList();

            return list;
        }

        #endregion

        #region 明细查询

        //工单明细分组后数据
        public async Task<PageModel<MaterialLabelViewEntity>> GetPageListByProId(MaterialLabelViewRequestModel reqModel)
        {
            List<MpModel> mpList = await Get_DisMaterial(reqModel.EquipmentID);

            string[] mIDs = mpList.Select(mp => mp.MaterialID).ToArray();
            if (mIDs == null || mIDs.Length <= 0)

            {
                return new PageModel<MaterialLabelViewEntity>();
            }
            PageModel<MaterialLabelViewEntity> result = new PageModel<MaterialLabelViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<MaterialLabelViewEntity>().AndIF(!string.IsNullOrEmpty(reqModel.ProductionOrderId), p => p.ProductionOrderId == reqModel.ProductionOrderId)
                             .ToExpression();
            //获取数据源
            var datas = await _dal.Db.Queryable<MaterialLabelViewEntity>().Where(whereExpression).In(p => p.MId, mIDs).ToListAsync();

            List<MaterialLabelViewEntity> data = new List<MaterialLabelViewEntity>();

            //循环所有数据源
            int f_bags = 0;
            int p_bags = 0;
            for (int i = 0; i < datas.Count; i++)
            {
                //总包数（总的包数）
                f_bags = Convert.ToInt32(datas[i].FullPage);

                for (int j = 0; j < f_bags; j++)
                {
                    MaterialLabelViewEntity model = new MaterialLabelViewEntity();
                    model.BatchId = datas[i].BatchId;
                    model.UId = datas[i].UId;
                    model.ProductionOrderId = datas[i].ProductionOrderId;
                    model.ProductionOrderNo = datas[i].ProductionOrderNo;
                    model.BNumber = datas[i].BNumber;
                    model.ExdateQty = datas[i].ExdateQty;
                    model.MId = datas[i].MId;
                    model.MName = datas[i].MName;
                    model.MCode = datas[i].MCode;
                    model.TagerQty = datas[i].TagerQty;
                    model.UintName = datas[i].UintName;
                    model.BagSize = datas[i].BagSize;
                    model.LotCode = datas[i].LotCode;
                    model.InventQty = datas[i].InventQty;
                    model.FullPage = "1";
                    model.InventPqty = datas[i].InventPqty;
                    model.SubLotcode = datas[i].SubLotcode;
                    data.Add(model);
                }

                //单包数量（单包的数量）
                p_bags = Convert.ToDecimal(datas[i].InventPqty) > 0 ? 1 : 0;
                for (int j = 0; j < p_bags; j++)
                {
                    MaterialLabelViewEntity model = new MaterialLabelViewEntity();
                    model.BatchId = datas[i].BatchId;
                    model.UId = datas[i].UId;
                    model.ProductionOrderId = datas[i].ProductionOrderId;
                    model.ProductionOrderNo = datas[i].ProductionOrderNo;
                    model.BNumber = datas[i].BNumber;
                    model.ExdateQty = datas[i].ExdateQty;
                    model.MId = datas[i].MId;
                    model.MName = datas[i].MName;
                    model.MCode = datas[i].MCode;
                    model.TagerQty = datas[i].TagerQty;
                    model.UintName = datas[i].UintName;
                    model.BagSize = datas[i].BagSize;
                    model.LotCode = datas[i].LotCode;
                    model.InventQty = datas[i].InventPqty.ToString();
                    model.FullPage = "1";
                    model.InventPqty = datas[i].InventPqty;
                    model.SubLotcode = datas[i].SubLotcode;
                    data.Add(model);
                }
            }

            result.dataCount = dataCount;
            result.data = data;
            return result;
        }


        public async Task<PageModel<RequestDetailReques>> GetPageListByProIds(MaterialLabelViewRequestModel reqModel)
        {

            //这里执行转换proID      
            var proidS = reqModel.ProIDS.Split(',').ToArray();

            //for (int i = 0; i < arrList.Length; i++)
            //{
            //    if (!string.IsNullOrEmpty(arrList[i])) 
            //    {
            //        p
            //    }
            //}

            if (proidS == null || proidS.Length == 0)
            {
                return new PageModel<RequestDetailReques>();
            }

            List<MpModel> mpList = await Get_DisMaterial(reqModel.EquipmentID);

            string[] mIDs = mpList.Select(mp => mp.MaterialID).ToArray();
            if (mIDs == null || mIDs.Length <= 0)

            {
                return new PageModel<RequestDetailReques>();
            }

            //mIDs = _MaterialEntitydal.Db.Queryable<DFM.Model.Models.MaterialEntity>().In(p => p.ID, mIDs).Select(mp => mp.ID).ToArray();
            mIDs = _MaterialPropertyValueEntityDal.Db.Queryable<MaterialPropertyValueEntity>().In(p => p.MaterialId, mIDs).
               Where(p => p.PropertyCode == "PrepByPO" && p.PropertyValue == "1")
                .Select(mp => mp.MaterialId).ToArray();
            if (mIDs == null || mIDs.Length <= 0)
            {
                return new PageModel<RequestDetailReques>();
            }
            PageModel<RequestDetailReques> result = new PageModel<RequestDetailReques>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<MaterialLabelViewEntity>().AndIF(!string.IsNullOrEmpty(reqModel.ProductionOrderId), p => p.ProductionOrderId == reqModel.ProductionOrderId)
                             .ToExpression();
            //获取数据源
            var dataResult = await _dal.Db.Queryable<MaterialLabelViewEntity>().Where(whereExpression).In(p => p.ProductionOrderId, proidS).In(p => p.MId, mIDs).ToListAsync();

            List<RequestDetailReques> data = new List<RequestDetailReques>();

            //循环所有数据源
            int f_bags = 0;
            int p_bags = 0;

            //去重复工单
            string[] proIDS = dataResult.Select(P => P.ProductionOrderId).Distinct().Where(p => !string.IsNullOrWhiteSpace(p)).ToArray();
            if (proIDS == null || proIDS.Length <= 0)
            {
                return new PageModel<RequestDetailReques>();
            }
            //循环工单（添加序号操作）
            for (int x = 0; x < proIDS.Length; x++)
            {
                string proID = proIDS[x];
                //按照工单来执行数据(筛选工单来排序)
                var searchDatas = dataResult.Where(P => P.ProductionOrderId == proID).OrderBy(p => p.Sapformula).ThenBy(p => p.TagerQty).ThenBy(p => p.TagerQty).ThenBy(p => p.MCode).ToList();
                int xuNumber = 0;

                #region 执行循环给数据操作

                for (int i = 0; i < searchDatas.Count; i++)
                {
                    //总包数(整袋)
                    f_bags = Convert.ToInt32(searchDatas[i].FullPage);
                    for (int j = 0; j < f_bags; j++)
                    {
                        xuNumber++;
                        RequestDetailReques model = new RequestDetailReques();
                        model.id = Guid.NewGuid().ToString();
                        model.Xnumber = xuNumber;
                        model.BatchId = searchDatas[i].BatchId;
                        model.UId = searchDatas[i].UId;
                        model.ProductionOrderId = searchDatas[i].ProductionOrderId;
                        model.ProductionOrderNo = searchDatas[i].ProductionOrderNo;
                        model.BNumber = searchDatas[i].BNumber;
                        model.ExdateQty = searchDatas[i].ExdateQty;
                        model.MId = searchDatas[i].MId;
                        model.MName = searchDatas[i].MName;
                        model.MCode = searchDatas[i].MCode;
                        model.TagerQty = searchDatas[i].TagerQty;
                        model.UintName = searchDatas[i].UintName;
                        model.BagSize = searchDatas[i].BagSize;
                        model.LotCode = reqModel.LotCode;
                        model.InventQty = searchDatas[i].InventQty;
                        model.FullPage = "1";
                        model.InventPqty = searchDatas[i].InventPqty;
                        model.SubLotcode = searchDatas[i].SubLotcode;
                        model.Sapformula = searchDatas[i].Sapformula;
                        data.Add(model);
                    }

                    //单包数量（单包）
                    p_bags = Convert.ToDecimal(searchDatas[i].InventPqty) > 0 ? 1 : 0;
                    for (int j = 0; j < p_bags; j++)
                    {
                     
                        xuNumber++;
                        RequestDetailReques model = new RequestDetailReques();
                        model.id = Guid.NewGuid().ToString();
                        model.Xnumber = xuNumber;
                        model.BatchId = searchDatas[i].BatchId;
                        model.UId = searchDatas[i].UId;
                        model.ProductionOrderId = searchDatas[i].ProductionOrderId;
                        model.ProductionOrderNo = searchDatas[i].ProductionOrderNo;
                        model.BNumber = searchDatas[i].BNumber;
                        model.ExdateQty = searchDatas[i].ExdateQty;
                        model.MId = searchDatas[i].MId;
                        model.MName = searchDatas[i].MName;
                        model.MCode = searchDatas[i].MCode;
                        model.TagerQty = searchDatas[i].TagerQty;
                        model.UintName = searchDatas[i].UintName;
                        model.BagSize = searchDatas[i].BagSize;
                        model.LotCode = reqModel.LotCode;// datas[i].LotCode;
                        model.InventQty = searchDatas[i].InventPqty.ToString();
                        model.FullPage = "1";
                        model.InventPqty = searchDatas[i].InventPqty;
                        model.SubLotcode = searchDatas[i].SubLotcode;
                        model.Sapformula = searchDatas[i].Sapformula;
                        data.Add(model);
                    }
                }

                #endregion
            }



            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        public async Task<PageModel<MaterialLabelViewEntity>> GetPageListByProIdsOLd(MaterialLabelViewRequestModel reqModel)
        {

            //这里执行转换proID      
            var proidS = reqModel.ProIDS.Split(',').ToArray();

            //for (int i = 0; i < arrList.Length; i++)
            //{
            //    if (!string.IsNullOrEmpty(arrList[i])) 
            //    {
            //        p
            //    }
            //}

            if (proidS == null || proidS.Length == 0)
            {
                return new PageModel<MaterialLabelViewEntity>();
            }

            List<MpModel> mpList = await Get_DisMaterial(reqModel.EquipmentID);

            string[] mIDs = mpList.Select(mp => mp.MaterialID).ToArray();
            if (mIDs == null || mIDs.Length <= 0)

            {
                return new PageModel<MaterialLabelViewEntity>();
            }

            //mIDs = _MaterialEntitydal.Db.Queryable<DFM.Model.Models.MaterialEntity>().In(p => p.ID, mIDs).Select(mp => mp.ID).ToArray();
            mIDs = _MaterialPropertyValueEntityDal.Db.Queryable<MaterialPropertyValueEntity>().In(p => p.MaterialId, mIDs).
               Where(p => p.PropertyCode == "PrepByPO" && p.PropertyValue == "1")
                .Select(mp => mp.MaterialId).ToArray();
            if (mIDs == null || mIDs.Length <= 0)
            {
                return new PageModel<MaterialLabelViewEntity>();
            }
            PageModel<MaterialLabelViewEntity> result = new PageModel<MaterialLabelViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<MaterialLabelViewEntity>().AndIF(!string.IsNullOrEmpty(reqModel.ProductionOrderId), p => p.ProductionOrderId == reqModel.ProductionOrderId)
                             .ToExpression();
            //获取数据源
            var dataResult = await _dal.Db.Queryable<MaterialLabelViewEntity>().Where(whereExpression).In(p => p.ProductionOrderId, proidS).In(p => p.MId, mIDs).ToListAsync();

            List<MaterialLabelViewEntity> data = new List<MaterialLabelViewEntity>();

            //循环所有数据源
            int f_bags = 0;
            int p_bags = 0;

            //去重复工单
            string[] proIDS = dataResult.Select(P => P.ProductionOrderId).Distinct().Where(p => !string.IsNullOrWhiteSpace(p)).ToArray();
            if (proIDS == null || proIDS.Length <= 0)
            {
                return new PageModel<MaterialLabelViewEntity>();
            }
            //循环工单（添加序号操作）
            for (int x = 0; x < proIDS.Length; x++)
            {
                string proID = proIDS[x];
                //按照工单来执行数据(筛选工单来排序)
                var searchDatas = dataResult.Where(P => P.ProductionOrderId == proID).OrderBy(p => p.Sapformula).ThenBy(p => p.TagerQty).ThenBy(p => p.TagerQty).ThenBy(p => p.MCode).ToList();
                int xuNumber = 0;

                #region 执行循环给数据操作

                for (int i = 0; i < searchDatas.Count; i++)
                {
                    //总包数(整袋)
                    f_bags = Convert.ToInt32(searchDatas[i].FullPage);
                    for (int j = 0; j < f_bags; j++)
                    {
                        xuNumber++;
                        MaterialLabelViewEntity model = new MaterialLabelViewEntity();
                        // model.id = Guid.NewGuid().ToString();
                        model.Xnumber = xuNumber;
                        model.BatchId = searchDatas[i].BatchId;
                        model.UId = searchDatas[i].UId;
                        model.ProductionOrderId = searchDatas[i].ProductionOrderId;
                        model.ProductionOrderNo = searchDatas[i].ProductionOrderNo;
                        model.BNumber = searchDatas[i].BNumber;
                        model.ExdateQty = searchDatas[i].ExdateQty;
                        model.MId = searchDatas[i].MId;
                        model.MName = searchDatas[i].MName;
                        model.MCode = searchDatas[i].MCode;
                        model.TagerQty = searchDatas[i].TagerQty;
                        model.UintName = searchDatas[i].UintName;
                        model.BagSize = searchDatas[i].BagSize;
                        model.LotCode = reqModel.LotCode;
                        model.InventQty = searchDatas[i].InventQty;
                        model.FullPage = "1";
                        model.InventPqty = searchDatas[i].InventPqty;
                        model.SubLotcode = searchDatas[i].SubLotcode;
                        model.Sapformula = searchDatas[i].Sapformula;
                        data.Add(model);
                    }

                    //单包数量（单包）
                    p_bags = Convert.ToDecimal(searchDatas[i].InventPqty) > 0 ? 1 : 0;
                    for (int j = 0; j < p_bags; j++)
                    {

                        xuNumber++;
                        MaterialLabelViewEntity model = new MaterialLabelViewEntity();
                        //model.id = Guid.NewGuid().ToString();
                        model.Xnumber = xuNumber;
                        model.BatchId = searchDatas[i].BatchId;
                        model.UId = searchDatas[i].UId;
                        model.ProductionOrderId = searchDatas[i].ProductionOrderId;
                        model.ProductionOrderNo = searchDatas[i].ProductionOrderNo;
                        model.BNumber = searchDatas[i].BNumber;
                        model.ExdateQty = searchDatas[i].ExdateQty;
                        model.MId = searchDatas[i].MId;
                        model.MName = searchDatas[i].MName;
                        model.MCode = searchDatas[i].MCode;
                        model.TagerQty = searchDatas[i].TagerQty;
                        model.UintName = searchDatas[i].UintName;
                        model.BagSize = searchDatas[i].BagSize;
                        model.LotCode = reqModel.LotCode;// datas[i].LotCode;
                        model.InventQty = searchDatas[i].InventPqty.ToString();
                        model.FullPage = "1";
                        model.InventPqty = searchDatas[i].InventPqty;
                        model.SubLotcode = searchDatas[i].SubLotcode;
                        model.Sapformula = searchDatas[i].Sapformula;
                        data.Add(model);
                    }
                }

                #endregion
            }



            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        #endregion

        #endregion


    }
}