
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.PPM.Services
{
    public class BclblDetailViewServices : BaseServices<BclblDetailViewEntity>, IBclblDetailViewServices
    {
        private readonly IBaseRepository<BclblDetailViewEntity> _dal;
        public BclblDetailViewServices(IBaseRepository<BclblDetailViewEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<BclblDetailViewEntity>> GetList(BclblDetailViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<BclblDetailViewEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<BclblDetailViewEntity>> GetPageList(BclblDetailViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<BclblDetailViewEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

        public async Task<bool> SaveForm(BclblDetailViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}