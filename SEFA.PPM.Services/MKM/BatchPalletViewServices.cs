
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.MKM.Model.ViewModels.View;
using SEFA.MKM.Model.Models.MKM;
using System.Linq;
using Microsoft.AspNetCore.DataProtection.KeyManagement;
using System;

namespace SEFA.MKM.Services
{
    public class BatchPalletViewServices : BaseServices<BatchPalletViewEntity>, IBatchPalletViewServices
    {
        private readonly IBaseRepository<BatchPalletViewEntity> _dal;
        public BatchPalletViewServices(IBaseRepository<BatchPalletViewEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }


        public async Task<BatchPalletViewEntity> QueryById(string id)
        {
            List<BatchPalletViewEntity> result = new List<BatchPalletViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BatchPalletViewEntity>()
                             .AndIF(!string.IsNullOrEmpty(id), a => a.ID == id)
                             .ToExpression();
            BatchPalletViewEntity data = await _dal.Db.Queryable<BatchPalletViewEntity>()
                .Where(whereExpression).FirstAsync();
            return data;
        }

        public async Task<List<BatchPalletViewEntity>> GetList(BatchPalletViewRequestModel reqModel)
        {
            List<BatchPalletViewEntity> result = new List<BatchPalletViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BatchPalletViewEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<BatchPalletViewEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        #region MyRegion

        #endregion
        /// <summary>
        /// 获取明细需要注意下面的同步修改
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<PageModel<BatchPalletViewEntity>> GetPageList(BatchPalletModel reqModel)
        {
            PageModel<BatchPalletViewEntity> result = new PageModel<BatchPalletViewEntity>();
            RefAsync<int> dataCount = 0;

            if (string.IsNullOrEmpty(reqModel.ConStatus))
            {
                var whereExpression = Expressionable.Create<BatchPalletViewEntity>()
                  //加入查询条件(时间)
                  //batch pallet id
                  .AndIF(!string.IsNullOrEmpty(reqModel.BNubmber), a => a.ContainerName.Contains(reqModel.BNubmber))
                  //pro
                  .AndIF(!string.IsNullOrEmpty(reqModel.ProOrder), a => a.ProOrder == reqModel.ProOrder)
                  //location
                  .AndIF(!string.IsNullOrEmpty(reqModel.LocationF), a => a.EquipmentId == reqModel.LocationF).And(a => a.ContainerStatus == "1" || a.ContainerStatus == "3" || a.ContainerStatus == "4")
                  //Status
                  //bin
                  .AndIF(!string.IsNullOrEmpty(reqModel.LocationS), a => a.LocationS == reqModel.LocationS).
                   //时间
                   AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.PlanStartTime.Value >= Convert.ToDateTime(reqModel.StartTime))
                   .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.PlanStartTime.Value <= Convert.ToDateTime(reqModel.EndTime))
                   .AndIF(!string.IsNullOrEmpty(reqModel.Formula), a => a.Formula != null && a.Formula.Contains(reqModel.Formula))
                 //machine
                 .AndIF(!string.IsNullOrEmpty(reqModel.CMachine), a => a.CMachine == reqModel.CMachine).ToExpression();

                var data2 = await _dal.Db.Queryable<BatchPalletViewEntity>().Where(whereExpression).OrderByDescending(p => p.PlanStartTime).ToListAsync();
                int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
                var rDat = data2.OrderBy(p => p.CreateDate).Skip(startIndex).Take(reqModel.pageSize).ToList();
                result.dataCount = data2.Count;
                result.data = rDat;
                return result;

            }

           var  whereExpression1 = Expressionable.Create<BatchPalletViewEntity>()
          //加入查询条件(时间)
          //batch pallet id
          .AndIF(!string.IsNullOrEmpty(reqModel.BNubmber), a => a.ContainerName.Contains(reqModel.BNubmber))
          //pro
          .AndIF(!string.IsNullOrEmpty(reqModel.ProOrder), a => a.ProOrder == reqModel.ProOrder)
          //location
          .AndIF(!string.IsNullOrEmpty(reqModel.LocationF), a => a.EquipmentId == reqModel.LocationF)
           //Status

           .AndIF(!string.IsNullOrEmpty(reqModel.ConStatus), a => a.ContainerStatus == reqModel.ConStatus)
          //bin
          .AndIF(!string.IsNullOrEmpty(reqModel.LocationS), a => a.LocationS == reqModel.LocationS)
           //时间

           .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.PlanStartTime.Value >= Convert.ToDateTime(reqModel.StartTime))
         .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.PlanStartTime.Value <= Convert.ToDateTime(reqModel.EndTime))
         .AndIF(!string.IsNullOrEmpty(reqModel.Formula), a => a.Formula != null && a.Formula.Contains(reqModel.Formula))
         //machine
         .AndIF(!string.IsNullOrEmpty(reqModel.CMachine), a => a.CMachine == reqModel.CMachine).ToExpression();

            var data3 = await _dal.Db.Queryable<BatchPalletViewEntity>().Where(whereExpression1).OrderByDescending(p => p.PlanStartTime).ToListAsync();
            int startIndex1 = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
            var rDat1 = data3.OrderBy(p => p.CreateDate).Skip(startIndex1).Take(reqModel.pageSize).ToList();
            result.dataCount = data3.Count;
            result.data = rDat1;
            return result;
        }

        /// <summary>
        /// 获取Machine
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<List<Select>> GetMachine(BatchPalletModel reqModel)
        {
            List<BatchPalletViewEntity> result = new List<BatchPalletViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BatchPalletViewEntity>()
              //加入查询条件(时间)
              //batch pallet id
              .AndIF(!string.IsNullOrEmpty(reqModel.BNubmber), a => a.BNubmber == reqModel.BNubmber)
              //pro
              .AndIF(!string.IsNullOrEmpty(reqModel.ProOrder), a => a.ProOrder == reqModel.ProOrder)
              //location
              .AndIF(!string.IsNullOrEmpty(reqModel.LocationF), a => a.ProOrder == reqModel.LocationF)
               //Status
               .AndIF(!string.IsNullOrEmpty(reqModel.ConStatus), a => a.ContainerStatus == reqModel.ConStatus)
             //bin
             .AndIF(!string.IsNullOrEmpty(reqModel.LocationS), a => a.LocationS == reqModel.LocationS)
               //时间
               .AndIF(!string.IsNullOrEmpty(reqModel.StarTime), a => a.StatesTime >= Convert.ToDateTime(reqModel.StarTime))
                   .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.StatesTime >= Convert.ToDateTime(reqModel.EndTime))
             //machine
             //.AndIF(!string.IsNullOrEmpty(reqModel.CMachine), a => a.CMachine == reqModel.CMachine)
             .ToExpression();
            result = await _dal.Db.Queryable<BatchPalletViewEntity>().Where(whereExpression).ToListAsync();
            var selects = result.GroupBy(p => new { p.ECode, p.CMachine }).Select(x => new Select { key = x.Key.ECode, value = x.Key.CMachine }).ToList();
            return selects;

        }

        public async Task<bool> SaveForm(BatchPalletViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }


        public async Task<List<Select>> QueryBinS()
        {
            List<DicBinBpalletEntity> result = new List<DicBinBpalletEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<DicBinBpalletEntity>()
                            .ToExpression();
            result = await _dal.Db.Queryable<DicBinBpalletEntity>().Where(whereExpression).ToListAsync();

            var selects = result.GroupBy(p => new { p.SCode }).Select(x => new Select { key = x.Key.SCode, value = x.Key.SCode }).ToList();
            return selects;

        }


        /// <summary>
        /// 获取下拉BIN
        /// </summary>
        /// <returns></returns>
        public async Task<List<DicBinBpalletEntity>> QueryBin()
        {
            List<DicBinBpalletEntity> result = new List<DicBinBpalletEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<DicBinBpalletEntity>()
                             .ToExpression();
            result = await _dal.Db.Queryable<DicBinBpalletEntity>().Where(whereExpression).ToListAsync();
            return result;
        }


    }
}