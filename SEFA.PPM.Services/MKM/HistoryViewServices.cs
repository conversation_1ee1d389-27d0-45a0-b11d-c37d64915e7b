
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using System;
using System.Linq;
using SEFA.Base.IRepository.UnitOfWork;
using System.ComponentModel;
using System.Data.SqlTypes;
using System.Security.Cryptography;
using SEFA.Base.Common.HttpContextUser;
using SEFA.PPM.Model.Models;
using SEFA.Base.Common.Common;
using Microsoft.AspNetCore.Mvc.RazorPages;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using SEFA.Base.Common.WebApiClients.HttpApis;
using SEFA.DFM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels;
using SEFA.MKM.Model.Models.MKM;
using SEFA.MKM.Model.ViewModels.View;
using System.Reflection.PortableExecutable;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using SEFA.Base.Common.Seed;
using SEFA.PPM.Model.Models.PTM;
using SEFA.Base.AOP;
using static SEFA.PTM.Services.ConsumeViewServices;
using SEFA.PTM.IServices;
using System.Reflection;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.PPM.Model.Models.MKM;

namespace SEFA.MKM.Services
{
    public class HistoryViewServices : BaseServices<HistoryViewEntity>, IHistoryViewServices
    {
        private readonly IBaseRepository<HistoryViewEntity> _dal;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IUser _uIser;
        private readonly IBaseRepository<MaterialInventoryEntity> _materialInventoryEntityDal;
        private readonly IBaseRepository<MaterialSubLotEntity> _materialSubLotServicesDal;
        private readonly IBaseRepository<MaterialLotEntity> _materialLotEntityDal;
        private readonly IBaseRepository<PoConsumeActualEntity> _poConsumeActualEntitydal;
        private readonly IBaseRepository<PoConsumeRequirementEntity> _poconsumeDal;
        private readonly IConsumeViewServices _consumeViewServices;

        private readonly IProductionHistoryViewServices _productionHistoryViewServices;
        public HistoryViewServices(IBaseRepository<HistoryViewEntity> dal, IBaseRepository<PoConsumeActualEntity> poConsumeActualEntitydal, IUser uIser, IUnitOfWork unitOfWork, IBaseRepository<MaterialInventoryEntity> materialInventoryEntityDal, IProductionHistoryViewServices productionHistoryViewServices, IBaseRepository<PoConsumeRequirementEntity> poconsumeDal, IConsumeViewServices consumeViewServices)
        {
            _dal = dal;
            BaseDal = dal;
            _poConsumeActualEntitydal = poConsumeActualEntitydal;
            _materialInventoryEntityDal = materialInventoryEntityDal;
            _uIser = uIser;
            _unitOfWork = unitOfWork;
            _productionHistoryViewServices = productionHistoryViewServices;
            _poconsumeDal = poconsumeDal;
            _consumeViewServices = consumeViewServices;
        }

        public async Task<List<HistoryViewEntity>> GetList(HistoryViewRequestModel reqModel)
        {
            if (reqModel.typeSerch.Contains("ZZ"))
            {
                reqModel.typeSerch = "ZXH2";
            }
            else if (reqModel.typeSerch.Contains("GBZ"))
            {
                reqModel.typeSerch = "ZXH1";
            }
            var whereExpression = Expressionable.Create<HistoryViewEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
                .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
                .AndIF(!string.IsNullOrEmpty(reqModel.Machine), a => a.MachineName.Contains(reqModel.Machine) || a.MachineCode.Contains(reqModel.Machine))
                .AndIF(!string.IsNullOrEmpty(reqModel.Material), a => a.MCode.Contains(reqModel.Material) || a.MName.Contains(reqModel.Material))
                .AndIF(!string.IsNullOrEmpty(reqModel.Batch), a => a.LBatch.Contains(reqModel.Batch))
                .AndIF(!string.IsNullOrEmpty(reqModel.SSCC), a => a.SubSscc.Contains(reqModel.SSCC))
                .AndIF(!string.IsNullOrEmpty(reqModel.PRO_Order), a => a.ProcessOrder.Contains(reqModel.PRO_Order))
                .AndIF(!string.IsNullOrEmpty(reqModel.ProcessOrder), a => a.ProcessOrder.Contains(reqModel.ProcessOrder))
                .AndIF(!string.IsNullOrEmpty(reqModel.Container), a => a.CName.Contains(reqModel.Container))
                .AndIF(!string.IsNullOrEmpty(reqModel.Material_Class), a => a.CClass.Contains(reqModel.Material_Class))
                .AndIF(!string.IsNullOrEmpty(reqModel.Source), a => a.SourceCode.Contains(reqModel.Source) || a.SourceName.Contains(reqModel.Source))
                .AndIF(!string.IsNullOrEmpty(reqModel.SourceBin), a => a.SourcebinCode.Contains(reqModel.SourceBin) || a.SourcebinId.Contains(reqModel.SourceBin))
                .AndIF(!string.IsNullOrEmpty(reqModel.Shift), a => a.ShiftName.Contains(reqModel.Shift))
                    .AndIF(!string.IsNullOrEmpty(reqModel.typeSerch), a => a.Sapordertype.Contains(reqModel.typeSerch))
                             .ToExpression();

            if (reqModel.typeSerch == "ZXH1")
            {
                whereExpression = Expressionable.Create<HistoryViewEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
                .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
                .AndIF(!string.IsNullOrEmpty(reqModel.Machine), a => a.MachineName.Contains(reqModel.Machine) || a.MachineCode.Contains(reqModel.Machine))
                .AndIF(!string.IsNullOrEmpty(reqModel.Material), a => a.MCode.Contains(reqModel.Material) || a.MName.Contains(reqModel.Material))
                .AndIF(!string.IsNullOrEmpty(reqModel.Batch), a => a.LBatch.Contains(reqModel.Batch))
                .AndIF(!string.IsNullOrEmpty(reqModel.SSCC), a => a.SubSscc.Contains(reqModel.SSCC))
                .AndIF(!string.IsNullOrEmpty(reqModel.PRO_Order), a => a.ProcessOrder.Contains(reqModel.PRO_Order))
                .AndIF(!string.IsNullOrEmpty(reqModel.ProcessOrder), a => a.ProcessOrder.Contains(reqModel.ProcessOrder))
                .AndIF(!string.IsNullOrEmpty(reqModel.Container), a => a.CName.Contains(reqModel.Container))
                .AndIF(!string.IsNullOrEmpty(reqModel.Material_Class), a => a.CClass.Contains(reqModel.Material_Class))
                .AndIF(!string.IsNullOrEmpty(reqModel.Source), a => a.SourceCode.Contains(reqModel.Source) || a.SourceName.Contains(reqModel.Source))
                .AndIF(!string.IsNullOrEmpty(reqModel.SourceBin), a => a.SourcebinCode.Contains(reqModel.SourceBin) || a.SourcebinId.Contains(reqModel.SourceBin))
                .AndIF(!string.IsNullOrEmpty(reqModel.Shift), a => a.ShiftName.Contains(reqModel.Shift))
                .AndIF(!string.IsNullOrEmpty(reqModel.typeSerch), a => !a.Sapordertype.Contains("ZXH2"))
                .AndIF(reqModel.IsSauce == "True", a => a.MaterialType == "ZSFG")
                .AndIF(reqModel.IsSauce == "False", a => a.MaterialType != "ZSFG")
                .ToExpression();
            }
            var data = await _dal.Db.Queryable<HistoryViewEntity>().
            Where(whereExpression).OrderByDescending(p => p.CreateDate).ToListAsync();
            return data;
        }

        public async Task<List<GroupData>> GetConsumeSumList(HistoryViewRequestModel reqModel)
        {
            var list = await GetList(reqModel);
            var goups = list.GroupBy(x => new { x.Pid, x.BatchId, x.ProcessOrder, x.MId, x.MCode, x.MName, x.LBatch, x.QuantityUnit });
            // 查询 BatchConsumeRequirementEntity 表的数据
            var batchConsumeRequirements = await _dal.Db.Queryable<BatchConsumeRequirementEntity>().ToListAsync();
            // 查询 PoConsumeRequirementEntity 表的数据
            var poConsumeRequirements = await _dal.Db.Queryable<PoConsumeRequirementEntity>().ToListAsync();
            var data = (goups.Select(g =>
            {
                var batchConsumeSum = batchConsumeRequirements
                        .Join(poConsumeRequirements, bc => bc.PoConsumeRequirementId, pc => pc.ID, (bc, pc) => new { bc, pc })
                        .Where(j => j.bc.BatchId == g.Key.BatchId && j.pc.ProductionOrderId == g.Key.Pid && j.pc.MaterialId == g.Key.MId)
                        .Sum(j => j.bc.Quantity);
                return new GroupData
                {
                    Pid = g.Key.Pid,
                    BatchId = g.Key.BatchId,
                    ProcessOrder = g.Key.ProcessOrder,
                    MId = g.Key.MId,
                    MCode = g.Key.MCode,
                    MName = g.Key.MName,
                    LBatch = g.Key.LBatch,
                    Unit = g.Key.QuantityUnit,
                    Q0 = batchConsumeSum,
                    Q1 = g.Sum(x => x.IQuantity) ?? 0,
                    Q2 = g.Where(x => x.SendStates == "已发送")?.Sum(x => x.IQuantity) ?? 0,
                    Q3 = g.Where(x => x.SendStates == "等待SAP返回结果")?.Sum(x => x.IQuantity) ?? 0,
                    Q4 = g.Where(x => x.SendStates == "未发送")?.Sum(x => x.IQuantity) ?? 0,
                    Q5 = g.Where(x => x.SendStates == "发送失败")?.Sum(x => x.IQuantity) ?? 0,
                    Q6 = (g.Sum(x => x.IQuantity) ?? 0) - batchConsumeSum,
                    Details = g.GroupBy(x => new
                    {
                        x.Pid,
                        x.BatchId,
                        x.ProcessOrder,
                        x.MId,
                        x.MCode,
                        x.MName,
                        x.LBatch,
                        x.SubSscc,
                        x.QuantityUnit
                    }).Select(gi => new GroupDataDetail()
                    {
                        Pid = gi.Key.Pid,
                        BatchId = gi.Key.BatchId,
                        ProcessOrder = gi.Key.ProcessOrder,
                        MId = gi.Key.MId,
                        MCode = gi.Key.MCode,
                        MName = gi.Key.MName,
                        LBatch = gi.Key.LBatch,
                        TraceCode = gi.Key.SubSscc,
                        Quantity = gi.Sum(x => x.IQuantity) ?? 0,
                        Unit = gi.Key.QuantityUnit
                    }).OrderBy(x => x.ProcessOrder).ThenBy(x => x.MCode).ThenBy(x => x.LBatch).ToList()
                };
            })).OrderBy(x => x.ProcessOrder).ThenBy(x => x.MCode).ThenBy(x => x.LBatch).ToList();
            return data;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<List<Select>> GetConsumMachine(BatchPalletModel reqModel)
        {
            if (reqModel.typeSerch.Contains("ZZ"))
            {
                reqModel.typeSerch = "ZXH2";
            }
            else if (reqModel.typeSerch.Contains("GBZ"))
            {
                reqModel.typeSerch = "ZXH1";
            }


            var dataRespone = await HttpHelper.PostAsync<List<EquipmentEntity>>("DFM", $"api/Equipment/GetListByLevel?key=unit", _uIser.GetToken());
            if (!dataRespone.success)
            {
                return new List<Select>();
            }

            var result = dataRespone.response;
            if (dataRespone.response == null || dataRespone.response.Count <= 0)
            {
                return new List<Select>();
            }

            var selects = result.GroupBy(p => new { p.EquipmentName, p.EquipmentCode }).Select(x => new Select { key = x.Key.EquipmentCode, value = x.Key.EquipmentName }).ToList();
            return selects;


            //var selects = result.GroupBy(p => new { p.EquipmentName, p.EquipmentCode }).Select(x => new Select { key = x.Key.EquipmentCode, value = x.Key.EquipmentName }).ToList();
            //return selects;

            //if (reqModel.typeSerch == "ZXH1")
            //{
            //    var selects = await _dal.Db.Queryable<HistoryViewEntity>().Where(a => a.Sapordertype != "ZXH2" && !string.IsNullOrEmpty(a.MachineCode)).Select(x => new Select { key = x.MachineCode, value = x.MachineName }).Distinct().ToListAsync();
            //    return selects;
            //}
            //else
            //{
            //    var selects = await _dal.Db.Queryable<HistoryViewEntity>().Where(a => a.Sapordertype == "ZXH2" && !string.IsNullOrEmpty(a.MachineCode)).Select(x => new Select { key = x.MachineCode, value = x.MachineName }).Distinct().ToListAsync();
            //    return selects;
            //}

        }

        public async Task<List<Select>> GetConsumMachineSource(BatchPalletModel reqModel)
        {
            if (reqModel.typeSerch.Contains("ZZ"))
            {
                reqModel.typeSerch = "ZXH2";
            }
            else if (reqModel.typeSerch.Contains("GBZ"))
            {
                reqModel.typeSerch = "ZXH1";
            }

            //获取单元unit
            var dataRespone = await HttpHelper.PostAsync<List<EquipmentEntity>>("DFM", $"api/Equipment/GetListByLevel?key=unit", _uIser.GetToken());

            //获取Storage
            var datasRespone = await HttpHelper.PostAsync<List<EquipmentEntity>>("DFM", $"api/Equipment/GetListByLevel?key=Storage", _uIser.GetToken());

            if (!dataRespone.success && !dataRespone.success)
            {
                return new List<Select>();
            }

            var result = dataRespone.response;
            var resultR = datasRespone.response;
            if (result == null || result.Count <= 0)
            {
                if (resultR == null || resultR.Count <= 0)
                {
                    return new List<Select>();
                }
            }

            List<Select> list = new List<Select>();

            var selects = result.GroupBy(p => new { p.EquipmentName }).Select(x => new Select { key = x.Key.EquipmentName, value = x.Key.EquipmentName }).ToList();
            var selectsR = resultR.GroupBy(p => new { p.EquipmentName }).Select(x => new Select { key = x.Key.EquipmentName, value = x.Key.EquipmentName }).ToList();
            if (selects != null && selects.Count > 0)
            {
                list.AddRange(selects);
            }
            if (selectsR != null && selectsR.Count > 0)
            {
                list.AddRange(selectsR);
            }

            return list.Distinct().ToList();

            //if (reqModel.typeSerch == "ZXH1")
            //{
            //    var selects = await _dal.Db.Queryable<HistoryViewEntity>().Where(a => a.Sapordertype != "ZXH2" && !string.IsNullOrEmpty(a.SourceName)).Select(x => new Select { key = x.SourceName, value = x.SourceName }).Distinct().ToListAsync();
            //    return selects;
            //}
            //else
            //{
            //    var selects = await _dal.Db.Queryable<HistoryViewEntity>().Where(a => a.Sapordertype == "ZXH2" && !string.IsNullOrEmpty(a.SourceName)).Select(x => new Select { key = x.SourceName, value = x.SourceName }).Distinct().ToListAsync();
            //    return selects;
            //}

        }

        ///// <summary>
        ///// 
        ///// </summary>
        ///// <param name="reqModel"></param>
        ///// <returns></returns>
        //public async Task<List<Select>> GetConsumMachine(BatchPalletModel reqModel)
        //{
        //	if (reqModel.typeSerch.Contains("ZZ"))
        //	{
        //		reqModel.typeSerch = "ZXH2";
        //	}
        //	else if (reqModel.typeSerch.Contains("GBZ"))
        //	{
        //		reqModel.typeSerch = "ZXH1";
        //	}

        //	if (reqModel.typeSerch == "ZXH1")
        //	{
        //		var selects = await _dal.Db.Queryable<HistoryViewEntity>().Where(a => a.Sapordertype != "ZXH2" && !string.IsNullOrEmpty(a.MachineCode)).Select(x => new Select { key = x.MachineCode, value = x.MachineName }).Distinct().ToListAsync();
        //		return selects;
        //	}
        //	else
        //	{
        //		var selects = await _dal.Db.Queryable<HistoryViewEntity>().Where(a => a.Sapordertype == "ZXH2" && !string.IsNullOrEmpty(a.MachineCode)).Select(x => new Select { key = x.MachineCode, value = x.MachineName }).Distinct().ToListAsync();
        //		return selects;
        //	}

        //}

        //public async Task<List<Select>> GetConsumMachineSource(BatchPalletModel reqModel)
        //{
        //	if (reqModel.typeSerch.Contains("ZZ"))
        //	{
        //		reqModel.typeSerch = "ZXH2";
        //	}
        //	else if (reqModel.typeSerch.Contains("GBZ"))
        //	{
        //		reqModel.typeSerch = "ZXH1";
        //	}

        //	if (reqModel.typeSerch == "ZXH1")
        //	{
        //		var selects = await _dal.Db.Queryable<HistoryViewEntity>().Where(a => a.Sapordertype != "ZXH2" && !string.IsNullOrEmpty(a.SourceName)).Select(x => new Select { key = x.SourceName, value = x.SourceName }).Distinct().ToListAsync();
        //		return selects;
        //	}
        //	else
        //	{
        //		var selects = await _dal.Db.Queryable<HistoryViewEntity>().Where(a => a.Sapordertype == "ZXH2" && !string.IsNullOrEmpty(a.SourceName)).Select(x => new Select { key = x.SourceName, value = x.SourceName }).Distinct().ToListAsync();
        //		return selects;
        //	}

        //}


        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<PageModel<HistoryViewEntityModel>> GetPageList(HistoryViewRequestModel reqModel)
        {
            if (reqModel.typeSerch.Contains("ZZ"))
            {
                reqModel.typeSerch = "ZXH2";
            }
            else if (reqModel.typeSerch.Contains("GBZ"))
            {
                reqModel.typeSerch = "ZXH1";
            }
            PageModel<HistoryViewEntityModel> result = new PageModel<HistoryViewEntityModel>();
            RefAsync<int> dataCount = 0;

            var whereExpression = Expressionable.Create<HistoryViewEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
                .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
                .AndIF(!string.IsNullOrEmpty(reqModel.Machine), a => a.MachineName.Contains(reqModel.Machine) || a.MachineCode.Contains(reqModel.Machine))
                .AndIF(!string.IsNullOrEmpty(reqModel.Material), a => a.MCode.Contains(reqModel.Material) || a.MName.Contains(reqModel.Material))
                .AndIF(!string.IsNullOrEmpty(reqModel.Batch), a => a.LBatch.Contains(reqModel.Batch))
                .AndIF(!string.IsNullOrEmpty(reqModel.SSCC), a => a.SubSscc.Contains(reqModel.SSCC))
                .AndIF(!string.IsNullOrEmpty(reqModel.PRO_Order), a => a.ProcessOrder.Contains(reqModel.PRO_Order))
                .AndIF(!string.IsNullOrEmpty(reqModel.ProcessOrder), a => a.ProcessOrder.Contains(reqModel.ProcessOrder))
                .AndIF(!string.IsNullOrEmpty(reqModel.Container), a => a.CName.Contains(reqModel.Container))
                .AndIF(!string.IsNullOrEmpty(reqModel.Material_Class), a => a.CClass.Contains(reqModel.Material_Class))
                .AndIF(!string.IsNullOrEmpty(reqModel.Source), a => a.SourceCode.Contains(reqModel.Source) || a.SourceName.Contains(reqModel.Source))
                .AndIF(!string.IsNullOrEmpty(reqModel.SourceBin), a => a.SourcebinCode.Contains(reqModel.SourceBin) || a.SourcebinId.Contains(reqModel.SourceBin))
                .AndIF(!string.IsNullOrEmpty(reqModel.Shift), a => a.ShiftName.Contains(reqModel.Shift))
                .AndIF(!string.IsNullOrEmpty(reqModel.typeSerch), a => a.Sapordertype.Contains(reqModel.typeSerch))
                .AndIF(reqModel.DataType == "1", a => a.IQuantity >= 0)
                .AndIF(reqModel.DataType == "-1", a => a.IQuantity < 0)
                             .ToExpression();

            if (reqModel.typeSerch == "ZXH1")
            {
                whereExpression = Expressionable.Create<HistoryViewEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
                .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
                .AndIF(!string.IsNullOrEmpty(reqModel.Machine), a => a.MachineName.Contains(reqModel.Machine) || a.MachineCode.Contains(reqModel.Machine))
                .AndIF(!string.IsNullOrEmpty(reqModel.Material), a => a.MCode.Contains(reqModel.Material) || a.MName.Contains(reqModel.Material))
                .AndIF(!string.IsNullOrEmpty(reqModel.Batch), a => a.LBatch.Contains(reqModel.Batch))
                .AndIF(!string.IsNullOrEmpty(reqModel.SSCC), a => a.SubSscc.Contains(reqModel.SSCC))
                .AndIF(!string.IsNullOrEmpty(reqModel.PRO_Order), a => a.ProcessOrder.Contains(reqModel.PRO_Order))
                .AndIF(!string.IsNullOrEmpty(reqModel.ProcessOrder), a => a.ProcessOrder.Contains(reqModel.ProcessOrder))
                .AndIF(!string.IsNullOrEmpty(reqModel.Container), a => a.CName.Contains(reqModel.Container))
                .AndIF(!string.IsNullOrEmpty(reqModel.Material_Class), a => a.CClass.Contains(reqModel.Material_Class))
                .AndIF(!string.IsNullOrEmpty(reqModel.Source), a => a.SourceCode.Contains(reqModel.Source) || a.SourceName.Contains(reqModel.Source))
                .AndIF(!string.IsNullOrEmpty(reqModel.SourceBin), a => a.SourcebinCode.Contains(reqModel.SourceBin) || a.SourcebinId.Contains(reqModel.SourceBin))
                .AndIF(!string.IsNullOrEmpty(reqModel.Shift), a => a.ShiftName.Contains(reqModel.Shift))
                .AndIF(!string.IsNullOrEmpty(reqModel.typeSerch), a => !a.Sapordertype.Contains("ZXH2"))
                .AndIF(reqModel.IsSauce == "True", a => a.MaterialType == "ZSFG")
                .AndIF(reqModel.IsSauce == "False", a => a.MaterialType != "ZSFG")
                .AndIF(reqModel.DataType == "1", a => a.IQuantity >= 0)
                .AndIF(reqModel.DataType == "-1", a => a.IQuantity < 0)
                .ToExpression();
            }


            var data2 = await _dal.Db.Queryable<HistoryViewEntity>().
          Where(whereExpression).OrderByDescending(p => p.CreateDate).ToListAsync();

            int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
            var rDat = data2.OrderByDescending(p => p.CreateDate).Skip(startIndex).Take(reqModel.pageSize).ToList();

            HistoryViewEntityModel historyViewEntityModel = new HistoryViewEntityModel();
            historyViewEntityModel.historyViewEntities = rDat;
            if (data2 != null)
            {
                historyViewEntityModel.Total = data2.Sum(p => p.IQuantity).Value;
            }
            else
            {
                historyViewEntityModel.Total = 0;
            }
            List<HistoryViewEntityModel> list = new List<HistoryViewEntityModel>();
            list.Add(historyViewEntityModel);
            result.dataCount = data2.Count;
            result.data = list;
            return result;

            //  var data = await _dal.Db.Queryable<HistoryViewEntity>()
            //.Where(whereExpression).OrderByDescending(p => p.CreateDate)
            //.ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            //  result.dataCount = dataCount;
            //  result.data = data;
            //  return result;
        }


        public async Task<List<ConsumModels>> GetExportList(HistoryViewRequestModel reqModel)
        {
            if (reqModel.typeSerch.Contains("ZZ"))
            {
                reqModel.typeSerch = "ZXH2";
            }
            else if (reqModel.typeSerch.Contains("GBZ"))
            {
                reqModel.typeSerch = "ZXH1";
            }
            PageModel<HistoryViewEntity> result = new PageModel<HistoryViewEntity>();
            RefAsync<int> dataCount = 0;

            var whereExpression = Expressionable.Create<HistoryViewEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
                .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
                .AndIF(!string.IsNullOrEmpty(reqModel.Machine), a => a.MachineName.Contains(reqModel.Machine) || a.MachineCode.Contains(reqModel.Machine))
                .AndIF(!string.IsNullOrEmpty(reqModel.Material), a => a.MCode.Contains(reqModel.Material) || a.MName.Contains(reqModel.Material))
                .AndIF(!string.IsNullOrEmpty(reqModel.Batch), a => a.LBatch.Contains(reqModel.Batch))
                .AndIF(!string.IsNullOrEmpty(reqModel.SSCC), a => a.SubSscc.Contains(reqModel.SSCC))
                .AndIF(!string.IsNullOrEmpty(reqModel.PRO_Order), a => a.ProcessOrder.Contains(reqModel.PRO_Order))
                .AndIF(!string.IsNullOrEmpty(reqModel.ProcessOrder), a => a.ProcessOrder.Contains(reqModel.ProcessOrder))
                .AndIF(!string.IsNullOrEmpty(reqModel.Container), a => a.CName.Contains(reqModel.Container))
                .AndIF(!string.IsNullOrEmpty(reqModel.Material_Class), a => a.CClass.Contains(reqModel.Material_Class))
                .AndIF(!string.IsNullOrEmpty(reqModel.Source), a => a.SourceCode.Contains(reqModel.Source) || a.SourceName.Contains(reqModel.Source))
                .AndIF(!string.IsNullOrEmpty(reqModel.SourceBin), a => a.SourcebinCode.Contains(reqModel.SourceBin) || a.SourcebinId.Contains(reqModel.SourceBin))
                .AndIF(!string.IsNullOrEmpty(reqModel.Shift), a => a.ShiftName.Contains(reqModel.Shift))
                .AndIF(!string.IsNullOrEmpty(reqModel.typeSerch), a => a.Sapordertype.Contains(reqModel.typeSerch))
                .AndIF(reqModel.DataType == "1", a => a.IQuantity >= 0)
                .AndIF(reqModel.DataType == "-1", a => a.IQuantity < 0)
                             .ToExpression();

            if (reqModel.typeSerch == "ZXH1")
            {
                whereExpression = Expressionable.Create<HistoryViewEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
                .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
                .AndIF(!string.IsNullOrEmpty(reqModel.Machine), a => a.MachineName.Contains(reqModel.Machine) || a.MachineCode.Contains(reqModel.Machine))
                .AndIF(!string.IsNullOrEmpty(reqModel.Material), a => a.MCode.Contains(reqModel.Material) || a.MName.Contains(reqModel.Material))
                .AndIF(!string.IsNullOrEmpty(reqModel.Batch), a => a.LBatch.Contains(reqModel.Material))
                .AndIF(!string.IsNullOrEmpty(reqModel.SSCC), a => a.SubSscc.Contains(reqModel.SSCC))
                .AndIF(!string.IsNullOrEmpty(reqModel.PRO_Order), a => a.ProcessOrder.Contains(reqModel.PRO_Order))
                .AndIF(!string.IsNullOrEmpty(reqModel.ProcessOrder), a => a.ProcessOrder.Contains(reqModel.ProcessOrder))
                .AndIF(!string.IsNullOrEmpty(reqModel.Container), a => a.CName.Contains(reqModel.Container))
                .AndIF(!string.IsNullOrEmpty(reqModel.Material_Class), a => a.CClass.Contains(reqModel.Material_Class))
                .AndIF(!string.IsNullOrEmpty(reqModel.Source), a => a.SourceCode.Contains(reqModel.Source) || a.SourceName.Contains(reqModel.Source))
                .AndIF(!string.IsNullOrEmpty(reqModel.SourceBin), a => a.SourcebinCode.Contains(reqModel.SourceBin) || a.SourcebinId.Contains(reqModel.SourceBin))
                .AndIF(!string.IsNullOrEmpty(reqModel.Shift), a => a.ShiftName.Contains(reqModel.Shift))
                .AndIF(!string.IsNullOrEmpty(reqModel.typeSerch), a => !a.Sapordertype.Contains("ZXH2"))
                .AndIF(reqModel.IsSauce == "True", a => a.MaterialType == "ZSFG")
                .AndIF(reqModel.IsSauce == "False", a => a.MaterialType != "ZSFG")
                .AndIF(reqModel.DataType == "1", a => a.IQuantity >= 0)
                .AndIF(reqModel.DataType == "-1", a => a.IQuantity < 0)
                             .ToExpression();
            }
            var data = await _dal.Db.Queryable<HistoryViewEntity>()
                .Where(whereExpression).ToListAsync();

            var results = (from a in data
                           select new ConsumModels
                           {
                               ProductionOrderNo = a.ProductionOrderNo,
                               MCode = a.MCode,
                               MName = a.MName,
                               BatchCode = a.BatchCode,
                               IQuantity = a.IQuantity.Value,
                               QuantityUnit = a.QuantityUnit,
                               SourceName = a.SourceName,
                               SourceCode = a.SourceCode,
                               CreateTime = a.CreateDate.ToString("yyyy-MM-dd HH:mm:ss")
                           }).ToList();
            return results;
        }

        /// <summary>
        /// 反冲
        /// </summary>
        /// <param name="id"></param>
        /// <param name="quantity"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> Recoil(string id, decimal quantity)
        {
            var result = new MessageModel<string>()
            {
                success = false,
                msg = "反冲失败！"
            };
            //查询数据
            var data = await _dal.QueryById(id);
            var dataBase = await _poConsumeActualEntitydal.QueryById(id);
            var oldQuantity = dataBase.Quantity;
            if (data == null)
            {
                result.msg = "消耗记录未找到";
                return result;
            }

            if (dataBase == null)
            {
                result.msg = "消耗记录未找到";
                return result;
            }


            #region 拿之前库存

            ReverseModel reverseModel = new ReverseModel();
            reverseModel.ID = id;
            var reuslt = await GetGetConsumedQTY(reverseModel);
            if (quantity > Convert.ToDecimal(reuslt.msg))
            {
                result.msg = "反冲数量大于库存数量";
                //result.success = false;
                return result;
            }

            #endregion

            //查询 库存信息(这需要确认数据ID)
            var resultInvent = await _materialInventoryEntityDal.FindList(p => p.SublotId == data.SubId);
			if (resultInvent.Exists(x => x.EquipmentId != dataBase.SourceEquipmentId))
			{
				result.msg = $"库存位置不在{data.SourceName},无法进行反冲！";
				return result;
			}
			//查询是否管理库存
			var conResult = await _productionHistoryViewServices.GetEquipmentStorege(data.EquipmentId);
            if (conResult == string.Empty)
            {
                result.msg = "节点是否配置管理库存节点";
                return result;
            }
            bool isControl = conResult.Split(";")[1].ToString() == "1" ? true : false;//是否管理库存
            PoConsumeActualEntity model = null;
            PoConsumeActualEntity model2 = null;
            try
            {
                _unitOfWork.BeginTran();

                if (isControl == true)
                {
                    #region 归还库存操作              

                    bool dateInvent = false;
                    int v = 1;
                    string unitId = dataBase.UnitId;
                    if (data.QuantityUnit?.ToLower() == "g")
                    {
                        v = 1000;
                        var unit = (await _dal.Db.Queryable<UnitmanageEntity>().Where(x => x.Name == "KG").ToListAsync()).FirstOrDefault();
                        unitId = unit?.ID ?? "KG";
                    }
                    MaterialInventoryEntity inventModel = new MaterialInventoryEntity();

                    //存在更新，不存在新增
                    if (resultInvent != null && resultInvent.Count > 0)
                    {
                        //合并数据(更新库存)
                        inventModel = resultInvent[0];
                        inventModel.Quantity += Math.Round(Convert.ToDecimal(quantity) / v, 3);  //quantity;
                                                                                                 //     inventModel.Modify(  , _uIser.ID.ToString());
                        inventModel.Modify(inventModel.ID, _uIser.Name.ToString());
                        dateInvent = await _materialInventoryEntityDal.Update(inventModel);
                    }
                    else
                    {
                        inventModel.Create(_uIser.Name.ToString());
                        //创建库存(其中包含新的子批次)
                        inventModel.LotId = dataBase.LotId;
                        inventModel.SublotId = dataBase.SubLotId;
                        inventModel.Quantity = Math.Round(Convert.ToDecimal(quantity) / v, 3);  //quantity;
                        inventModel.QuantityUomId = unitId;
                        //modelInventory.StorageLocation = storage_location;
                        inventModel.EquipmentId = dataBase.SourceEquipmentId;
                        inventModel.ContainerId = data.ContainerId;
                        dateInvent = await _materialInventoryEntityDal.Add(inventModel) > 0;
                    }

                    #endregion

                }
                else
                {

                }

                #region 写一个历史记录（物料消耗历史）
                var list = new List<PoConsumeActualEntity>();
                //获取数据
                model = new PoConsumeActualEntity();
                model = dataBase;
                model.Quantity = -model.Quantity;
                model.SendExternal = 0;
                var poConsumeRequirement = await _poconsumeDal.FindEntity(dataBase.PoConsumeRequirementId);
                model.SendExternal = poConsumeRequirement.Quantity == 0 ? 1 : 0;
                model.Type = null;
                model.Mjahr = null;
                model.Mblnr = null;
                model.Zeile = null;
                model.Msg = null;
                model.Pid = dataBase.ID;
                //创建新的ID
                model.CreateCustomGuid(_uIser.Name.ToString());
                list.Add(model);
                if (oldQuantity > quantity)
                {
                    //写入consumed_actual表
                    model2 = new PoConsumeActualEntity
                    {
                        ProductionOrderId = dataBase.ProductionOrderId,
                        PoConsumeRequirementId = dataBase.PoConsumeRequirementId,
                        ProductExecutionId = dataBase.ProductExecutionId,
                        EquipmentId = dataBase.EquipmentId,
                        SourceEquipmentId = dataBase.SourceEquipmentId,
                        Quantity = oldQuantity - quantity,
                        UnitId = dataBase.UnitId,
                        LotId = dataBase.LotId,
                        SubLotId = dataBase.SubLotId,
                        SubLotStatus = dataBase.SubLotStatus,
                        StorageBin = dataBase.StorageBin,
                        StorageLocation = dataBase.StorageLocation,
                        ContainerId = dataBase.ContainerId,
                        TeamId = "",
                        ShiftId = dataBase.ShiftId,
                        ReasonCode = "",
                        Comment = "",
                        Deleted = 0,
                        SendExternal = 0,
                    };
                    model2.Quantity = oldQuantity - quantity;
                    model2.SendExternal = 0;
                    model2.Type = null;
                    model2.Mjahr = null;
                    model2.Mblnr = null;
                    model2.Zeile = null;
                    model2.Msg = null;
                    model2.Pid = null;
                    model2.TranNo = null;
                    model2.Index = null;
                    model2.CreateCustomGuid(_uIser.Name.ToString());
                    list.Add(model2);
                }
                await _poConsumeActualEntitydal.Add(list);
                #endregion

                _unitOfWork.CommitTran();
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msgDev = ex.StackTrace;
                return result;
            }
            //try
            //{
            //	if (model != null)
            //	{
            //		await _consumeViewServices.ConsumeReport(new List<string>() { model.ID });
            //	}
            //	if (model2 != null)
            //	{
            //		await _consumeViewServices.ConsumeReverseReport(new List<string>() { model2.ID });
            //	}
            //}
            //catch (Exception ex)
            //{

            //}
            result.success = true;
            result.msg = "反冲成功！";
            return result;
        }


        /// <summary>
        /// 报工重发
        /// </summary>
        /// <param name="id"></param>      
        /// <returns></returns>
        public async Task<MessageModel<string>> RepeatPlan(string[] id)
        {
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                List<PoConsumeActualEntity> upList = new List<PoConsumeActualEntity>();

                for (Int32 i = 0; i < id.Length; i++)
                {
                    //查询数据
                    var data = await _poConsumeActualEntitydal.QueryById(id[i]);
                    if (data != null)
                    {
                        data.SendExternal = 3;
                        data.Modify(id[i], _uIser.ToString());
                        upList.Add(data);
                    }
                }
                _unitOfWork.BeginTran();

                bool dataBase = await _poConsumeActualEntitydal.Update(upList);
                if (dataBase == true)
                {
                    result.success = true;
                    result.msg = "重发成功";
                }

                _unitOfWork.CommitTran();
            }
            catch (Exception ex)
            {
                result.msg = ex.Message;
                _unitOfWork.RollbackTran();

            }


            return result;
        }
        /// <summary>
        ///  查询ConsumedQTY
        /// </summary>
        /// <param name="processOrder">订单号</param>
        /// <param name="m_Code">物料编码</param>
        /// <param name="proBatch">产线批次</param>
        /// <param name="sscc">子批次</param>
        /// <param name="machineCode"></param>
        /// <param name="sourceCode"></param>
        /// <returns></returns>
        public async Task<decimal> GetConsumedQTY(string processOrder, string m_Code, string proBatch, string sscc, string machineCode, string sourceCode)
        {
            var whereExpression = Expressionable.Create<HistoryViewEntity>()
                   .AndIF(!string.IsNullOrEmpty(processOrder), a => a.ProcessOrder.Contains(processOrder))
                   .AndIF(!string.IsNullOrEmpty(m_Code), a => a.MCode.Contains(m_Code))
                  .AndIF(!string.IsNullOrEmpty(proBatch), a => a.BatchCode.Contains(proBatch))
                  .AndIF(!string.IsNullOrEmpty(sscc), a => a.SubSscc.Contains(sscc))
                  .AndIF(!string.IsNullOrEmpty(machineCode), a => a.MachineCode.Contains(machineCode))
                  .AndIF(!string.IsNullOrEmpty(sourceCode), a => a.SourceCode.Contains(sourceCode)).ToExpression();
            var data = await _dal.FindList(whereExpression);
            var result = data.GroupBy(p => new { p.ProcessOrder, p.MCode, p.BatchCode, p.SubSscc, p.MachineCode, p.SourceCode }).Select(p => p.Sum(p => p.IQuantity)).ToList();

            if (result != null && result.Count > 0)
            {
                return result[0].Value;
            }

            return 0;
        }


        public async Task<MessageModel<string>> GetGetConsumedQTY(ReverseModel reqModel)
        {

            string userID = _uIser.Name.ToString();
            var result = new MessageModel<string>();
            result.success = true;
            try
            {
                //获取当前行实际产出数据
                var pae = await _dal.FindEntity(reqModel.ID);
                if (pae == null)
                {
                    result.msg = "0";
                }
                result.msg = pae.CanReversedQty.ToString();
                return result;
                decimal returnQty = pae.IQuantity.Value;
                //拿到当前数据的SSCC              
                string sscc = pae.SubId;
                if (!string.IsNullOrEmpty(sscc))
                {
                    var qtyModel = await _dal.FindList(p => p.SubId == sscc && p.IQuantity.Value <= 0);
                    if (qtyModel != null)
                    {
                        returnQty = returnQty - qtyModel.Sum(p => Math.Abs(p.IQuantity.Value));
                        if (returnQty < 0)
                        {
                            result.msg = "0";

                        }
                        else
                        {
                            result.msg = returnQty.ToString();
                        }
                    }
                    else
                    {
                        result.msg = returnQty.ToString();

                    }
                }
                else
                {
                    result.msg = returnQty.ToString();

                }
                return result;
            }
            catch (Exception)
            {
                result.success = false;
                result.msg = "0";
                return result;
            }
        }

        public async Task<bool> SaveForm(HistoryViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}