
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using SEFA.PPM.Model.ViewModels.MKM.View;
using SEFA.PPM.Model.ViewModels.MKM.InterfaceView;
using static SEFA.PTM.Services.ConsumeViewServices;
using System;
using SEFA.PPM.Model.Models.PTM;
using SEFA.MKM.Model.Models;
using SEFA.DFM.Model.Models;
using Magicodes.ExporterAndImporter.Core.Extension;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using SEFA.PPM.Model.ViewModels.PTM;
using System.Xml.Linq;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using System.Xml;
using BatchEntity = SEFA.PPM.Model.Models.BatchEntity;

namespace SEFA.PPM.Services
{
	public class TippingEquipmentViewServices : BaseServices<TippingEquipmentViewEntity>, ITippingEquipmentViewServices
	{
		private readonly IBaseRepository<TippingEquipmentViewEntity> _dal;
		private readonly IBaseRepository<TippingWorkOrderViewEntity> _TippingWorkOrderViewEntity;
		private readonly IBaseRepository<TippingMaterialViewEntity> _TippingMaterialViewEntity;
		private readonly IBaseRepository<LineTippingViewEntity> _LineTippingViewEntity;
		private readonly IBaseRepository<TippingVersionPlayViewEntity> _TippingVersionPlayViewEntity;
		private readonly IInfluxDbServices _IInfluxDbServices;
		private readonly IBaseRepository<InfluxOpcTagEntity> _InfluxOpcTagEntity;
		private readonly IBaseRepository<DFM.Model.Models.EquipmentEntity> _eq;
		private readonly IBaseRepository<ProductionOrderEntity> _ProductionOrderEntity;
		public TippingEquipmentViewServices(IBaseRepository<TippingEquipmentViewEntity> dal, IBaseRepository<TippingWorkOrderViewEntity> TippingWorkOrderViewEntity, IBaseRepository<TippingMaterialViewEntity> tippingMaterialViewEntity
			, IBaseRepository<LineTippingViewEntity> lineTippingViewEntity, IBaseRepository<TippingVersionPlayViewEntity> TippingVersionPlayViewEntity, IInfluxDbServices IInfluxDbServices, IBaseRepository<InfluxOpcTagEntity> InfluxOpcTagEntity, IBaseRepository<DFM.Model.Models.EquipmentEntity> eq
, IBaseRepository<ProductionOrderEntity> productionOrderEntity)
		{
			this._dal = dal;
			base.BaseDal = dal;
			_TippingWorkOrderViewEntity = TippingWorkOrderViewEntity;
			_TippingMaterialViewEntity = tippingMaterialViewEntity;
			_LineTippingViewEntity = lineTippingViewEntity;
			_TippingVersionPlayViewEntity = TippingVersionPlayViewEntity;
			_IInfluxDbServices = IInfluxDbServices;
			_InfluxOpcTagEntity = InfluxOpcTagEntity;
			_eq = eq;
			_ProductionOrderEntity = productionOrderEntity;
		}
		/// <summary>
		/// 当日产线投料进度
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<LineTippingModel> GetLineTipping(LineTippingViewRequestModel reqModel)
		{
			//List<LineTippingModel> models= new List<LineTippingModel>();
			LineTippingModel model = new LineTippingModel();
			var whereExpression = Expressionable.Create<LineTippingViewEntity>()
				.And(p => p.LineId == reqModel.LineId)
				.ToExpression();
			var data = await _LineTippingViewEntity.FindEntity(whereExpression);
			var eqmentName = await _eq.FindEntity(reqModel.LineId);
			if (eqmentName != null)
			{
				if (eqmentName.EquipmentName.Contains("#"))
				{
					model.LineName = eqmentName.EquipmentName.Substring(eqmentName.EquipmentName.IndexOf('#') + 1);
				}
				else
				{
					model.LineName = eqmentName.EquipmentName;
				}
			}

			if (data != null)
			{
				model.Complete = data.Complete == null ? 0 : data.Complete;
				model.Yt = data.Yt == null ? 0 : data.Yt;
				model.Total = data.Total == null ? 0 : data.Total;

			}
			else
			{
				model.Complete = 0;
				model.Yt = 0;
				model.Total = 0;
			}

			return model;
		}
		/// <summary>
		/// 当日配方生产进度
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<TippingVersionModel> GetTippingVersion(TippingVersionPlayViewRequestModel reqModel)
		{
			TippingVersionModel model = new TippingVersionModel();
			var today = DateTime.Today;

			int Yt = 0;
			int Yb = 0;
			int total = 0;
			var speed = 0;
			model.SapFormula = "";

			var equipmentList = await _dal.Db.Queryable<DFM.Model.Models.EquipmentEntity, EquipmentFunctionEntity, FunctionEntity>(
				(eq, ef, f) => new object[]
				{
							JoinType.Inner , eq.ID == ef.EquipmentId,
							JoinType.Inner, ef.FunctionId == f.ID
				})
				.Where((eq, ef, f) => eq.Enabled == 1
								 && eq.Deleted == 0
								 && eq.LineId == reqModel.LineId
								 && f.FunctionCode == "Tipping"
								 )
				.Select((eq, ef, f) => new
				{
					eq.EquipmentName,
					eq.EquipmentCode,
					eq.ID,
					eq.LineId
				})
				.ToListAsync();
			if (equipmentList?.Count > 0)
			{
				var equipmentIds = equipmentList.Select(x => x.ID);
				var orderList = await _dal.Db.Queryable<ProductionOrderEntity>()
					 .InnerJoin<PoProducedExecutionEntity>((t, p) => t.ID == p.ProductionOrderId)
					 .Where((t, p) => p.Status == "1" && p.StartTime != null && p.EndTime == null &&
					 //t.PlanDate == today &&
					 equipmentIds.Contains(p.RunEquipmentId))
					 .OrderBy((t, p) => p.StartTime)
					 .Select((t, p) => new
					 {
						 p.ID,
						 p.ProductionOrderId,
						 p.StartTime,
						 t.SapFormula
					 })
					 .ToListAsync();
				if (orderList?.Count > 0)
				{
					var exIds = orderList.Select(x => x.ID);
					var orderList1 = orderList.FirstOrDefault();
					if (orderList1 != null)
					{
						model.SapFormula = orderList1.SapFormula;
						var batchs = await _dal.Db.Queryable<ProductionOrderEntity>()
						 .InnerJoin<PoProducedExecutionEntity>((t, p) => t.ID == p.ProductionOrderId)
						 .InnerJoin<BatchEntity>((t, p, b) => b.ID == p.BatchId)
						 .Where((t, p, b) => exIds.Contains(p.ID) &&
						 t.SapFormula == orderList1.SapFormula)
						 .Select((t, p, b) => new
						 {
							 p.BatchId,
							 b.PrepStatus,
						 })
						 .Distinct()
						 .ToListAsync();
						Yb = batchs.Where(p => p.PrepStatus == "3" || p.PrepStatus == "4" || p.PrepStatus == "6" || p.PrepStatus == "7" || p.PrepStatus == "8" || p.PrepStatus == "9").Count();
						Yt = batchs.Where(p => p.PrepStatus == "9").Count();
						total = batchs.Count();
						var production1 = await _ProductionOrderEntity.FindEntity(orderList1.ProductionOrderId);
						if (production1 != null)
						{
							var gzjList = await _eq.FindList(p => p.Level == "Unit" && p.LineId == production1.FillLineId && p.Enabled == 1 && p.Deleted == 0 && p.EquipmentName.Contains("灌装机"));
							if (gzjList?.Count > 0)
							{
								var gz = gzjList.FirstOrDefault();
								var opc = await _InfluxOpcTagEntity.FindEntity(p => p.EquipmentId == gz.ID && p.Name.Contains("灌装速度"));
								if (opc != null)
								{
									try
									{
										//获取点位状态
										speed = (int)Convert.ToDecimal((await _IInfluxDbServices.GetLastInfluxData(DateTime.Now.AddMinutes(-10), DateTime.Now, null, opc.Tag))?.Value);
									}
									catch (Exception)
									{
										speed = 0;

									}
								}
							}
						}
					}
				}
			}
			model.Yt = Yt;
			model.Yb = Yb;
			model.Total = total;
			model.Speed = speed;
			return model;
		}

		/// <summary>
		/// 投料信息
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>

		public async Task<List<TippingInfo>> GetFeedingMes(TippingEquipmentViewRequestModel reqModel)
		{
			List<TippingMaterialViewEntity> Materiallist = new List<TippingMaterialViewEntity>();
			List<TippingInfo> TippingInfo1 = new List<TippingInfo>();
			//查找设备
			var whereExpression = Expressionable.Create<TippingEquipmentViewEntity>()
				.And(p => p.LineId == reqModel.LineId)
				.ToExpression();
			var data = await _dal.FindList(whereExpression);
			//获取所有OPC点位配置
			var opc = await _InfluxOpcTagEntity.FindList(p => p.EquipmentId != null);

			var EquipmentList = await _dal.Db.Queryable<DFM.Model.Models.EquipmentEntity, EquipmentFunctionEntity, FunctionEntity>(
						(eq, ef, f) => new object[]
						{
					JoinType.Inner , eq.ID == ef.EquipmentId,
					JoinType.Inner, ef.FunctionId == f.ID
						})
						.Where((eq, ef, f) => eq.Enabled == 1
										 && eq.Deleted == 0
										 && eq.LineId == reqModel.LineId
										 && f.FunctionCode == "Tipping"
										 )
						.Select((eq, ef, f) => new
						{
							eq.EquipmentName,
							eq.EquipmentCode,
							eq.ID,
							eq.LineId
						})
						.ToListAsync();
			var EquipmentLists = EquipmentList.OrderBy(p => p.EquipmentName);
			foreach (var item1 in EquipmentLists)
			{
				TippingInfo tippingInfo = new TippingInfo();
				List<TippingMaterial> mtrList = new List<TippingMaterial>();

				var data1 = data.Where(p => p.EquipmentName == item1.EquipmentName).FirstOrDefault();
				if (data1 != null)
				{
					//找工单和批次
					var workOrder = await _TippingWorkOrderViewEntity.FindEntity(p => p.RunEquipmentId == data1.RunEquipmentId);
					if (workOrder != null)
					{
						tippingInfo.EquipmentName = data1.EquipmentName;
						tippingInfo.PlanQty = workOrder.PlanQty;
						tippingInfo.Unit = workOrder.Unit;
						tippingInfo.ProductionOrderNo = workOrder.ProductionOrderNo;
						tippingInfo.MaterialVersionNumber = workOrder.MaterialVersionNumber;
						//tippingInfo.SortOrder = workOrder2.SortOrder;
						var Influxdbquantity = 1;
						string Status = "";
						//根据设备找配置的采集点位
						var opc1 = opc.Where(p => p.EquipmentId == data1.RunEquipmentId && p.Name.Contains("状态")).FirstOrDefault();
						if (opc1 != null)
						{
							try
							{

								Influxdbquantity = (int)Convert.ToDecimal((await _IInfluxDbServices.GetLastInfluxData(DateTime.Now.AddMinutes(-10), DateTime.Now, null, opc1.Tag))?.Value);
							}
							catch (Exception)
							{

								Influxdbquantity = 1;
							}

							//没找到点位则赋值状态为无排产
							switch (Influxdbquantity)
							{
								case 0:
									tippingInfo.state = "运行中";
									break;
								case 1:
									tippingInfo.state = "未启动";
									break;
								case 2:
									tippingInfo.state = "故障";
									break;
								case 3:
									tippingInfo.state = "CIP";
									break;
								case 4:
									tippingInfo.state = "下缸作业";
									break;
								default:
									break;
							}
						}
						else
						{
							tippingInfo.state = "未启动";
						}

						var containerHistories = await _dal.Db.Queryable<ContainerHistoryEntity>().Where(x => x.BatchId == workOrder.ID && (x.Type == "Inventory tipping Start" || x.Type == "Inventory tipping Complate")).ToListAsync();
						var list = await _dal.Db.Queryable<PoConsumeRequirementEntity>().Where(x => x.ProductionOrderId == workOrder.ProductionOrderId)?.OrderByDescending(x => x.SortOrder)?.Select(x => x.SortOrder)?.Distinct()?.ToListAsync();
						tippingInfo.SortOrder = list.LastOrDefault();
						tippingInfo.BatchState = "未开始";
						//倒序遍历
						for (int i = 0; i < list.Count; i++)
						{
							var sort = list[i].ToString();
							//存在完成 直接是完成
							if (containerHistories.Exists(x => x.Comment == sort && x.Type == "Inventory tipping Complate"))
							{
								tippingInfo.SortOrder = list[i];
								tippingInfo.BatchState = "投料完成";
								break;
							}
							//不存在完成 但存在开始则为开始
							else if (containerHistories.Exists(x => x.Comment == sort && x.Type == "Inventory tipping Start"))
							{
								tippingInfo.SortOrder = list[i];
								tippingInfo.BatchState = "运行中";
								break;
							}
							//都不存在则找下一个更小的顺序，下一个顺序有完成，则当前顺序就是未开始
							else
							{
								var j = i + 1;
								if (j < list.Count)
								{
									var j_str = j.ToString();
									if (containerHistories.Exists(x => x.Comment == j_str && x.Type == "Inventory tipping Complate"))
									{
										tippingInfo.SortOrder = list[i];
										tippingInfo.BatchState = "未开始";
										break;
									}
								}
							}
						}
						var materials = await _dal.Db.Queryable<TippingMlistViewEntity>()
							.Where(x => x.BatchId == workOrder.ID && x.SortOrder == tippingInfo.SortOrder)
							.Select(x => new TippingMaterial
							{
								FeedStates = x.FeedStates,
								Code = x.MaterialCode,
								Name = x.MaterialName
							}).ToListAsync();
						tippingInfo.MaterialList = materials ?? new List<TippingMaterial>();
						////找物料
						//var materialList = await _TippingMaterialViewEntity.FindList(p => p.RunEquipmentId == data1.RunEquipmentId && p.ProductionOrderId == workOrder.ProductionOrderId);
						//var materialLists = materialList.GroupBy(p => new { p.Code, p.SortOrder, p.Name }).Select(p => new { p.Key.Code, p.Key.Name, p.Key.SortOrder, number = p.Sum(p => p.Number) });
						//var materialLists2 = materialLists.GroupBy(p => p.SortOrder).ToList();

						//bool shouldBreak = false;
						//foreach (var item in materialLists2)
						//{
						//	tippingInfo.SortOrder = item.Key;
						//	List<TippingMaterial> mtrList2 = new List<TippingMaterial>();
						//	if (item == materialLists2.Last())
						//	{
						//		foreach (var item2 in item)
						//		{
						//			TippingMaterial tipping = new TippingMaterial();
						//			tipping.FeedStates = item2.number == 2 ? 1 : 0;
						//			tipping.Name = item2.Name;
						//			tipping.Code = item2.Code;
						//			mtrList.Add(tipping);
						//		}
						//	}
						//	else
						//	{
						//		foreach (var item2 in item)
						//		{
						//			TippingMaterial tipping = new TippingMaterial();
						//			tipping.FeedStates = item2.number == 2 ? 1 : 0;
						//			tipping.Name = item2.Name;
						//			tipping.Code = item2.Code;
						//			mtrList2.Add(tipping);
						//		}
						//		if (mtrList2 != null)
						//		{
						//			var FeedStateList = mtrList2.Where(p => p.FeedStates == 0).ToList();
						//			if (FeedStateList.Count > 0)
						//			{
						//				mtrList = mtrList2;
						//				shouldBreak = true;
						//				break;
						//			}
						//		}
						//	}
						//	if (shouldBreak)
						//	{
						//		break; // 退出外循环
						//	}
						//}
						//if (mtrList.Count > 0)
						//{
						//	var bState = mtrList.Where(p => p.FeedStates == 0).Count();
						//	if (bState > 0)
						//	{
						//		tippingInfo.BatchState = "运行中";
						//	}
						//	else
						//	{
						//		tippingInfo.BatchState = "投料完成";
						//	}
						//}
						//else
						//{
						//	tippingInfo.BatchState = "未开始";
						//}


						//tippingInfo.MaterialList = mtrList;
						//TippingInfo1.Add(tippingInfo);
					}

				}
				else
				{
					tippingInfo.EquipmentName = item1.EquipmentName;
					tippingInfo.state = "未启动";
				}
				TippingInfo1.Add(tippingInfo);
			}



			return TippingInfo1;
		}


		#region 投料信息复制
		/*
		 *  public async Task<List<TippingInfo>> GetFeedingMes(TippingEquipmentViewRequestModel reqModel)
		{
			List<TippingMaterialViewEntity> Materiallist = new List<TippingMaterialViewEntity>();
			List<TippingInfo> TippingInfo1 = new List<TippingInfo>();
			//查找设备
			var whereExpression = Expressionable.Create<TippingEquipmentViewEntity>()
				.And(p=>p.LineId==reqModel.LineId)
				.ToExpression();
			var data = await _dal.FindList(whereExpression);
			//获取所有OPC点位配置
			var opc = await _InfluxOpcTagEntity.FindList(p => p.EquipmentId != null);

			var EquipmentList = await _dal.Db.Queryable<DFM.Model.Models.EquipmentEntity, EquipmentFunctionEntity, FunctionEntity>(
						(eq, ef, f) => new object[]
						{
					JoinType.Inner , eq.ID == ef.EquipmentId,
					JoinType.Inner, ef.FunctionId == f.ID
						})
						.Where((eq, ef, f) => eq.Enabled == 1
										 && eq.Deleted==0
										 && eq.LineId== reqModel.LineId
										 && f.FunctionCode== "Tipping"
										 )

						.Select((eq, ef, f) => new
						{
							eq.EquipmentName,
							eq.EquipmentCode,
							eq.ID,
						})
						.ToListAsync();
			foreach (var item1 in EquipmentList)
			{
				TippingInfo tippingInfo = new TippingInfo();
				List<TippingMaterial> mtrList = new List<TippingMaterial>();
				tippingInfo.EquipmentName = item1.EquipmentName;
				if (true)
				{

				}
				foreach (var item in data)
				{
					//找工单和批次
					var workOrder = await _TippingWorkOrderViewEntity.FindList(p => p.RunEquipmentId == item.RunEquipmentId);
					var workOrder2 = workOrder.OrderBy(p => p.SortOrder).FirstOrDefault();
					if (workOrder2 != null)
					{
						//根据设备找配置的采集点位
						var opc1 = opc.Where(p => p.EquipmentId == item.RunEquipmentId).ToList();

						decimal Influxdbquantity = Convert.ToDecimal((await _IInfluxDbServices.GetLastInfluxData(DateTime.Now.AddMinutes(-10), DateTime.Now, null, "tag1"))?.Value);

						tippingInfo.EquipmentName = item.EquipmentName;
						tippingInfo.PlanQty = workOrder2.PlanQty;
						tippingInfo.Unit = workOrder2.Unit;
						tippingInfo.ProductionOrderNo = workOrder2.ProductionOrderNo;
						tippingInfo.MaterialVersionNumber = workOrder2.MaterialVersionNumber;
						tippingInfo.state = 1;  //设备状态后续调整
												//tippingInfo.RunEquipmentId = workOrder2.RunEquipmentId;
												//tippingInfo.ProductionOrderId = workOrder2.ProductionOrderId;
												//tippingInfo.PoSegmentRequirementId = workOrder2.PoSegmentRequirementId;
						tippingInfo.SortOrder = workOrder2.SortOrder;
					}
					//找物料
					var materialList = await _TippingMaterialViewEntity.FindList(p => p.RunEquipmentId == item.RunEquipmentId);
					for (int i = 0; i < materialList.Count; i++)
					{
						TippingMaterial tipping = new TippingMaterial();
						tipping.FeedStates = materialList[i].FeedStates == null ? 0 : materialList[i].FeedStates;
						tipping.Name = materialList[i].Name;
						tipping.Code = materialList[i].Code;
						mtrList.Add(tipping);
					}
					if (mtrList.Count > 0)
					{
						var bState = mtrList.Where(p => p.FeedStates == 0).Count();
						if (bState > 0)
						{
							tippingInfo.BatchState = "运行中";
						}
						else
						{
							tippingInfo.BatchState = "结束";
						}
					}
					else
					{
						tippingInfo.BatchState = "未开始";
					}


					tippingInfo.MaterialList = mtrList;
					TippingInfo1.Add(tippingInfo);
				}
			}



			return TippingInfo1;
		}
		 */
		#endregion
		public async Task<PageModel<TippingEquipmentViewEntity>> GetPageList(TippingEquipmentViewRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<TippingEquipmentViewEntity>()
							 .ToExpression();
			var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
			return data;
		}

		public async Task<bool> SaveForm(TippingEquipmentViewEntity entity)
		{
			if (string.IsNullOrEmpty(entity.ID))
			{
				return await this.Add(entity) > 0;
			}
			else
			{
				return await this.Update(entity);
			}
		}
	}
}