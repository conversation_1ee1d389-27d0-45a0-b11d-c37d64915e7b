
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using System;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using SEFA.PPM.Model.ViewModels.MKM.View;
using SEFA.MKM.Model.Models;
using System.Runtime.InteropServices;
using System.Linq.Expressions;
using SEFA.Base.Common.WebApiClients.HttpApis;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using SEFA.PPM.Model.Models.PTM;
using System.Collections;
using System.Diagnostics.Eventing.Reader;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using System.Text.RegularExpressions;
using CsvHelper;
using System.Numerics;
using StackExchange.Profiling.Internal;
using SEFA.PPM.Model.ViewModels.PPM;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Common.HttpRestSharp;
using static System.Collections.Specialized.BitVector32;
using SEFA.Base.Common.HttpContextUser;
using Microsoft.AspNetCore.Http.Metadata;
using SEFA.Base.Common.LogHelper;
using SEFA.PPM.Model.Models.MKM;

namespace SEFA.PPM.Services
{
	public class PackTechnologyViewServices : BaseServices<PackTechnologyViewEntity>, IPackTechnologyViewServices
	{
		private readonly IBaseRepository<PackTechnologyViewEntity> _dal;
		private readonly IBaseRepository<PackDowntimeViewEntity> _PackDowntimeViewEntity;
		private readonly IBaseRepository<EquipmentEntity> _EquipmentEntity;
		private readonly IBaseRepository<PackLastOverTimeViewEntity> _PackLastOverTimeViewEntity;
		private readonly IBaseRepository<PackCurrentViewEntity> _PackCurrentViewEntity;
		private readonly IBaseRepository<PackNextCurrentViewEntity> _PackNextCurrentViewEntity;
		private readonly IBaseRepository<PackJitViewEntity> _PackJitViewEntity;
		private readonly IBaseRepository<InventoryControlEntity> _InventoryControlEntity;
		private readonly IBaseRepository<InfluxOpcTagEntity> _InfluxOpcTagEntity;
		private readonly IInfluxDbServices _IInfluxDbServices;
		private readonly IBaseRepository<OeeReportViewEntity> _OeeReportViewEntity;
		private readonly IBaseRepository<DowntimeEntity> _DowntimeEntity;
		private readonly IBaseRepository<DowntimeReasonEntity> _DowntimeReasonEntity;
		private readonly IBaseRepository<DowntimeGroupEntity> _DowntimeGroupEntity;
		private readonly IBaseRepository<DowntimeCategroyEntity> _DowntimeCategroyEntity;
		private readonly IBaseRepository<ProductionOrderEntity> _ProductionOrderEntity;
		private readonly IBaseRepository<PoProducedExecutionEntity> _PoProducedExecutionEntity;
		private readonly IBaseRepository<PoProducedActualEntity> _PoProducedActualEntity;
		private readonly IBaseRepository<SappackorderEntity> _SappackorderEntity;
		private readonly IBaseRepository<KpitgtEntity> _KpitgtEntity;
		private readonly IBaseRepository<FormulascheduleEntity> _FormulascheduleEntity;
		private readonly IBaseRepository<PerformanceEntity> _PerformanceEntity;
		private readonly IUser _user;
		public PackTechnologyViewServices(IBaseRepository<PackTechnologyViewEntity> dal, IBaseRepository<PackDowntimeViewEntity> PackDowntimeViewEntity, IBaseRepository<EquipmentEntity> EquipmentEntity
			, IBaseRepository<PackLastOverTimeViewEntity> packLastOverTimeViewEntity, IBaseRepository<PackCurrentViewEntity> packCurrentViewEntity, IBaseRepository<PackNextCurrentViewEntity> packNextCurrentViewEntity, IBaseRepository<PackJitViewEntity> packJitViewEntity, IBaseRepository<InventoryControlEntity> InventoryControlEntity
			, IBaseRepository<InfluxOpcTagEntity> influxOpcTagEntity, IInfluxDbServices IInfluxDbServices
			, IBaseRepository<OeeReportViewEntity> oeeReportViewEntity, IBaseRepository<DowntimeEntity> downtimeEntity, IBaseRepository<DowntimeGroupEntity> downtimeGroupEntity, IBaseRepository<DowntimeCategroyEntity> downtimeCategroyEntity, IBaseRepository<ProductionOrderEntity> productionOrderEntity, IBaseRepository<PoProducedExecutionEntity> poProducedExecutionEntity, IBaseRepository<DowntimeReasonEntity> downtimeReasonEntity, IBaseRepository<PoProducedActualEntity> poProducedActualEntity, IBaseRepository<SappackorderEntity> sappackorderEntity, IBaseRepository<KpitgtEntity> kpitgtEntity, IBaseRepository<FormulascheduleEntity> formulascheduleEntity, IBaseRepository<PerformanceEntity> performanceEntity
			, IUser user
			)
		{
			this._dal = dal;
			base.BaseDal = dal;
			_PackDowntimeViewEntity = PackDowntimeViewEntity;
			_EquipmentEntity = EquipmentEntity;
			_PackLastOverTimeViewEntity = packLastOverTimeViewEntity;
			_PackCurrentViewEntity = packCurrentViewEntity;
			_PackNextCurrentViewEntity = packNextCurrentViewEntity;
			_PackJitViewEntity = packJitViewEntity;
			_InventoryControlEntity = InventoryControlEntity;
			_InfluxOpcTagEntity = influxOpcTagEntity;
			_IInfluxDbServices = IInfluxDbServices;
			_OeeReportViewEntity = oeeReportViewEntity;
			_DowntimeEntity = downtimeEntity;
			_DowntimeGroupEntity = downtimeGroupEntity;
			_DowntimeCategroyEntity = downtimeCategroyEntity;
			_ProductionOrderEntity = productionOrderEntity;
			_PoProducedExecutionEntity = poProducedExecutionEntity;
			_DowntimeReasonEntity = downtimeReasonEntity;
			_PoProducedActualEntity = poProducedActualEntity;
			_SappackorderEntity = sappackorderEntity;
			_KpitgtEntity = kpitgtEntity;
			_FormulascheduleEntity = formulascheduleEntity;
			_PerformanceEntity = performanceEntity;
			_user = user;

		}

		public async Task<List<PackTechnologyViewEntity>> GetList(PackTechnologyViewRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<PackTechnologyViewEntity>()
							 .ToExpression();
			var data = await _dal.FindList(whereExpression);
			return data;
		}

		public async Task<PageModel<PackTechnologyViewEntity>> GetPageList(PackTechnologyViewRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<PackTechnologyViewEntity>()
							 .ToExpression();
			var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);

			return data;
		}

		public async Task<bool> SaveForm(PackTechnologyViewEntity entity)
		{
			if (string.IsNullOrEmpty(entity.ID))
			{
				return await this.Add(entity) > 0;
			}
			else
			{
				return await this.Update(entity);
			}
		}
		/// <summary>
		/// JIT物料状态指示灯
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<List<PackJitModel>> GetJITMaterial(PackTechnologyViewRequestModel reqModel)
		{
			List<PackJitModel> packJits = new List<PackJitModel>();
			List<PackJitMaterial> packJits1 = new List<PackJitMaterial>();
			PackJitMaterial packJitMaterial = new PackJitMaterial();
			for (int i = 0; i < reqModel.LineIds.Length; i++)
			{
				PackJitModel jitModel = new PackJitModel();
				var data = await _PackJitViewEntity.FindList(p => p.LineId == reqModel.LineIds[i]);
				for (int j = 0; j < data.Count; j++)
				{
					if (j < 1)
					{
						jitModel.LineName = data[i].LineName;
					}
					List<InventoryControlEntity> list = await _InventoryControlEntity.FindList(p => p.MaterialId != null);
					var mmodel = list.Where(p => p.MaterialId == data[i].MaterialId).FirstOrDefault();
					int minquy = 0;//安全水位（h）
					int maxquy = 0;//最高水位（h）

					if (mmodel != null)
					{
						minquy = mmodel.MinQuantity;
						maxquy = mmodel.MaxQuantity;
					}
					DateTime stars = DateTime.Now;
					DateTime highends = stars.AddHours(maxquy);
					DateTime minends = stars.AddHours(minquy);

					//最高水位
					decimal hight_Water = Convert.ToDecimal(data.Where(p => p.CreateDate >= stars && p.CreateDate <= highends).Sum(p => p.NeedQuantity));
					//最低水位
					decimal min_Water = Convert.ToDecimal(data.Where(p => p.CreateDate >= stars && p.CreateDate <= minends).Sum(p => p.NeedQuantity));



					packJitMaterial.MaterialName = data[i].MaterialName;
					if (data[i].Inventqty > hight_Water)
					{
						packJitMaterial.state = "green";
					}
					else if (data[i].Inventqty < min_Water)
					{
						packJitMaterial.state = "red";
					}
					else if (data[i].Inventqty < hight_Water && data[i].Inventqty > min_Water)
					{
						packJitMaterial.state = "yellow";
					}
				}

				packJits1.Add(packJitMaterial);
				jitModel.PackJit = packJits1;
				packJits.Add(jitModel);
			}

			return packJits;
		}

		/// <summary>
		/// 包装看板-获取PAC-ONPK工艺提醒
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<List<PackTechnologyViewEntity>> GetPackTechnology(PackTechnologyViewRequestModel reqModel)
		{
			List<PackTechnologyViewEntity> model = new List<PackTechnologyViewEntity>();
			var data = await _dal.FindList(p => reqModel.LineIds.Any(e => e == p.LineName));
			foreach (var item in data)
			{
				PackTechnologyViewEntity models = item;

				if (item.LineName.Contains("#"))
				{
					models.LineName = item.LineName.Substring(item.LineName.IndexOf('#') + 1);
				}
				else
				{
					models.LineName = item.LineName;
				}
				model.Add(models);
			}
			return model;
		}
		/// <summary>
		/// 包装看板-停机时间
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>

		public async Task<List<DowntimeModel>> GetDowntime(PackTechnologyViewRequestModel reqModel)
		{
			List<DowntimeModel> downtimeModels = new List<DowntimeModel>();

			if (reqModel == null || reqModel.LineIds == null)
			{
				return downtimeModels;
			}

			var lineIdsSet = new HashSet<string>(reqModel.LineIds, StringComparer.OrdinalIgnoreCase);
			var startTime = DateTime.Today;
			var endTime = startTime.AddDays(1).AddSeconds(-1);
			var performanceEntities = await _dal.Db.Queryable<PerformanceEntity>()
				.Where(p => lineIdsSet.Contains(p.LineName) &&
				   (p.Categroy == "计划停机" || p.Categroy == "非计划停机") &&
				   ((p.StartTimeUtc >= startTime && p.EndTimeUtc != null && p.EndTimeUtc <= endTime) ||
				   (p.EndTimeUtc == null && p.StartTimeUtc <= endTime))
				)
				.ToListAsync() ?? new List<PerformanceEntity>();

			// 提前对 performanceEntities 进行分组，减少每次循环中的计算量
			var groupedPerformanceEntities = performanceEntities
				.Where(x => x.LineName != null && x.Categroy != null)
				.GroupBy(x => new { x.LineName, x.Categroy })
				.ToDictionary(g => new { g.Key.LineName, g.Key.Categroy }, g => (long)g.Sum(x => x.TimeDifferenceInSeconds)); // 使用 long 类型

			var lines = await _EquipmentEntity.Db.Queryable<EquipmentEntity>().Where(p => lineIdsSet.Contains(p.EquipmentCode)).ToListAsync();

			foreach (var item in reqModel.LineIds)
			{
				var line = lines.FirstOrDefault(l => string.Equals(l.EquipmentCode, item, StringComparison.OrdinalIgnoreCase));

				if (line == null)
				{
					continue;
				}

				DowntimeModel downtime = new DowntimeModel();

				if (!string.IsNullOrEmpty(line.EquipmentName))
				{
					int hashIndex = line.EquipmentName.IndexOf('#');
					downtime.LineName = hashIndex == -1 ? line.EquipmentName : line.EquipmentName.Substring(hashIndex + 1);
				}
				else
				{
					downtime.LineName = "未知";
				}

				long plannedStoppages = 0;
				long unplannedStoppages = 0;

				if (groupedPerformanceEntities.TryGetValue(new { LineName = item, Categroy = "计划停机" }, out var plannedTotal))
				{
					plannedStoppages = plannedTotal;
				}

				if (groupedPerformanceEntities.TryGetValue(new { LineName = item, Categroy = "非计划停机" }, out var unplannedTotal))
				{
					unplannedStoppages = unplannedTotal;
				}

				downtime.PlannedStoppages = Math.Round(plannedStoppages / 3600.0, 2);
				downtime.UnplannedStoppages = Math.Round(unplannedStoppages / 3600.0, 2);
				downtime.InTotal = Math.Round((plannedStoppages + unplannedStoppages) / 3600.0, 2);

				downtimeModels.Add(downtime);
			}

			return downtimeModels;
		}

		/// <summary>
		/// 包装看板-停机时间
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>

		public async Task<List<DowntimeModel>> GetDowntime1(PackTechnologyViewRequestModel reqModel)
		{
			List<DowntimeModel> downtimeModels = new List<DowntimeModel>();
			for (int i = 0; i < reqModel.LineIds.Length; i++)
			{
				DowntimeModel downtime = new DowntimeModel();
				#region 计划停机
				//计划停机
				double planDuration = 0;
				var planStop = await _PackDowntimeViewEntity.FindList(p => p.LineCode == reqModel.LineIds[i] && (p.Description == "Planned Stoppages" || p.Description == "计划停机"));
				foreach (var item in planStop)
				{
					if (item.StartTimeUtc != null && item.EndTimeUtc != null)
					{
						//把事件的开始时间和结束时间转换成分钟
						TimeSpan stateOfDay = TimeSpan.Parse(item.StartTimeUtc.Value.ToString("HH:mm"));
						TimeSpan endOfDay = TimeSpan.Parse(item.EndTimeUtc.Value.ToString("HH:mm"));
						double minutes1 = stateOfDay.TotalMinutes;
						double minutes2 = endOfDay.TotalMinutes;
						double minutes = minutes2 - minutes1;
						planDuration += minutes;//累加计划停机下的原因时长
					}
					//var BoilingEndTime = MinutesToHoursMinutes(Convert.ToInt32(minutes2));//转换成时间格式
				}
				#endregion
				#region 非计划停机
				//非计划停机
				double UpPlanDuration = 0;
				var UnplanStop = await _PackDowntimeViewEntity.FindList(p => p.LineCode == reqModel.LineIds[i] && (p.Description == "Unplanned Stoppages" || p.Description == "非计划停机"));
				foreach (var item in UnplanStop)
				{
					if (item.StartTimeUtc != null && item.EndTimeUtc != null)
					{
						//把事件的开始时间和结束时间转换成分钟
						TimeSpan stateOfDay = TimeSpan.Parse(item.StartTimeUtc.Value.ToString("HH:mm"));
						TimeSpan endOfDay = TimeSpan.Parse(item.EndTimeUtc.Value.ToString("HH:mm"));
						double minutes1 = stateOfDay.TotalMinutes;
						double minutes2 = endOfDay.TotalMinutes;
						double minutes = minutes2 - minutes1;
						UpPlanDuration += minutes;//累加非计划停机下的原因时长
					}
				}
				#endregion
				//获取产线名称
				var c = reqModel.LineIds[i];
				var lineName = await _EquipmentEntity.FindEntity(p => p.EquipmentCode == reqModel.LineIds[i]);
				//拆解产线获取#数据
				var str = lineName.EquipmentName.Substring(lineName.EquipmentName.IndexOf('#') + 1);
				//分钟转换成小时数存入
				downtime.LineName = str;
				double A = Math.Round(planDuration / 60.0, 3);
				double B = Math.Round(UpPlanDuration / 60.0, 3);
				downtime.PlannedStoppages = A;
				downtime.UnplannedStoppages = B;
				downtime.InTotal = A + B;
				downtimeModels.Add(downtime);
			}
			return downtimeModels;
		}

		/// <summary>
		/// 包装看板-当前灌装工单信息
		/// </summary>Current work order
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<List<CurrentOrderInfoMode>> CurrentWorkOrderInfo(PackTechnologyViewRequestModel reqModel)
		{
			List<CurrentOrderInfoMode> packCurrents = new List<CurrentOrderInfoMode>();
			try
			{
				for (int i = 0; i < reqModel.LineIds.Length; i++)
				{
					#region 当前灌装工单信息
					CurrentOrderInfoMode model = new CurrentOrderInfoMode();
					var models = await _PackCurrentViewEntity.FindList(p => p.LineCode == reqModel.LineIds[i]);
					foreach (var item in models)
					{
						/*if (models != null)
                        {*/
						#region 计算预计结束时间
						//当前时间转换成分钟
						TimeSpan stateOfDay = TimeSpan.Parse(DateTime.Now.ToString("HH:mm"));
						decimal minutes = Convert.ToDecimal(stateOfDay.TotalMinutes);
						decimal minutes1 = 0;
						//工单计划完成时间转换成分钟
						if (!string.IsNullOrEmpty(item.PlanEndTime.ToString()))
						{
							TimeSpan stateOfDay1 = TimeSpan.Parse(item.PlanEndTime.Value.ToString("HH:mm"));
							minutes1 = Convert.ToDecimal(stateOfDay1.TotalMinutes);
						}

						//灌包装速度--暂时拿不到先赋默认值后续再进行修改
						decimal speed = 0m;
						var opc = await _InfluxOpcTagEntity.FindEntity(p => p.EquipmentId == item.ID && p.Name.Contains("灌装速度"));
						if (opc != null)
						{
							try
							{
								//获取点位状态
								speed = (int)Convert.ToDecimal((await _IInfluxDbServices.GetLastInfluxData(DateTime.Now.AddMinutes(-10), DateTime.Now, null, opc.Tag))?.Value);
							}
							catch (Exception)
							{
								speed = 0;

							}
						}

						decimal expEndTime = 0m;
						//预计时间=当前时间+（工单计划总产量-目前已报产量）/（单只产品重量 * 当前灌装速度）
						//20250219预计时间=当前时间+（工单计划总产量-目前已报产量）/  当前灌装速度
						if (speed > 0)
						{
							expEndTime = (decimal)(minutes + (item.PlanQty - item.ActualQuantity) / speed);
						}
						SerilogServer.LogDebug($"预计结束时间=当前时间+（工单计划总产量-目前已报产量）/  当前灌装速度: 计划产量：{item.PlanQty}" + $"实际产出：{item.ActualQuantity}" + $"当前灌装速度：{speed}", "当前灌装工单预计结束时间log");
						//将计算得到的预计结束时间转换成时间格式
						DateTime now = DateTime.Now; // 获取当前时间
						DateTime BoilingEndTime = now.AddMinutes((double)expEndTime);
						SerilogServer.LogDebug($"预计结束时间：{BoilingEndTime}", "当前灌装工单预计结束时间log");
						//DateTime BoilingEndTime = MinutesToHoursMinutes(Convert.ToInt32(expEndTime));//转换成时间格式

						//当前进度=计算得到的预计结束时间-工单计划 结束时间
						double schedule = Convert.ToDouble(expEndTime - minutes1);
						double A = Math.Round(schedule / 60.0, 3);

						#endregion

						if (item.LineName.Contains("#"))
						{
							model.LineName = item.LineName.Substring(item.LineName.IndexOf('#') + 1);
						}
						else
						{
							model.LineName = item.LineName;
						}
						model.Description = item.Description;
						model.MaterialName = item.MaterialName;
						model.Ntgew = item.Bezei;
						model.StartTime = item.StartTime;
						model.ExpEndTime = BoilingEndTime;
						model.CurrentProgress = A + "h";
					}
					#endregion

					#region 下一张灌装工单信息
					//下一张灌装工单信息
					//NextOrderInfoModel model = new NextOrderInfoModel();
					var models1 = await _PackCurrentViewEntity.FindList(p => p.LineCode == reqModel.LineIds[i]);

					//               var todayA = DateTime.Today;
					//               MessageModel<List<SortFormulaTreeModel>> apiResult_equipmentActionProperties = await HttpHelper.PostAsync<List<SortFormulaTreeModel>>("PPM", "/ppm/Formulaschedule/GetSortFormulaTree", _user.GetToken(), new { lineId = reqModel.LineIds[i], startTime = todayA });
					//               var equipmentActionProperties = apiResult_equipmentActionProperties.response;
					//if (equipmentActionProperties == null || equipmentActionProperties.Count == 0)
					//{
					//	//continue;
					//	//result.msg = "equipmentActionProperties为空";
					//	//return result;
					//}
					//else 
					//{
					//	var II = 1;
					//	List<PackList> packLists = new List<PackList>();
					//                   PackList packList= new PackList();
					//                   foreach (var item in equipmentActionProperties)
					//                   {

					//                       foreach (var item1 in item.children)
					//                       {
					//                           foreach (var item2 in item1.children)
					//                           {

					//                           }
					//                       }
					//                   }

					//               }
					foreach (var item in models1)
					{
						var pro = await _ProductionOrderEntity.FindEntity(p => p.ProductionOrderNo == item.ProductionOrderNo);

						var data = await _PackNextCurrentViewEntity.FindList(p => p.SegmentCode == pro.SegmentCode && p.Description == pro.SapFormula && p.LineId == pro.FillLineId);
						var prodata = data.Where(p => p.Sequence > pro.Sequence).ToList();
						//有已释放且大于当前顺序走的逻辑
						if (prodata.Count > 0)
						{
							var datas = prodata.OrderBy(p => p.Sequence).FirstOrDefault();
							if (datas != null)
							{
								//下一张灌装工单信息
								if (datas.LineName.Contains("#"))
								{
									model.LineName = datas.LineName.Substring(datas.LineName.IndexOf('#') + 1);
								}
								else
								{
									model.LineName = datas.LineName;
								}
								model.state = "";//只有换配方或者换规格
								model.Description1 = datas.Description;
								model.Name = datas.MaterialName;
								model.Ntgew1 = datas.Ntgew;
								model.PlanStateTime = datas.PlanDate.ToString();
							}
						}
						else
						{
							var today = DateTime.Now.Date.ToString("yyyy-MM-dd");
							var formulas = await _FormulascheduleEntity.FindList(p => p.ProduceDate == DateTime.Now.Date && p.FillLineID == pro.FillLineId);
							if (formulas.Count > 0)
							{
								var formulas1 = formulas.FirstOrDefault();
								//var mat = await _FormulascheduleEntity.FindList(p => p.MatId == formulas1.MatId && p.ProduceDate == DateTime.Now.Date);

								var order = await _PackNextCurrentViewEntity.FindList(p => p.MaterialId == formulas1.MatId && p.LineId == pro.FillLineId);
								if (order.Count > 0)
								{
									var datas = order.OrderBy(p => p.Sequence).FirstOrDefault();
									if (datas != null)
									{
										//下一张灌装工单信息
										if (datas.LineName.Contains("#"))
										{
											model.LineName = datas.LineName.Substring(datas.LineName.IndexOf('#') + 1);
										}
										else
										{
											model.LineName = datas.LineName;
										}
										model.state = "";

										if (item.Description == datas.Description)
										{
											model.state = "换配方";//只有换配方或者换规格
										}
										else if (item.Bezei == datas.Ntgew)
										{
											model.state = "换规格";//只有换配方或者换规格
										}
										model.Description1 = datas.Description;
										model.Name = datas.MaterialName;
										model.Ntgew1 = datas.Ntgew;
										model.PlanStateTime = datas.PlanDate.ToString();
									}
								}
								else
								{
									//下一张灌装工单信息
									if (item.LineName.Contains("#"))
									{
										model.LineName = item.LineName.Substring(item.LineName.IndexOf('#') + 1);
									}
									else
									{
										model.LineName = item.LineName;
									}
									model.state = "";
									model.Description1 = "";
									model.Name = "";
									model.Ntgew1 = "";
									model.PlanStateTime = "";

								}

							}
							else
							{
								//下一张灌装工单信息
								if (item.LineName.Contains("#"))
								{
									model.LineName = item.LineName.Substring(item.LineName.IndexOf('#') + 1);
								}
								else
								{
									model.LineName = item.LineName;
								}
								model.state = "";
								model.Description1 = "";
								model.Name = "";
								model.Ntgew1 = "";
								model.PlanStateTime = "";
							}
						}

					}
					#endregion

					packCurrents.Add(model);
				}
			}
			catch (Exception ex)
			{
				string d = ex.StackTrace;
				throw;
			}

			return packCurrents;
		}
		/// <summary>
		/// 分钟转换小时
		/// </summary>
		/// <param name="totalMinutes"></param>
		/// <returns></returns>
		public static string MinutesToHoursMinutes(int totalMinutes)
		{
			TimeSpan time = TimeSpan.FromMinutes(totalMinutes);
			return time.ToString(@"hh\:mm");
		}
		#region 弃（已合并）
		/// <summary>
		/// 包装看板-当天灌装工单last over time 
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<List<LastOverTimeModel>> GetLastOverTime(PackTechnologyViewRequestModel reqModel)
		{
			List<LastOverTimeModel> overTimeModels = new List<LastOverTimeModel>();
			for (int i = 0; i < reqModel.LineIds.Length; i++)
			{
				LastOverTimeModel lastOverTimeModel = new LastOverTimeModel();
				var models = await _PackLastOverTimeViewEntity.FindList(p => p.LineId == reqModel.LineIds[i]);
				//取当天产线最后一条数据的计划结束时间
				var model = models.OrderByDescending(p => p.PlanStartTime).FirstOrDefault();

				//取当天产线最后一条数据的计划结束时间
				var models2 = models.Where(p => p.EndTime != null);
				var model1 = models2.OrderBy(p => p.EndTime).LastOrDefault();
				//把事件的开始时间和结束时间转换成分钟
				double minutes1 = 0;
				double minutes2 = 0;
				if (model != null)
				{
					TimeSpan stateOfDay = TimeSpan.Parse(model.PlanEndTime.Value.ToString("HH:mm"));
					minutes1 = stateOfDay.TotalMinutes;//这条产线最后一条工单的结束时间
				}
				if (model1 != null)
				{
					TimeSpan endOfDay = TimeSpan.Parse(model1.EndTime.Value.ToString("HH:mm"));
					minutes2 = endOfDay.TotalMinutes;//工单执行表中最后一条的结束时间
				}
				var day = 0;
				//产线最后一条工单的结束时间-工单执行表中最后一条的结束时间
				double minutes = minutes1 + (minutes2 - minutes1);
				//判断大于00:00
				if (minutes > 1440)
				{
					//计算时间大于今天的天数 
					day = Convert.ToInt32(minutes) / 1440;
					//时间=总时间-整天数
					minutes = minutes - (day * 1440);
				}
				var BoilingEndTime = MinutesToHoursMinutes(Convert.ToInt32(minutes));//转换成时间格式

				//获取产线名称
				var lineName = await _EquipmentEntity.FindEntity(p => p.ID == reqModel.LineIds[i]);
				if (lineName != null)
				{
					//拆解产线获取#数据
					if (lineName.EquipmentName.Contains("#"))
					{
						lineName.EquipmentName = lineName.EquipmentName.Substring(lineName.EquipmentName.IndexOf('#') + 1);
					}
					lastOverTimeModel.LineName = lineName.EquipmentName;
					lastOverTimeModel.LastOverTime = BoilingEndTime;
					lastOverTimeModel.day = day;
					lastOverTimeModel.RealtSpeed = "0";//实时速度待后面调整
					lastOverTimeModel.StandardSpeed = "0";//标准速度后面调整
					lastOverTimeModel.OEE = "0";//OEE后面调整
					lastOverTimeModel.Expressive = "0";//表现性后面调整
				}
				overTimeModels.Add(lastOverTimeModel);
				overTimeModels.OrderBy(P => P.LineName);
			}
			return overTimeModels;
		}

		#endregion
		/// <summary>
		///下一张灌装工单信息
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<List<NextOrderInfoModel>> NextOrderInfo(PackTechnologyViewRequestModel reqModel)
		{

			List<NextOrderInfoModel> nextOrderInfos = new List<NextOrderInfoModel>();
			for (int i = 0; i < reqModel.LineIds.Length; i++)
			{
				//下一张灌装工单信息
				NextOrderInfoModel model = new NextOrderInfoModel();
				var models = await _PackCurrentViewEntity.FindList(p => p.LineCode == reqModel.LineIds[i]);
				foreach (var item in models)
				{
					var pro = await _ProductionOrderEntity.FindEntity(p => p.ProductionOrderNo == item.ProductionOrderNo);

					var data = await _PackNextCurrentViewEntity.FindList(p => p.SegmentCode == pro.SegmentCode && p.Description == pro.SapFormula && p.LineId == pro.FillLineId);
					var prodata = data.Where(p => p.Sequence > pro.Sequence).ToList();
					//有已释放且大于当前顺序走的逻辑
					if (prodata.Count > 0)
					{
						var datas = prodata.OrderBy(p => p.Sequence).FirstOrDefault();
						if (datas != null)
						{
							//下一张灌装工单信息
							if (datas.LineName.Contains("#"))
							{
								model.LineName = datas.LineName.Substring(datas.LineName.IndexOf('#') + 1);
							}
							else
							{
								model.LineName = datas.LineName;
							}
							model.state = "";//只有换配方或者换规格
							model.Description = datas.Description;
							model.Name = datas.MaterialName;
							model.Ntgew = datas.Ntgew;
							model.PlanStateTime = datas.PlanDate.ToString();
						}
					}
					else
					{
						var today = DateTime.Now.Date.ToString("yyyy-MM-dd");
						var formulas = await _FormulascheduleEntity.FindList(p => p.ProduceDate == DateTime.Now.Date && p.FillLineID == pro.FillLineId);
						if (formulas.Count > 0)
						{
							var formulas1 = formulas.FirstOrDefault();
							//var mat = await _FormulascheduleEntity.FindList(p => p.MatId == formulas1.MatId && p.ProduceDate == DateTime.Now.Date);

							var order = await _PackNextCurrentViewEntity.FindList(p => p.MaterialId == formulas1.MatId && p.LineId == pro.FillLineId);
							if (order.Count > 0)
							{
								var datas = order.OrderBy(p => p.Sequence).FirstOrDefault();
								if (datas != null)
								{
									//下一张灌装工单信息
									if (datas.LineName.Contains("#"))
									{
										model.LineName = datas.LineName.Substring(datas.LineName.IndexOf('#') + 1);
									}
									else
									{
										model.LineName = datas.LineName;
									}
									model.state = "";

									if (item.Description == datas.Description)
									{
										model.state = "换配方";//只有换配方或者换规格
									}
									else if (item.Bezei == datas.Ntgew)
									{
										model.state = "换规格";//只有换配方或者换规格
									}
									model.Description = datas.Description;
									model.Name = datas.MaterialName;
									model.Ntgew = datas.Ntgew;
									model.PlanStateTime = datas.PlanDate.ToString();
								}
							}
							else
							{
								//下一张灌装工单信息
								if (item.LineName.Contains("#"))
								{
									model.LineName = item.LineName.Substring(item.LineName.IndexOf('#') + 1);
								}
								else
								{
									model.LineName = item.LineName;
								}
								model.state = "";
								model.Description = "";
								model.Name = "";
								model.Ntgew = "";
								model.PlanStateTime = "";

							}

						}
						else
						{
							//下一张灌装工单信息
							if (item.LineName.Contains("#"))
							{
								model.LineName = item.LineName.Substring(item.LineName.IndexOf('#') + 1);
							}
							else
							{
								model.LineName = item.LineName;
							}
							model.state = "";
							model.Description = "";
							model.Name = "";
							model.Ntgew = "";
							model.PlanStateTime = "";
						}
					}

					nextOrderInfos.Add(model);
					nextOrderInfos.OrderBy(p => p.LineName);
				}

			}
			return nextOrderInfos;
		}

		/// <summary>
		/// 包装看板-关键设备指示灯
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<List<EquipmentStatusModel>>> GetProductivity(PackTechnologyViewRequestModel reqModel)
		{

			MessageModel<List<EquipmentStatusModel>> result = new MessageModel<List<EquipmentStatusModel>>
			{
				success = false,
				msg = "获取失败！",
			};
			try
			{
				List<EquipmentStatusModel> statusModels = new List<EquipmentStatusModel>();

				//产线下包含获取灌装机、贴标机、装箱机、码垛机
				var data = await _dal.Db.Queryable<EquipmentEntity, EquipmentEntity>(
					(c, p) => new object[]
					{
					JoinType.Inner , c.ID == p.LineId
					})
				   .Where((c, p) => reqModel.LineIds.Any(e => e == c.EquipmentCode)
				   && (p.EquipmentName.Contains("灌装机") || p.EquipmentName.Contains("贴标机") || p.EquipmentName.Contains("装箱机") || p.EquipmentName.Contains("码垛机"))
				   && p.Enabled == 1 && p.Deleted == 0
					)
					.Select((c, p) => new
					{
						lineCode = c.EquipmentCode,
						p.EquipmentName,
						p.EquipmentCode,
						p.ID
					})
					.ToListAsync();

				var startTime = DateTime.Today;
				var endTime = startTime.AddDays(1).AddSeconds(-1);
				var productionOrders = await _dal.Db.Queryable<ProductionOrderEntity>().Where(x => reqModel.LineIds.Contains(x.LineCode) && x.PlanDate >= startTime && x.PlanDate <= endTime).ToListAsync();
				var orderIds = productionOrders.Select(x => x.ID).ToList();
				var orderNo = productionOrders.Select(x => x.ProductionOrderNo).ToList();
				var performances = await _dal.Db.Queryable<PerformanceEntity>().Where(x => x.Categroy != "未占用/无排产" && x.Machine.Contains("灌装机") && !string.IsNullOrEmpty(x.ProductionOrderId) && orderIds.Contains(x.ProductionOrderId)).ToListAsync();
				var orderSpeed = await _dal.Db.Queryable<ProductionOrderEntity>()
					.InnerJoin<PoProducedActualEntity>((po, pa) => po.ID == pa.ProductionOrderId)
					.InnerJoin<SappackorderEntity>((po, pa, sp) => po.ProductionOrderNo == sp.Aufnr)
					.InnerJoin<UnitmanageEntity>((po, pa, sp, u) => u.ID == pa.UnitId)
					.InnerJoin<MaterialSubLotEntity>(((po, pa, sp, u, su) => su.ID == pa.SubLotId))
					.Where(po => po.PoStatus != "4" && orderIds.Contains(po.ID))
					.GroupBy((po, pa, sp, u, su) => new
					{
						po.LineCode,
						pa.ProductionOrderId,
						po.ProductionOrderNo,
						sp.OeeSpeed,
					})
					.Select((po, pa, sp, u, su) => new
					{
						po.LineCode,
						pa.ProductionOrderId,
						po.ProductionOrderNo,
						UnGoodQuantity = SqlFunc.AggregateSum(SqlFunc.IIF(su.ExternalStatus == "1", SqlFunc.IIF(u.Name != "EA", pa.Quantity * sp.Psmng, pa.Quantity), 0)),
						Quantity = SqlFunc.AggregateSum(SqlFunc.IIF(u.Name != "EA", pa.Quantity * sp.Psmng, pa.Quantity)),
						Speed = SqlFunc.AggregateSum(SqlFunc.IIF(u.Name != "EA", pa.Quantity * sp.Psmng, pa.Quantity)) / sp.OeeSpeed,
					})
					.ToListAsync();

				var lineDownTime = performances
				.GroupBy(p => p.LineId)
				.ToDictionary(g => g.Key, g => g.GroupBy(x => x.Categroy).ToDictionary(g => g.Key, g => g.Sum(x => (long)x.TimeDifferenceInSeconds) / 3600m));

				var lineSpeed = orderSpeed.GroupBy(p => p.LineCode)
				.ToDictionary(g => g.Key, g => new { UnGoodQuantity = g.Sum(x => x.UnGoodQuantity), Quantity = g.Sum(x => x.Quantity), Speed = g.Sum(x => x.Speed), });
                List<EquipmentEntity> GzEquipmentidLIST = await _EquipmentEntity.FindList(p => reqModel.LineIds.Contains(p.EquipmentCode));
                for (int i = 0; i < reqModel.LineIds.Length; i++)
				{
                    #region 获取标准OEE值

                    string lineID = string.Empty;
                    decimal StandardSpeed = 0m;
                    //当前产线下灌装机
                    var LIST = GzEquipmentidLIST.Where(P => P.EquipmentCode == reqModel.LineIds[i]).FirstOrDefault();
                    if (LIST != null)
                    {
                        lineID = LIST.ID;
                    }
                    SerilogServer.LogDebug($"包装看板s GzEquipmentid:", "包装记录LOG ");
                    var GzEquipmentid = await _EquipmentEntity.FindList(p => p.LineId == lineID && p.Enabled == 1 && p.Deleted == 0 && p.EquipmentName.Contains("灌装机") && p.Level == "Unit");
                    SerilogServer.LogDebug($"包装看板e GzEquipmentid:", "包装记录LOG ");
                    //找灌装机执行工单
                    if (GzEquipmentid != null || GzEquipmentid != null)
                    {
                        SerilogServer.LogDebug($"包装看板开始 first:", "包装记录LOG ");
                        //找灌装机正在执行的工单
                        var first = GzEquipmentid.FirstOrDefault();
                        var proid = await _PoProducedExecutionEntity.FindEntity(p => p.RunEquipmentId == first.ID && p.Status == "1");
                        SerilogServer.LogDebug($"包装看板结束 first:", "包装记录LOG ");
                        if (proid != null)
                        {
                            //找到工单号
                            var proOrder = await _ProductionOrderEntity.FindEntity(p => p.ID == proid.ProductionOrderId);
                            if (proOrder != null)
                            {
                                var OEE_speed = await _SappackorderEntity.FindEntity(p => p.Aufnr == proOrder.ProductionOrderNo);
                                if (OEE_speed != null)
                                {
                                    StandardSpeed = Convert.ToDecimal(OEE_speed.OeeSpeed);

                                }
                            }
                        }

                    }


                    #endregion

                    #region 关键设备指示灯
                    //获取某一条产线灌装机、贴标机、装箱机、码垛机
                    var datas = data.Where(p => p.lineCode == reqModel.LineIds[i]).ToList();
					var eq = await _EquipmentEntity.FindEntity(p => p.EquipmentCode == reqModel.LineIds[i]);
					EquipmentStatusModel model = new EquipmentStatusModel();
					if (eq != null)
					{
						model.LineName = eq.EquipmentName;
					}
					else
					{
						model.LineName = "";
					}
					lineSpeed.TryGetValue(reqModel.LineIds[i], out var v);
					lineDownTime.TryGetValue(eq.ID, out var dic1);
					decimal runTime = 0;
					decimal s = 0;
					if (dic1 != null)
					{
						dic1.TryGetValue("运行中", out runTime);
						s = dic1.Sum(x => x.Value);
					}
					//dic1.TryGetValue("计划停机", out var planStopTime);
					//dic1.TryGetValue("非计划停机", out var unPlanStopTime);
					//表现性
					var v1 = runTime == 0 ? 0 : Math.Round(v?.Speed ?? 0 / runTime, 4);
					//有效性
					var v2 = s == 0 ? 0 : runTime / s;
					//品质性
					var v3 = ((v == null || v.Quantity == null || v.Quantity == 0) ? 0 : (v.Quantity ?? 0 - v.UnGoodQuantity ?? 0) / v.Quantity) ?? 0;
					//OEE
					var v4 = Math.Round(v1 * v2 * v3 * 100, 2);


					List<L2Models> l2Models = new List<L2Models>();
					L2Models l2Model = new L2Models();
					if (datas.Count > 0)
					{
						var ids = datas.Select(p => p.ID).ToList();
						var performanceEntities = await (_PerformanceEntity.Db.Queryable<PerformanceEntity>()
					   .Where(x => ids.Contains(x.EquipmentId) && x.EndTimeUtc == null)
					   .OrderByDescending(x => x.StartTimeUtc)).ToListAsync();
						foreach (var item in datas)
						{
							L2Models l2 = new L2Models();
							var performanceEntity = performanceEntities.FirstOrDefault(x => x.EquipmentId == item.ID);
							string Status = "无排产";
							if (performanceEntity != null)
							{
								switch (performanceEntity.Categroy)
								{

									case "生产运行":
										Status = "运行中";
										break;
									case "计划停机":
										Status = "停机";
										break;
									case "非计划停机":
										Status = "故障";
										break;
									default:
										break;
								}
							}
							l2.EquipmentStatus = Status;
							if (item.EquipmentName.Contains("#"))
							{
								//获取产线名截取#号后数据
								l2.EquipmentName = item.EquipmentName.Substring(0, item.EquipmentName.IndexOf('#'));
							}
							else
							{
								l2.EquipmentName = item.EquipmentName;
							}
							l2Models.Add(l2);
						}
					}

					#region 设备数量不足时补充空数据
					var state = "无排产";
					if (datas.Count == 3)
					{
						l2Model.EquipmentStatus = state;
						l2Model.EquipmentName = "";
						l2Models.Add(l2Model);
					}
					if (datas.Count == 2)
					{
						l2Model.EquipmentStatus = state;
						l2Model.EquipmentName = "";
						l2Models.Add(l2Model);
						l2Model.EquipmentStatus = state;
						l2Model.EquipmentName = "";
						l2Models.Add(l2Model);
					}
					if (datas.Count == 1)
					{
						l2Model.EquipmentStatus = state;
						l2Model.EquipmentName = "";
						l2Models.Add(l2Model);
						l2Model.EquipmentStatus = state;
						l2Model.EquipmentName = "";
						l2Models.Add(l2Model);
						l2Model.EquipmentStatus = state;
						l2Model.EquipmentName = "";
						l2Models.Add(l2Model);
					}
					if (datas.Count == 0)
					{
						l2Model.EquipmentStatus = state;
						l2Model.EquipmentName = "";
						l2Models.Add(l2Model);
						l2Model.EquipmentStatus = state;
						l2Model.EquipmentName = "";
						l2Models.Add(l2Model);
						l2Model.EquipmentStatus = state;
						l2Model.EquipmentName = "";
						l2Models.Add(l2Model);
						l2Model.EquipmentStatus = state;
						l2Model.EquipmentName = "";
						l2Models.Add(l2Model);
					}
					#endregion
					#endregion

					#region JIT物料状态指示灯
					List<PackJitMaterial> packJits1 = new List<PackJitMaterial>();
					PackJitMaterial packJitMateria2 = new PackJitMaterial();
					var data1 = await _PackJitViewEntity.FindList(p => p.EquipmentCode == reqModel.LineIds[i]);
					for (int j = 0; j < data.Count; j++)
					{
						PackJitMaterial packJitMaterial = new PackJitMaterial();
						if (j >= 7)
						{
							break;
						}
						List<InventoryControlEntity> list = await _InventoryControlEntity.FindList(p => p.MaterialId != null);
						var mmodel = list.Where(p => p.MaterialId == data1[i].MaterialId).FirstOrDefault();
						int minquy = 0;//安全水位（h）
						int maxquy = 0;//最高水位（h）

						if (mmodel != null)
						{
							minquy = mmodel.MinQuantity;
							maxquy = mmodel.MaxQuantity;
						}
						DateTime stars = DateTime.Now;
						DateTime highends = stars.AddHours(maxquy);
						DateTime minends = stars.AddHours(minquy);

						//最高水位
						decimal hight_Water = Convert.ToDecimal(data1.Where(p => p.CreateDate >= stars && p.CreateDate <= highends).Sum(p => p.NeedQuantity));
						//最低水位
						decimal min_Water = Convert.ToDecimal(data1.Where(p => p.CreateDate >= stars && p.CreateDate <= minends).Sum(p => p.NeedQuantity));


						if (data1.Count == 0)
						{
							packJitMaterial.MaterialName = "";
							packJitMaterial.state = "";
						}
						else
						{
							packJitMaterial.MaterialName = data1[i].MaterialName;
							if (data1[i].Inventqty > hight_Water)
							{
								packJitMaterial.state = "green";
							}
							else if (data1[i].Inventqty < min_Water)
							{
								packJitMaterial.state = "red";
							}
							else if (data1[i].Inventqty < hight_Water && data1[i].Inventqty > min_Water)
							{
								packJitMaterial.state = "yellow";
							}
						}


						packJits1.Add(packJitMaterial);
					}
					#region JIT物料组不足时补充到数组
					if (packJits1.Count == 7)
					{
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
					}
					else if (packJits1.Count == 6)
					{
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
					}
					else if (packJits1.Count == 5)
					{
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
					}
					else if (packJits1.Count == 4)
					{
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
					}
					else if (packJits1.Count == 3)
					{
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
					}
					else if (packJits1.Count == 2)
					{
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
					}
					else if (packJits1.Count == 1)
					{
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
					}
					else if (packJits1.Count == 0)
					{
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
						packJitMateria2.MaterialName = "";
						packJitMateria2.state = "";
						packJits1.Add(packJitMateria2);
					}
					#endregion


					#endregion

					#region 灌装机速度/当天灌装工单
					var day = 0;
					var BoilingEndTime = MinutesToHoursMinutes(Convert.ToInt32(0));//转换成时间格式
					LastOverTimeModel lastOverTimeModel = new LastOverTimeModel();
					var models = await _PackLastOverTimeViewEntity.FindList(p => p.EquipmentCode == reqModel.LineIds[i]);
					if (models.Count > 0)
					{

						//取当天产线最后一条数据的计划结束时间
						var Lastmodel = models.OrderByDescending(p => p.PlanStartTime).FirstOrDefault();

						//把事件的开始时间和结束时间转换成分钟
						double minutes1 = 0;
						double minutes2 = 0;
						if (Lastmodel.PlanEndTime != null)
						{
							TimeSpan stateOfDay = TimeSpan.Parse(Lastmodel.PlanEndTime.Value.ToString("HH:mm"));
							minutes1 = stateOfDay.TotalMinutes;//这条产线最后一条工单的计划结束时间
						}
						//取当天产线最后一条数据的计划结束时间
						//var models2 = models.Where(p => p.EndTime != null).ToList();
						//if (models2.Count > 0)
						//{
						//	var model1 = models2.OrderBy(p => p.EndTime).LastOrDefault();
						//	if (model1.EndTime != null)
						//	{
						//		TimeSpan endOfDay = TimeSpan.Parse(model1.EndTime.Value.ToString("HH:mm"));
						//		minutes2 = endOfDay.TotalMinutes;//工单执行表中最后一条的结束时间
						//	}
						//}
						var models2 = await _PackCurrentViewEntity.FindList(p => p.LineCode == reqModel.LineIds[i]);
						if (models2 != null || models2.Count > 0)
						{
							foreach (var item in models2)
							{
								#region 计算预计结束时间
								//当前时间转换成分钟
								TimeSpan stateOfDay = TimeSpan.Parse(DateTime.Now.ToString("HH:mm"));
								decimal minutes_1 = Convert.ToDecimal(stateOfDay.TotalMinutes);
								decimal minutes1_1 = 0;
								//工单计划完成时间转换成分钟
								if (!string.IsNullOrEmpty(item.PlanEndTime.ToString()))
								{
									TimeSpan stateOfDay1 = TimeSpan.Parse(item.PlanEndTime.Value.ToString("HH:mm"));
									minutes1_1 = Convert.ToDecimal(stateOfDay1.TotalMinutes);
								}

								//灌包装速度--暂时拿不到先赋默认值后续再进行修改
								decimal speed = 0m;
								var opc = await _InfluxOpcTagEntity.FindEntity(p => p.EquipmentId == item.ID && p.Name.Contains("灌装速度"));
								if (opc != null)
								{
									try
									{
										//获取点位状态
										speed = (int)Convert.ToDecimal((await _IInfluxDbServices.GetLastInfluxData(DateTime.Now.AddMinutes(-10), DateTime.Now, null, opc.Tag))?.Value);
									}
									catch (Exception)
									{
										speed = 0;

									}
								}

								//20250219预计时间=当前时间+（工单计划总产量-目前已报产量）/  当前灌装速度
								if (speed > 0)
								{
									minutes2 = (double)(minutes_1 + (item.PlanQty - item.ActualQuantity) / speed);
								}
								SerilogServer.LogDebug($"预计结束时间=当前时间+（工单计划总产量-目前已报产量）/  当前灌装速度：{minutes2}" + $" 计划产量：{item.PlanQty}" + $"实际产出：{item.ActualQuantity}" + $"当前灌装速度：{speed}", "当天灌装计划结束时间预计结束时间log");

							}
						}


						day = 0;
						//产线最后一条工单的结束时间-工单执行表中最后一条的结束时间
						double minutes = minutes1 + (minutes2 - minutes1);
						//判断大于00:00
						if (minutes > 1440)
						{
							//计算时间大于今天的天数 
							day = Convert.ToInt32(minutes) / 1440;
							//时间=总时间-整天数
							minutes = minutes - (day * 1440);
						}
						BoilingEndTime = MinutesToHoursMinutes(Convert.ToInt32(minutes));//转换成时间格式
					}

					#endregion

					#region 组合生产效率产能

					//关键设备指示灯
					model.EquipmentName1 = l2Models[0].EquipmentName;
					model.EquipmentStatus1 = l2Models[0].EquipmentStatus;
					model.EquipmentName2 = l2Models[1].EquipmentName;
					model.EquipmentStatus2 = l2Models[1].EquipmentStatus;
					model.EquipmentName3 = l2Models[2].EquipmentName;
					model.EquipmentStatus3 = l2Models[2].EquipmentStatus;
					model.EquipmentName4 = l2Models[3].EquipmentName;
					model.EquipmentStatus4 = l2Models[3].EquipmentStatus;

					//JIT物料状态指示灯
					model.m1 = packJits1[0].MaterialName;
					model.s1 = packJits1[0].state;
					model.m2 = packJits1[1].MaterialName;
					model.s2 = packJits1[1].state;
					model.m3 = packJits1[2].MaterialName;
					model.s3 = packJits1[2].state;
					model.m4 = packJits1[3].MaterialName;
					model.s4 = packJits1[3].state;
					model.m5 = packJits1[4].MaterialName;
					model.s5 = packJits1[4].state;
					model.m6 = packJits1[5].MaterialName;
					model.s6 = packJits1[5].state;
					model.m7 = packJits1[6].MaterialName;
					model.s7 = packJits1[6].state;
					model.m8 = packJits1[7].MaterialName;
					model.s8 = packJits1[7].state;
					DateTime todayStart = DateTime.Today; // 当天的00:00:00
					DateTime todayEnd = todayStart.AddDays(1).AddSeconds(-1); // 当天的23:59:59
																			  //var oeeModel = await _OeeReportViewEntity.FindList(p => p.CreateDate >= todayStart && p.CreateDate <= todayEnd);
					#region OEE 计算
					//DateTime now = DateTime.Now;

					////有效性
					//#region 有效性计算
					//var eff = 0.00m;//有效性
					//var downtimeList = await _dal.Db.Queryable<DowntimeEntity, DowntimeReasonEntity, DowntimeGroupEntity, EquipmentEntity, DowntimeCategroyEntity, ProductionOrderEntity, PoProducedExecutionEntity, EquipmentEntity>(
					//	(Downtime, Reason, g, eqs, Categroy, pro, exe, eq2) => new object[]
					//	{
					//JoinType.Left , Downtime.ReasonId == Reason.ID,
					//JoinType.Left , Reason.GroupId==g.ID,
					// JoinType.Left,  Downtime.EquipmentId==eqs.ID,
					//JoinType.Left,  g.CategoryId==Categroy.ID,
					//JoinType.Left,  Downtime.OrderId==pro.ID,
					//JoinType.Left,  Downtime.PoExecutionId==exe.ID,
					//JoinType.Left,  eq2.ID == eqs.LineId
					//	})
					//	.Where((Downtime, Reason, g, eqs, Categroy, pro, exe, eq2) => eqs.EquipmentName.Contains("灌装机")
					//					 && eq2.EquipmentCode == reqModel.LineIds[i]
					//					 //&& Downtime.StartTimeUtc >= Convert.ToDateTime("2024-10-01 00:00:00")
					//					 //&& Downtime.EndTimeUtc <= Convert.ToDateTime("2024-12-31 23:59:59")
					//					 && Downtime.EndTimeUtc != null
					//					 && Downtime.StartTimeUtc >= DateTime.Today
					//					 && Downtime.EndTimeUtc <= DateTime.Today.AddDays(1).AddTicks(-1)
					//					 )
					//	.Select((Downtime, Reason, g, eqs, Categroy, pro, exe, eq2) => new
					//	{
					//		eq2.EquipmentCode,
					//		Categroy.Description,
					//		EndTimeUtc = Downtime.EndTimeUtc ?? now,
					//		Downtime.StartTimeUtc
					//	})
					//	.ToListAsync();
					//var results = downtimeList.Select(d =>
					//{
					//	TimeSpan duration = (TimeSpan)(d.EndTimeUtc - d.StartTimeUtc);
					//	return new
					//	{
					//		d.EquipmentCode,
					//		d.Description,
					//		d.StartTimeUtc,
					//		d.EndTimeUtc,
					//		InHours = (decimal)duration.TotalHours
					//	};
					//}).ToList();
					//decimal total = 0.00m;
					//if (results.Count > 0)
					//{

					//	var timeList = results.GroupBy(p => new { p.Description }).Select(x => new { x.Key.Description, InHours = x.Sum(p => p.InHours) }).ToList();
					//	var b = timeList.Where(p => p.Description == "计划停机").Select(p => p.InHours);
					//	var UnplannedTime = timeList.Where(p => p.Description == "计划停机").Select(p => p.InHours).FirstOrDefault();
					//	var PlannedTime = timeList.Where(p => p.Description == "非计划停机").Select(p => p.InHours).FirstOrDefault();
					//	var runTime = timeList.Where(p => p.Description == "生产运行").Select(p => p.InHours).FirstOrDefault();
					//	total = UnplannedTime + PlannedTime + runTime;
					//	if (total > 0)
					//	{
					//		eff = runTime / total;

					//	}
					//}
					//#endregion

					////表现性
					//#region 表现性计算
					//decimal Expressive = 0.00m;//表现性
					//var productionList = await _dal.Db.Queryable<PoProducedActualEntity, ProductionOrderEntity, EquipmentEntity, EquipmentEntity, SappackorderEntity>(
					//   (proa, pro, eqs, eq2, sap) => new object[]
					//   {
					//	JoinType.Left , proa.ProductionOrderId == pro.ID,
					//	JoinType.Left , proa.EquipmentId==eqs.ID,
					//	JoinType.Left,  eqs.LineId==eq2.ID,
					//	JoinType.Left,  pro.ProductionOrderNo==sap.Aufnr,

					//})
					//   .Where((proa, pro, eqs, eq2) => eqs.EquipmentName.Contains("灌装机")
					//					&& eq2.EquipmentCode == reqModel.LineIds[i]
					//					//&& proa.CreateDate >= Convert.ToDateTime("2024-10-01 00:00:00")
					//					//&& proa.CreateDate <= Convert.ToDateTime("2024-12-31 23:59:59")
					//					&& proa.CreateDate >= DateTime.Today
					//					&& proa.CreateDate <= DateTime.Today.AddDays(1).AddSeconds(-1)
					//					)
					//   .Select((proa, pro, eqs, eq2, sap) => new
					//   {
					//	   pro.ProductionOrderNo,
					//	   proa.ProductionOrderId,
					//	   proa.Quantity,
					//	   OeeSpeed = sap.OeeSpeed == null ? 0 : sap.OeeSpeed
					//   })
					//   .ToListAsync();


					//if (productionList.Count > 0)
					//{
					//	//分组工单汇总产量
					//	var groupOrder = productionList.GroupBy(p => new { p.ProductionOrderId, p.ProductionOrderNo }).Select(s => new { s.Key.ProductionOrderNo, Quantity = s.Sum(p => p.Quantity), oeespeed = s.Sum(p => p.OeeSpeed), }).ToList();
					//	var proStandardTime = 0.00m;

					//	if (groupOrder.Count > 0)
					//	{
					//		foreach (var item in groupOrder)
					//		{
					//			if (item.oeespeed > 0)
					//			{
					//				decimal sumTime = Convert.ToDecimal(item.Quantity / item.oeespeed);
					//				proStandardTime += sumTime;

					//			}

					//		}
					//		if (total > 0)
					//		{
					//			Expressive = proStandardTime / total;
					//		}

					//	}
					//}
					//#endregion
					#endregion
					//品质性  后续确认不良品怎么取再补充
					var Quality = 1;

					//OEE右小角
					decimal OEEChain = 0;
					decimal ExpressiveChain = 0;
					//var oeeActual = Math.Round(Quality * eff * Expressive, 0);
					var oeeActual = v4;
					var OeeTage = await _KpitgtEntity.FindEntity(p => p.Year == DateTime.Today.Year && p.Month == DateTime.Today.Month && p.DataName.Contains("OEE") && p.DateType == "预算" && p.ModelRef == eq.ID);
					if (OeeTage != null)
					{
						if (oeeActual != 0)
						{
							OEEChain = Math.Round((oeeActual - OeeTage.Tgt) * 100 / oeeActual, 0);
						}
					}

					var ExpTage = await _KpitgtEntity.FindEntity(p => p.Year == DateTime.Today.Year && p.Month == DateTime.Today.Month && p.DataName.Contains("表现性") && p.DateType == "预算" && p.ModelRef == eq.ID);
					if (ExpTage != null)
					{
						if (v4 != 0)
						{
							ExpressiveChain = Math.Round((v4 - ExpTage.Tgt) * 100 / v4, 0);
						}


					}
					//灌装速度
					var RealtSpeed = 0m;

					var equipment = await _EquipmentEntity.Db.Queryable<EquipmentEntity>().Where(p => p.LineId == eq.ID && p.Deleted == 0 && p.EquipmentName.Contains("灌装机") && p.Enabled == 1).FirstAsync();
					// var equipment = await _dal.q
					//FindEntity(p => p.LineId == lineid.ID && p.Deleted == 0 && p.EquipmentName.Contains("灌装机") && p.Enabled==1);
					if (equipment != null)
					{
						//查找设备状态点位
						var opcTag = await _InfluxOpcTagEntity.FindEntity(p => p.EquipmentId == equipment.ID && p.Name == "灌装速度");
						//获取点位状态
						if (opcTag != null)
						{
							try
							{
								RealtSpeed = (int)Convert.ToDecimal((await _IInfluxDbServices.GetLastInfluxData(DateTime.Now.AddMinutes(-10), DateTime.Now, null, opcTag.Tag))?.Value);

							}
							catch (Exception)
							{
								RealtSpeed = 0;

							}
						}
					}

					//OEE和表现性、灌装机速度、当天灌装工单last over time  (int)(time1 - time2).TotalHours
					//model.OEE = Math.Round(Quality * eff * Expressive, 0);
					model.OEE = v4;
					model.OEEChain = OEEChain;//OEE右下角
											  //model.Expressive = Math.Round(Expressive * 100, 0);
					model.Expressive = Math.Round(v1 * 100, 2);
					model.ExpressiveChain = ExpressiveChain; //表现性右下角
					model.RealtSpeed = RealtSpeed;//实时速度
					model.StandardSpeed = StandardSpeed;//标准速度取PACKorder的OEE速度
					model.LastOverTime = BoilingEndTime;
					model.day = day;
					statusModels.Add(model);
					#endregion

					#endregion
				}
				statusModels.OrderBy(p => p.LineName);
				result.response = statusModels;
				result.success = true;
				result.msg = "获取成功";
				return result;
			}
			catch (Exception ex)
			{
				result.msg = ex.StackTrace;
				return result;

				throw;
			}


		}

	}
}