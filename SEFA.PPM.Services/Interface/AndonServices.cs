
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.Base.Common.HttpContextUser;
using System;
using SEFA.Base.ESB;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Services.BASE;
using SEFA.MKM.Model.Models;
using MaterialGroupEntity = SEFA.DFM.Model.Models.MaterialGroupEntity;
using SapSegmentEntity = SEFA.DFM.Model.Models.SapSegmentEntity;
using MaterialGroupMappingEntity = SEFA.DFM.Model.Models.MaterialGroupMappingEntity;
using FunctionPropertyEntity = SEFA.DFM.Model.Models.FunctionPropertyEntity;
using FunctionPropertyValueEntity = SEFA.DFM.Model.Models.FunctionPropertyValueEntity;
using EquipmentEntity = SEFA.DFM.Model.Models.EquipmentEntity;
using LabelPrintHistoryEntity = SEFA.DFM.Model.Models.LabelPrintHistoryEntity;
using EquipmentRequirementEntity = SEFA.DFM.Model.Models.EquipmentRequirementEntity;
using SapSegmentMaterialEntity = SEFA.DFM.Model.Models.SapSegmentMaterialEntity;
using SapSegmentMaterialStepEntity = SEFA.DFM.Model.Models.SapSegmentMaterialStepEntity;
using DataItemEntity = SEFA.DFM.Model.Models.DataItemEntity;
using MaterialVersionEntity = SEFA.DFM.Model.Models.MaterialVersionEntity;
using MaterialEntity = SEFA.DFM.Model.Models.MaterialEntity;
using ParameterGroupEntity = SEFA.DFM.Model.Models.ParameterGroupEntity;
using FunctionEntity = SEFA.DFM.Model.Models.FunctionEntity;
using System.Linq;
using SqlSugar;
using SEFA.MKM.IServices;
using SEFA.PPM.Model.Models.SIM;
using SEFA.Base.InfluxDb;
using MongoDB.Driver;
using SEFA.PPM.Model.Models.PTM;
using System.Data;
using SEFA.PTM.Model.Models;
using AutoMapper;
using SEFA.PPM.IServices.PTM;
using SEFA.Base.Extensions;
using System.Text;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.Common.Common;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using SEFA.DFM.Model.Models.Models.ViewModel;
using System.Xml;
using System.Dynamic;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Common.HttpRestSharp;
using static SEFA.PTM.Services.ConsumeViewServices;
using MongoDB.Bson;
using System.Net.NetworkInformation;

namespace SEFA.PPM.Services
{
	public class AndonServices : BaseServices<UnitConvertEntity>, IAndonServices
	{
		private readonly IBaseRepository<UnitConvertEntity> _dal;
		private readonly IBaseRepository<MaterialEntity> _dal2;
		private readonly IBaseRepository<MaterialGroupEntity> _dal4;
		private readonly IBaseRepository<ProductionOrderEntity> _dal5;
		private readonly IBaseRepository<BatchEntity> _dal6;
		private readonly IBaseRepository<PoSegmentRequirementEntity> _dal7;
		private readonly IBaseRepository<SapSegmentEntity> _dal8;
		private readonly IBaseRepository<SappackorderEntity> _dal9;
		private readonly IBaseRepository<ProductionOrderPropertyEntity> _dal10;
		private readonly IBaseRepository<PoConsumeMaterialListViewEntity> _dal11;
		private readonly IBaseRepository<FunctionPropertyEntity> _dal12;
		private readonly IBaseRepository<FunctionPropertyValueEntity> _dal13;
		private readonly IBaseRepository<PoProducedExecutionEntity> _dal14;
		private readonly IBaseRepository<EquipmentEntity> _dal15;
		private readonly IBaseRepository<MaterialSubLotEntity> _dal16;
		private readonly IBaseRepository<PoProducedActualEntity> _dal17;
		private readonly IBaseRepository<LabelPrintHistoryEntity> _dal18;
		private readonly IBaseRepository<MaterialInventoryEntity> _dal19;
		private readonly IBaseRepository<ProduceLocationViewEntity> _dal20;
		private readonly IBaseRepository<InventorylistingViewEntity> _dal21;
		private readonly IBaseRepository<EnergyBydayEntity> _dal22;
		private readonly IBaseRepository<EnergyByorderEntity> _dal23;
		private readonly IBaseRepository<SapSegmentMaterialEntity> _dal24;
		private readonly IBaseRepository<SapSegmentMaterialStepEntity> _dal25;
		private readonly IBaseRepository<PoConsumeRequirementEntity> _dal26;
		private readonly IBaseRepository<PoProducedRequirementEntity> _dal27;
		private readonly IBaseRepository<PoConsumeActualEntity> _dal28;
		private readonly IBaseRepository<ActionPropertyEntity> _dal29;
		private readonly IBaseRepository<BatchComsumeMaterialListViewEntity> _dal30;
		private readonly IBaseRepository<EquipmentStorageEntity> _dal31;
		private readonly IBaseRepository<EquipmentRequirementEntity> _dal32;
		private readonly IBaseRepository<DataItemEntity> _dal33;
		private readonly IBaseRepository<SEFA.DFM.Model.Models.DataItemDetailEntity> _dal34;
		private readonly IBaseRepository<MaterialVersionEntity> _dal35;
		private readonly IBaseRepository<PoExecutionHistroyViewEntity> _dal36;
		private readonly IBaseRepository<ProcessOrderViewEntity> _dal37;
		private readonly IBaseRepository<MaterialLotEntity> _dal38;
		private readonly IBaseRepository<ParaExecutionLogEntity> _paraExecutionLog;
		private readonly IBaseRepository<ParameterGroupEntity> _parameterGroupdal;
		private readonly IBaseRepository<LogsheetEntity> _logsheetdal;
		private readonly IBaseRepository<LogsheetDetailEntity> _logsheetDetaildal;
		private readonly IBaseRepository<MaterialGroupMappingEntity> _materMappingdal;
		private readonly IBaseRepository<MMaterialPropertyViewEntity> _materPropertydal;
		private readonly IUnitOfWork _unitOfWork;
		private readonly IUser _user;
		private readonly LKKESBHelper _esbHelper;
		private readonly InfluxDbHelper _influxDbHelper;
		private readonly IInfluxDbServices _IInfluxDbServices;
		private readonly IMapper _mapper;
		private readonly IPerformanceServices _performanceServices;
		private readonly IBaseRepository<FunctionEntity> _dalfunction;
		private readonly IBaseRepository<SEFA.PPM.Model.Models.PTM.FunctionPropertyVEntity> _funpdal;
		private readonly IRedisBasketRepository _redisBasketRepository;
		private readonly IBaseRepository<BWeightRecordsEntity> _bwrdal;
		private readonly IBaseRepository<WorkingHourEntity> _whdal;
		private readonly IBaseRepository<CipinfoEntity> _cipInfoDal;
		private readonly IBaseRepository<SampleListVEntity> _sampvDal;
		private readonly IBaseRepository<BProductionOrderListViewEntity> _bproDal;
		private readonly IBaseRepository<InventoryControlEntity> _inventorycotrDal;


		public AndonServices(
			IBaseRepository<UnitConvertEntity> dal,
			IBaseRepository<MaterialEntity> dal2,
			IBaseRepository<MaterialGroupEntity> dal4,
			IBaseRepository<ProductionOrderEntity> dal5,
			IBaseRepository<BatchEntity> dal6,
			IBaseRepository<PoSegmentRequirementEntity> dal7,
			IBaseRepository<SapSegmentEntity> dal8,
			IBaseRepository<SappackorderEntity> dal9,
			IBaseRepository<ProductionOrderPropertyEntity> dal10,
			IBaseRepository<PoConsumeMaterialListViewEntity> dal11,
			IBaseRepository<FunctionPropertyEntity> dal12,
			IBaseRepository<FunctionPropertyValueEntity> dal13,
			IBaseRepository<PoProducedExecutionEntity> dal14,
			IBaseRepository<EquipmentEntity> dal15,
			IUnitOfWork unitOfWork,
			IUser user,
			LKKESBHelper esbHelper,
			IBaseRepository<MaterialSubLotEntity> dal16,
			IBaseRepository<PoProducedActualEntity> dal17,
			IBaseRepository<LabelPrintHistoryEntity> dal18,
			IBaseRepository<MaterialInventoryEntity> dal19,
			IBaseRepository<ProduceLocationViewEntity> dal20,
			IBaseRepository<InventorylistingViewEntity> dal21,
			IContainerServices containerServices,
			IBaseRepository<EnergyByorderEntity> dal23,
			IBaseRepository<EnergyBydayEntity> dal22,
			IBaseRepository<SapSegmentMaterialEntity> dal24,
			IBaseRepository<SapSegmentMaterialStepEntity> dal25,
			IBaseRepository<PoConsumeRequirementEntity> dal26,
			IBaseRepository<PoProducedRequirementEntity> dal27,
			IBaseRepository<PoConsumeActualEntity> dal28,
			InfluxDbHelper influxDbHelper,
			IBaseRepository<ActionPropertyEntity> dal29,
			IBaseRepository<EquipmentStorageEntity> dal31,
			IBaseRepository<EquipmentRequirementEntity> dal32,
			IBaseRepository<DataItemEntity> dal33,
			IBaseRepository<SEFA.DFM.Model.Models.DataItemDetailEntity> dal34,
			IBaseRepository<MaterialVersionEntity> dal35,
			IBaseRepository<PoExecutionHistroyViewEntity> dal36,
			IBaseRepository<BatchComsumeMaterialListViewEntity> dal30,
			IInfluxDbServices iInfluxDbServices,
			IBaseRepository<ProcessOrderViewEntity> dal37,
			IBaseRepository<MaterialLotEntity> dal38,
			IBaseRepository<ParaExecutionLogEntity> paraExecutionLog,
			IMapper mapper,
			IBaseRepository<ParameterGroupEntity> parameterGroupdal,
			IBaseRepository<LogsheetDetailEntity> logsheetDetaildal,
			IBaseRepository<MaterialGroupMappingEntity> materMAappingdal,
			IPerformanceServices performanceServices,
			IRequestInventoryViewServices requestInventoryViewServices,
			IBaseRepository<FunctionEntity> dalfunction,
			IBaseRepository<MMaterialPropertyViewEntity> materPropertydal,
			IBaseRepository<Model.Models.PTM.FunctionPropertyVEntity> funpdal,
			ICookConfirmationServices cookConfirmationServices,
			IBaseRepository<LogsheetEntity> logsheetdal,
			IRedisBasketRepository redisBasketRepository,
			IBaseRepository<BWeightRecordsEntity> bwrdal,
			IBaseRepository<WorkingHourEntity> whdal,
			IBaseRepository<CipinfoEntity> cipInfoDal,
			IBaseRepository<SampleListVEntity> sampvDal,
			IBaseRepository<BProductionOrderListViewEntity> bproDal,
			IBaseRepository<InventoryControlEntity> inventorycotrDal)
		{
			this._dal = dal;
			this._dal2 = dal2;
			this._dal4 = dal4;
			this._dal5 = dal5;
			this._dal6 = dal6;
			this._dal7 = dal7;
			this._dal8 = dal8;
			this._dal9 = dal9;
			this._dal10 = dal10;
			this._dal11 = dal11;
			this._dal12 = dal12;
			this._dal13 = dal13;
			this._dal14 = dal14;
			this._dal15 = dal15;
			base.BaseDal = dal;
			this._unitOfWork = unitOfWork;
			this._user = user;
			_esbHelper = esbHelper;
			_dal16 = dal16;
			_dal17 = dal17;
			_dal18 = dal18;
			_dal19 = dal19;
			_dal20 = dal20;
			_dal21 = dal21;
			_dal23 = dal23;
			_dal22 = dal22;
			_dal24 = dal24;
			_dal25 = dal25;
			_dal26 = dal26;
			_dal27 = dal27;
			_dal28 = dal28;
			_influxDbHelper = influxDbHelper;
			_dal29 = dal29;
			_dal31 = dal31;
			_dal32 = dal32;
			_dal33 = dal33;
			_dal34 = dal34;
			_dal35 = dal35;
			_dal36 = dal36;
			_dal30 = dal30;
			_IInfluxDbServices = iInfluxDbServices;
			_dal37 = dal37;
			_dal38 = dal38;
			_paraExecutionLog = paraExecutionLog;
			_mapper = mapper;
			_parameterGroupdal = parameterGroupdal;
			_logsheetDetaildal = logsheetDetaildal;
			_materMappingdal = materMAappingdal;
			_dalfunction = dalfunction;
			_materPropertydal = materPropertydal;
			_funpdal = funpdal;
			_logsheetdal = logsheetdal;
			_redisBasketRepository = redisBasketRepository;
			_bwrdal = bwrdal;
			_whdal = whdal;
			_cipInfoDal = cipInfoDal;
			_bproDal = bproDal;
			_inventorycotrDal = inventorycotrDal;
		}

		#region Andon定时任务 count：5

		/// <summary>
		/// 原料库存超下限：查询库存低于制造工单【2】天的需求量的原料信息
		/// </summary>
		/// <param name="days">天数</param>
		/// <returns></returns>
		public async Task<MessageModel<string>> CheckRawMaterialInventory(int days)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			var startTime = DateTime.Now.Date;
			var endTime = DateTime.Now.Date.AddDays(days);
			//获取未来两天所有煮制工单
			var orders = await _dal5.FindList(x => x.PlanStartTime >= startTime && x.PlanStartTime < endTime && x.Type == "WorkOrder" && x.SapOrderType == "ZXH2" && !string.IsNullOrEmpty(x.ProductionOrderNo) && x.PoStatus != "4");
			var rawMaterials = await _dal2.FindList(x => x.Type == "ZRAW");
			if (orders?.Count > 0)
			{
				//找到原物料需求
				var poConsumeRequirements = await _dal26.FindList(x => orders.Any(x2 => x2.ID == x.ProductionOrderId) && (rawMaterials.Any(x3 => x3.ID == x.MaterialId) || x.StorageBin == "MFG3" || x.StorageBin == "SUR3"));
				//找到原料物料库存
				var inventorylistings = await _dal21.FindList(x => x.ClassCode == "ZRAW" || x.LocationS.Contains("MFG3") || x.LocationS.Contains("SUR3"));

				//使用左连接关联code及名称
				var data = from a in poConsumeRequirements
						   join b in inventorylistings on a.MaterialId equals b.MaterialId into abGroup
						   from b in abGroup.DefaultIfEmpty()
						   join c in rawMaterials on a.MaterialId equals c.ID into acGroup
						   from c in acGroup.DefaultIfEmpty()
						   select new
						   {
							   a.MaterialId,
							   MaterialCode = c?.Code ?? "No Code",
							   MaterialName = c?.NAME ?? "No Name",
							   NeedQuantity = a.Quantity,
							   ActualQuantity = b?.Quantity ?? 0
						   };

				//对最终结果进行分组并分别求和
				var groupedSum = data
					.GroupBy(item => item.MaterialId)
					.Select(group => new
					{
						MaterialId = group.Key,
						MaterialCode = group.First().MaterialCode,
						MaterialName = group.First().MaterialName,
						NeedQuantity = group.Sum(item => item.NeedQuantity),
						ActualQuantity = group.Sum(item => item.ActualQuantity)
					})
					.ToList();

				var codes = new List<string>();
				foreach (var item in groupedSum)
				{
					if (item.ActualQuantity < item.NeedQuantity)
					{
						codes.Add(item.MaterialCode);
					}
				}
				if (codes.Count > 0)
				{
					StringBuilder stringBuilder1 = new();
					stringBuilder1.AppendJoin('，', codes);
					var request = BuildAndonModel("INVENTORY", "INVENTORY02", "CookingArea", "Area", DateTime.Now,
						"以下物料【{0}】库存低于制造工单【{1}】天需求量",
						new object[] { stringBuilder1, days });
					return await ExecuteAndon(request);
				}
			}
			result.success = true;
			result.msg = "操作成功！未找到符合条件的数据";
			return result;
		}

		/// <summary>
		/// 包材库存超下限：查询库存低于包装工单【1】天的需求量的包材信息
		/// </summary>
		/// <param name="days">天数</param>
		/// <returns></returns>
		public async Task<MessageModel<string>> CheckPackagingInventory(int days)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			var startTime = DateTime.Now.Date;
			var endTime = DateTime.Now.Date.AddDays(days);
			//获取未来两天所有灌包装工单
			var orders = await _dal5.FindList(x => x.PlanStartTime >= startTime && x.PlanStartTime < endTime && x.Type == "WorkOrder" && x.SapOrderType != "ZXH2" && !string.IsNullOrEmpty(x.ProductionOrderNo) && x.PoStatus != "4");
			var pkgMaterials = await _dal2.FindList(x => x.Type == "ZPKG");
			if (orders?.Count > 0)
			{
				//找到包材物料需求
				var poConsumeRequirements = await _dal26.FindList(x => orders.Any(x2 => x2.ID == x.ProductionOrderId) && pkgMaterials.Any(x3 => x3.ID == x.MaterialId));
				//找到包材物料库存
				var inventorylistings = await _dal21.FindList(x => x.ClassCode == "ZPKG");
				//使用左连接关联code及名称
				var data = from a in poConsumeRequirements
						   join b in inventorylistings on a.MaterialId equals b.MaterialId into abGroup
						   from b in abGroup.DefaultIfEmpty()
						   join c in pkgMaterials on a.MaterialId equals c.ID into acGroup
						   from c in acGroup.DefaultIfEmpty()
						   select new
						   {
							   a.MaterialId,
							   MaterialCode = c?.Code ?? "No Code",
							   MaterialName = c?.NAME ?? "No Name",
							   NeedQuantity = a.Quantity,
							   ActualQuantity = b?.Quantity ?? 0
						   };

				//对最终结果进行分组并分别求和
				var groupedSum = data
					.GroupBy(item => item.MaterialId)
					.Select(group => new
					{
						MaterialId = group.Key,
						MaterialCode = group.First().MaterialCode,
						MaterialName = group.First().MaterialName,
						NeedQuantity = group.Sum(item => item.NeedQuantity),
						ActualQuantity = group.Sum(item => item.ActualQuantity)
					})
					.ToList();

				var codes = new List<string>();
				foreach (var item in groupedSum)
				{
					if (item.ActualQuantity < item.NeedQuantity)
					{
						codes.Add($"{item.MaterialName}({item.MaterialCode})");
					}
				}
				if (codes.Count > 0)
				{
					StringBuilder stringBuilder1 = new();
					stringBuilder1.AppendJoin('，', codes);
					var request = BuildAndonModel("INVENTORY", "INVENTORY03", "PackingArea", "Area", DateTime.Now,
						"以下包材【{0}】库存低于包装工单【{1}】天需求量",
						new object[] { stringBuilder1, days });
					return await ExecuteAndon(request);
				}
			}
			result.success = true;
			result.msg = "操作成功！未找到符合条件的数据";
			return result;
		}

		/// <summary>
		/// 工单执行异常：每天检查一次配方工单执行情况，查询未执行工单，或工单执行数量小于计划数量的工单
		/// </summary>
		/// <param name="areaCode"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> CheckOrderExecutionStatus(string areaCode)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			var startTime = DateTime.Now.Date;
			var endTime = DateTime.Now.Date.AddDays(1);
			//var startTime = DateTime.Now.Date.AddMonths(-8);
			//var endTime = DateTime.Now.Date;
			bool isCookie = areaCode == "CookingArea";
			var whereExpression = Expressionable.Create<ProductionOrderEntity>()
				.And(x => x.PlanStartTime >= startTime)
				.And(x => x.PlanStartTime < endTime)
				.And(x => x.Type == "WorkOrder")
				.And(x => !string.IsNullOrEmpty(x.ProductionOrderNo))
				.And(x => x.PoStatus != "4")
				.AndIF(isCookie == true, x => x.SapOrderType == "ZXH2")
				.AndIF(isCookie != true, x => x.SapOrderType != "ZXH2")
					.ToExpression();
			var todayOrders = await _dal5.FindList(whereExpression);
			if (todayOrders?.Count > 0)
			{
				StringBuilder stringBuilder1 = new();
				StringBuilder stringBuilder2 = new();
				var orderNos1 = new List<string>();
				var orderNos2 = new List<string>();
				var exOrderIds = (await _dal14.FindList(x => todayOrders.Any(x2 => x2.ID == x.ProductionOrderId)))?.Select(x => x.ProductionOrderId)?.Distinct() ?? new List<string>();
				var poProducedActuals = await _dal17.FindList(x => todayOrders.Any(x2 => x2.ID == x.ProductionOrderId));
				foreach (var order in todayOrders)
				{
					//工单未执行
					if (!exOrderIds.Contains(order.ID))
					{
						orderNos1.Add(order.ProductionOrderNo);
					}
					var sumProduceQuantity = poProducedActuals?.FindAll(x => x.ProductionOrderId == order.ID)?.Sum(x => x.Quantity) ?? 0;
					//工单已执行数量小于工单计划数量
					if (sumProduceQuantity < order.PlanQty)
					{
						orderNos2.Add(order.ProductionOrderNo);
					}
				}
				if (orderNos1.Count > 0 || orderNos2.Count > 0)
				{
					stringBuilder1.AppendJoin('，', orderNos1);
					stringBuilder2.AppendJoin('，', orderNos2);
					var request = BuildAndonModel("PRODUCTION", isCookie ? "PRODUCTION01" : "PRODUCTION09", areaCode, "Area", DateTime.Now,
						"以下工单【{0}】未正常执行，以下工单【{1}】执行数量小于计划数量",
						new object[] { stringBuilder1, stringBuilder2 });
					return await ExecuteAndon(request);
				}
			}
			result.success = true;
			result.msg = "操作成功！未找到符合条件的数据";
			return result;
		}

		/// <summary>
		/// 原物料时效性：每天检查物料时效性、查询距离失效日期[15]天的原料信息(所有不足15天的批次拼接成一个andon)
		/// </summary>
		/// <param name="days">天数</param>
		/// <returns></returns>
		public async Task<MessageModel<string>> CheckMaterialTimeliness(int days, string locations = null)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			var expirationDate = DateTime.Now.AddDays(15);
			string subAlarmType = "";
			if (string.IsNullOrEmpty(locations))
			{
				subAlarmType = "INVENTORY01";
				locations = "MFG3,SUR3";
			}
			else
			{
				subAlarmType = "INVENTORY08";
			}
			var locationList = locations.Split(',')?.ToList() ?? new List<string>();
			//找到有效期不满15天的物料库存
			var inventorylistings = await _dal21.FindList(x => locationList.Contains(x.LocationS) && x.ExpirationDate < expirationDate);
			//var materialSubLots = await _dal16.Query();
			var codes = new List<string>();
			if (inventorylistings?.Count > 0)
			{
				StringBuilder stringBuilder1 = new();
				foreach (var inventorylisting in inventorylistings)
				{
					if (!codes.Contains(inventorylisting.BatchId))
					{
						codes.Add(inventorylisting.BatchId);
					}
				}
				if (codes.Count > 0)
				{
					stringBuilder1.AppendJoin('，', codes);
					var request = BuildAndonModel("INVENTORY", subAlarmType, "Prep-Room", "Area", DateTime.Now,
						"以下物料批【{0}】，有效期不足【{1}】天",
						new object[] { stringBuilder1, days });
					return await ExecuteAndon(request);
				}
			}
			result.success = true;
			result.msg = "操作成功！未找到符合条件的数据";
			return result;
		}

		/// <summary>
		/// 物料不齐套：在包装开始前【6】小时检查包材齐套性，发送生产线、缺料明细信息给E-Andon系统
		/// </summary>
		/// <param name="hour">小时数</param>
		/// <returns></returns>
		public async Task<MessageModel<string>> CheckCompletenessOfPkg(int hour)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			var startTime = DateTime.Now;
			var endTime = DateTime.Now.AddHours(hour);
			//获取未来两天所有灌包装工单
			var orders = await _dal5.FindList(x => x.PlanStartTime >= startTime && x.PlanStartTime < endTime && x.Type == "WorkOrder" && x.SapOrderType != "ZXH2" && !string.IsNullOrEmpty(x.ProductionOrderNo) && x.PoStatus == "2");
			var pkgMaterials = await _dal2.FindList(x => x.Type == "ZPKG");
			if (orders?.Count > 0)
			{
				//找到包材物料需求
				var poConsumeRequirements = await _dal26.FindList(x => orders.Any(x2 => x2.ID == x.ProductionOrderId) && pkgMaterials.Any(x3 => x3.ID == x.MaterialId));
				//找到包材物料库存
				var inventorylistings = await _dal21.FindList(x => x.ClassCode == "ZPKG");
				//使用左连接关联code及名称
				var data = from a in poConsumeRequirements
						   join b in inventorylistings on a.MaterialId equals b.MaterialId into abGroup
						   from b in abGroup.DefaultIfEmpty()
						   join c in pkgMaterials on a.MaterialId equals c.ID into acGroup
						   from c in acGroup.DefaultIfEmpty()
						   select new
						   {
							   a.MaterialId,
							   MaterialCode = c?.Code ?? "No Code",
							   MaterialName = c?.NAME ?? "No Name",
							   NeedQuantity = a.Quantity,
							   ActualQuantity = b?.Quantity ?? 0
						   };

				//对最终结果进行分组并分别求和
				var groupedSum = data
					.GroupBy(item => item.MaterialId)
					.Select(group => new
					{
						MaterialId = group.Key,
						MaterialCode = group.First().MaterialCode,
						MaterialName = group.First().MaterialName,
						NeedQuantity = group.Sum(item => item.NeedQuantity),
						ActualQuantity = group.Sum(item => item.ActualQuantity)
					})
					.ToList();
				StringBuilder stringBuilder1 = new();
				foreach (var item in groupedSum)
				{
					if (item.ActualQuantity < item.NeedQuantity)
					{
						var requestItem = BuildAndonModel("QUALITY", "QUALITY06", "PackingArea", "Area", DateTime.Now,
							$"以下包材：【{item.MaterialCode}-{item.MaterialName}】未来【{hour}】小时工单需求量【{item.NeedQuantity}】，实际库存量【{item.ActualQuantity}】");
						await ExecuteAndon(requestItem);
						//stringBuilder1.AppendJoin('，', $"物料：【{item.MaterialCode}-{item.MaterialName}】需求量：【{item.NeedQuantity}】实际库存量：【{item.ActualQuantity}】");
					}
				}
				if (!string.IsNullOrEmpty(stringBuilder1.ToString()))
				{
					//var request = BuildAndonModel("QUALITY", "QUALITY06", "PackingArea", "Area", DateTime.Now,
					//	"未来【{0}】小时以下包材缺料：{1}",
					//	new object[] { hour,stringBuilder1 });
					//return await ExecuteAndon(request);
				}
			}
			result.success = true;
			result.msg = "操作成功！未找到符合条件的数据";
			return result;
		}

		#endregion

		#region Andon实时任务

		#region 需要先调用Andon接口 count：5

		#region 检验超时

		/// <summary>
		/// 发送logsheet表单Id给Andon
		/// </summary>
		/// <param name="logsheetId"></param>
		/// <returns></returns>
		public async Task<MessageModel<bool>> SendLogsheetIdToAndon(string logsheetId)
		{
			var result = new MessageModel<bool>
			{
				msg = "操作失败！",
				success = false
			};
			var request = FAJsonConvert.ToJson(new { logsheetId = logsheetId, SubAlarmType = "QUALITY06" });
			SerilogServer.LogDebug($"【SendLogsheetIdToAndon】Request：{request}{Environment.NewLine}Response：null", "AndonLog");
			var apiResult = await HttpHelper.PostAsync<bool>("ANDON", "andon/AlarmRecord/SendDelayMeassage", _user.GetToken(), request);
			if (apiResult == null)
			{
				result.success = false;
				result.msg = "接口调用返回为空！";
				SerilogServer.LogDebug($"【SendLogsheetIdToAndon】Request：{request}{Environment.NewLine}Response：null", "AndonLog");
				return result;
			}
			SerilogServer.LogDebug($"【SendLogsheetIdToAndon】Request：{request}{Environment.NewLine}Response：{FAJsonConvert.ToJson(apiResult)}", "AndonLog");
			result.success = true;
			result.msg = "操作成功！";
			result = apiResult;
			return result;
		}

		/// <summary>
		/// check logsheet检验超时：当送检超过【15】分钟未填写检验结果时，将检验单信息发送给E-Andon系统 接口内容：通过工单号+序号查找检验表单，如果检验结果为空，则发送andon。
		/// </summary>
		/// <param name="logsheetId"></param>
		/// <returns></returns>
		public async Task<MessageModel<dynamic>> CheckInspectionTime(string logsheetId)
		{
			var result = new MessageModel<dynamic>
			{
				msg = "操作失败！",
				success = false
			};
			SerilogServer.LogDebug($"【CheckInspectionTime】request：{logsheetId}", "AndonLog");
			var logsheet = await _logsheetdal.FindEntity(logsheetId);
			if (logsheet == null)
			{
				result.msg = "未找到此表单Id";
				return result;
			}
			if (logsheet.Status == 0)
			{
				var parameterGroup = await _parameterGroupdal.FindEntity(logsheet.ParameterGroupId);
				var equipment = await _dal15.FindEntity(logsheet.EquipmentId);
				//var sample = await _sampvDal.FindEntity(x => x.LogsheetId == logsheetId);
				//if (sample == null)
				//{
				//	result.msg = "未找到取样单";
				//	return result;
				//}
				string productionOrderNo = "";
				var poProducedExecution = await _dal14.FindEntity(logsheet.PoExecutionId);
				if (poProducedExecution == null)
				{
					productionOrderNo = $"PoExecutionId:{logsheet.PoExecutionId}";
				}
				else
				{
					var bProductionOrder = await _bproDal.FindEntity(poProducedExecution.ProductionOrderId);
					if (bProductionOrder == null)
					{
						productionOrderNo = $"ProductionOrderId:{poProducedExecution.ProductionOrderId}";
					}
					else
					{
						productionOrderNo = bProductionOrder.ProductionOrderNo;
						//roductionOrderNo = $"{bProductionOrder.ProductionOrderNo}({bProductionOrder.Sequence}/{bProductionOrder.Count})";
					}
				}
				double time = GetMinutesDifference(logsheet.CreateDate, DateTime.Now);
				var request = BuildAndonModel("QUALITY", "QUALITY06", equipment?.EquipmentCode ?? logsheet.EquipmentId, "Equipment", DateTime.Now,
				//"工单【020115776224(3/12)】，于【2024-11-14 18:00:00(送检时间)】送检的【出料质检单（检验类型）】已送检超过【15（传入时间）】分钟，尚未填写检验结果 ！",
				"工单【{0}】，于【{1}】送检的【{2}】已送检超过【{3}】分钟，尚未填写检验结果 ！",
				new object[] {
					productionOrderNo,
					logsheet.CreateDate.ToString("yyyy-MM-dd HH:mm:ss"),
					parameterGroup?.GroupName ?? logsheet.ParameterGroupId,
					time }
				);
				result.response = request;
				//return await ExecuteAndon(request);
			}
			result.success = true;
			result.msg = "操作成功！";
			return result;
		}

		#endregion

		#region 重新送检超时

		/// <summary>
		/// 发送logsheet表单Id给Andon
		/// </summary>
		/// <param name="logsheetId"></param>
		/// <param name="time"></param>
		/// <returns></returns>
		public async Task<MessageModel<bool>> SendNotuptostandardLogsheetIdToAndon(string logsheetId, DateTime inspectionTime)
		{
			var result = new MessageModel<bool>
			{
				msg = "操作失败！",
				success = false
			};
			var request = FAJsonConvert.ToJson(new { logsheetId = logsheetId, inspectionTime = inspectionTime, SubAlarmType = "QUALITY04" });
			SerilogServer.LogDebug($"【SendLogsheetIdToAndon】Request：{request}{Environment.NewLine}Response：null", "AndonLog");
			var apiResult = await HttpHelper.PostAsync<bool>("ANDON", "andon/AlarmRecord/SendDelayMeassage", _user.GetToken(), request);
			if (apiResult == null)
			{
				result.success = false;
				result.msg = "接口调用返回为空！";
				SerilogServer.LogDebug($"【SendLogsheetIdToAndon】Request：{request}{Environment.NewLine}Response：null", "AndonLog");
				return result;
			}
			SerilogServer.LogDebug($"【SendLogsheetIdToAndon】Request：{request}{Environment.NewLine}Response：{FAJsonConvert.ToJson(apiResult)}", "AndonLog");
			result.success = true;
			result.msg = "操作成功！";
			result = apiResult;
			return result;
		}

		/// <summary>
		/// 重新送检超时：检验结果不达标，30分钟内未查询到重新送检数据时触发。
		/// </summary>
		/// <param name="logsheetId"></param>
		/// <param name="time"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> CheckResubmissionDuration(string logsheetId, DateTime inspectionTime)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			SerilogServer.LogDebug($"【CheckResubmissionDuration】request：{logsheetId}", "AndonLog");
			var logsheet = await _logsheetdal.FindEntity(logsheetId);
			if (logsheet == null)
			{
				result.msg = "未找到此表单Id";
				return result;
			}
			//找重调样看是否存在
			var logsheets = await _logsheetdal.FindList(x => x.ID != logsheetId && x.EquipmentId == logsheet.EquipmentId && x.BatchId == logsheet.BatchId && x.Type == 5 /*&& x.PoExecutionId == logsheet.PoExecutionId */&& x.CreateDate > inspectionTime && x.ParameterGroupId == logsheet.ParameterGroupId);
			if (logsheets != null && !logsheets.Any())
			{
				string productionOrderNo = "";
				var poProducedExecution = await _dal14.FindEntity(logsheet.PoExecutionId);
				if (poProducedExecution == null)
				{
					productionOrderNo = $"PoExecutionId:{logsheet.PoExecutionId}";
				}
				else
				{
					var bProductionOrder = await _bproDal.FindEntity(poProducedExecution.ProductionOrderId);
					if (bProductionOrder == null)
					{
						productionOrderNo = $"ProductionOrderId:{poProducedExecution.ProductionOrderId}";
					}
					else
					{
						productionOrderNo = bProductionOrder.ProductionOrderNo;
						//roductionOrderNo = $"{bProductionOrder.ProductionOrderNo}({bProductionOrder.Sequence}/{bProductionOrder.Count})";
					}
				}
				var parameterGroup = await _parameterGroupdal.FindEntity(logsheet.ParameterGroupId);
				var equipment = await _dal15.FindEntity(logsheet.EquipmentId);
				//var sample = await _sampvDal.FindEntity(x => x.LogsheetId == logsheetId);
				//if (sample == null)
				//{
				//	result.msg = "未找到取样单";
				//	return result;
				//}
				double duration = GetMinutesDifference(inspectionTime, DateTime.Now);
				var request = BuildAndonModel("QUALITY", "QUALITY04", equipment?.EquipmentCode ?? logsheet.EquipmentId, "Equipment", DateTime.Now,
				//"工单【020115776224】的【出料质检单】检验结果不通过，超过【30（传入参数）】分钟还未重新送检",
				"工单【{0}】的【{1}】检验结果不通过，超过【{2}】分钟还未重新送检",
				new object[] {
					productionOrderNo,
					parameterGroup?.GroupName ?? logsheet.ParameterGroupId,
					duration }
				);
				return await ExecuteAndon(request);
			}
			result.success = true;
			result.msg = "操作成功！";
			return result;
		}

		#endregion

		#region 投料超时

		/// <summary>
		/// 发送batchId给Andon
		/// </summary>
		/// <param name="batchId"></param>
		/// <returns></returns>
		public async Task<MessageModel<bool>> SendBatchIdToAndon(string batchId, string equipmentId)
		{
			var result = new MessageModel<bool>
			{
				msg = "操作失败！",
				success = false
			};
			var request = FAJsonConvert.ToJson(new { batchId = batchId, equipmentId = equipmentId, applicationTime = DateTime.Now, SubAlarmType = "PRODUCTION04" });
			SerilogServer.LogDebug($"【SendBatchIdToAndon】Request：{request}{Environment.NewLine}Response：null", "AndonLog");
			var apiResult = await HttpHelper.PostAsync<bool>("ANDON", "andon/AlarmRecord/SendDelayMeassage", _user.GetToken(), request);
			if (apiResult == null)
			{
				result.success = false;
				result.msg = "接口调用返回为空！";
				SerilogServer.LogDebug($"【SendBatchIdToAndon】Request：{request}{Environment.NewLine}Response：null", "AndonLog");
				return result;
			}
			SerilogServer.LogDebug($"【SendBatchIdToAndon】Request：{request}{Environment.NewLine}Response：{FAJsonConvert.ToJson(apiResult)}", "AndonLog");
			result.success = true;
			result.msg = "操作成功！";
			result = apiResult;
			return result;
		}

		/// <summary>
		/// check batch投料超时：查询发起投料申请超过0.5小时未投料完成的工单
		/// </summary>
		/// <param name="batchId"></param>
		/// <param name="equipmentId"></param>
		/// <param name="applicationTime"></param>
		/// <returns></returns>
		public async Task<MessageModel<dynamic>> CheckFeedingTime(string batchId, string equipmentId, DateTime applicationTime)
		{
			var result = new MessageModel<dynamic>
			{
				msg = "操作失败！",
				success = false
			};
			SerilogServer.LogDebug($"【CheckFeedingTime】request：{batchId}", "AndonLog");
			var batch = await _dal6.FindEntity(batchId);
			if (batch == null)
			{
				result.msg = "未找到此batchId";
				return result;
			}
			if (batch.PrepStatus != "8" && batch.PrepStatus != "9")
			{
				var equipment = await _dal15.FindEntity(equipmentId);
				string productionOrderNo = "";
				var bProductionOrder = await _bproDal.FindEntity(batch.ProductionOrderId);
				if (bProductionOrder == null)
				{
					productionOrderNo = $"ProductionOrderId:{batch.ProductionOrderId}";
				}
				else
				{
					productionOrderNo = bProductionOrder.ProductionOrderNo;
					//roductionOrderNo = $"{bProductionOrder.ProductionOrderNo}({bProductionOrder.Sequence}/{bProductionOrder.Count})";
				}
				double time = GetMinutesDifference(applicationTime, DateTime.Now, "Hour");
				var request = BuildAndonModel("PRODUCTION", "PRODUCTION04", equipment?.EquipmentCode ?? equipmentId, "Equipment", DateTime.Now,
				//"工单【020115776224(3/12)】，于【2024-11-14 18:00:00(投料申请时间)】发起的投料申请，已超过【0.5（传入参数）】小时，尚未完成投料！",
				"工单【{0}】，于【{1}】发起的投料申请，已超过【{2}】小时，尚未开始投料！",
				new object[] {
					productionOrderNo,
					applicationTime.ToString("yyyy-MM-dd HH:mm:ss"),
					time }
				);
				//result.response = andonModel;
				return await ExecuteAndon(request);
			}
			result.success = true;
			result.msg = "操作成功！";
			return result;
		}

		#endregion

		#region CIP清洗结束恢复生产超时

		/// <summary>
		/// 发送CipEquipmentId给Andon
		/// </summary>
		/// <param name="logsheetId"></param>
		/// <returns></returns>
		public async Task<MessageModel<bool>> SendCipEquipmentIdToAndon(string equipmentId, DateTime cipTime)
		{
			var result = new MessageModel<bool>
			{
				msg = "操作失败！",
				success = false
			};
			var request = FAJsonConvert.ToJson(new { equipmentId = equipmentId, cipTime = cipTime, SubAlarmType = "PRODUCTION07" });
			SerilogServer.LogDebug($"【SendCipEquipmentIdToAndon】Request：{request}{Environment.NewLine}Response：null", "AndonLog");
			var apiResult = await HttpHelper.PostAsync<bool>("ANDON", "andon/AlarmRecord/SendDelayMeassage", _user.GetToken(), request);
			if (apiResult == null)
			{
				result.success = false;
				result.msg = "接口调用返回为空！";
				SerilogServer.LogDebug($"【SendCipEquipmentIdToAndon】Request：{request}{Environment.NewLine}Response：null", "AndonLog");
				return result;
			}
			SerilogServer.LogDebug($"【SendCipEquipmentIdToAndon】Request：{request}{Environment.NewLine}Response：{FAJsonConvert.ToJson(apiResult)}", "AndonLog");
			result.success = true;
			result.msg = "操作成功！";
			result = apiResult;
			return result;
		}

		/// <summary>
		/// CIP清洗结束恢复生产超时：查询CIP清洗清洗结束后【40】分钟未开启的工单信息（接收到MMI清洗完结束，40分钟后未查到该设备有工单开始记录触发报警）
		/// </summary>
		/// <param name="equipmentId"></param>
		/// <param name="cipTime"></param>
		/// <returns></returns>
		public async Task<MessageModel<dynamic>> CheckAfterCIPResumeTime(string equipmentId, DateTime cipTime)
		{
			var result = new MessageModel<dynamic>
			{
				msg = "操作失败！",
				success = false
			};
			SerilogServer.LogDebug($"【CheckAfterCIPResumeTime】request：{equipmentId}", "AndonLog");
			var equipment = await _dal15.FindEntity(equipmentId);
			if (equipment == null)
			{
				result.msg = "未找到此equipmentId";
				return result;
			}
			double time = GetMinutesDifference(cipTime, DateTime.Now);
			var poProducedExecutions = await _dal14.FindList(x => x.RunEquipmentId == equipmentId && x.StartTime > cipTime && x.StartTime <= DateTime.Now);
			//cip结束后40分钟内未有启动过工单
			if (poProducedExecutions == null || poProducedExecutions.Count == 0)
			{
				//找到当前设备可用的第一个工单
				var processOrder = (await _dal37.FindList(x => x.EquipmentId == equipment.ID && (x.Status == 2 || x.Status == 5 || x.Status == 6), x => x.PlanStartTime)).OrderBy(x => x.Sequence).FirstOrDefault();
				var request = BuildAndonModel("PRODUCTION", "PRODUCTION07", equipment.EquipmentCode, "Equipment", DateTime.Now,
					//"工单【020115776224】CIP结束已经超过【40分钟（传入参数）】，尚未开启工单",
					"工单【{0}】CIP结束已经超过【{1}分钟】，尚未开启工单！",
					new object[] {
									processOrder?.ProcessOrder ?? "",
									time }
					);
				//result.response = request;
				return await ExecuteAndon(request);
			}
			result.success = true;
			result.msg = "操作成功！";
			return result;
		}

		#endregion

		#region 未按时取样 Cook

		/// <summary>
		/// 发送QA时间给Andon
		/// </summary>
		/// <param name="logsheetId"></param>
		/// <param name="qaTime"></param>
		/// <returns></returns>
		public async Task<MessageModel<bool>> SendQATimeToAndon(string logsheetId, DateTime qaTime)
		{
			var result = new MessageModel<bool>
			{
				msg = "操作失败！",
				success = false
			};
			var request = FAJsonConvert.ToJson(new { logsheetId = logsheetId, qaTime = qaTime, SubAlarmType = "QUALITY02" });
			SerilogServer.LogDebug($"【SendQATimeToAndon】Request：{request}{Environment.NewLine}Response：null", "AndonLog");
			var apiResult = await HttpHelper.PostAsync<bool>("ANDON", "andon/AlarmRecord/SendDelayMeassage", _user.GetToken(), request);
			if (apiResult == null)
			{
				result.success = false;
				result.msg = "接口调用返回为空！";
				SerilogServer.LogDebug($"【SendQATimeToAndon】Request：{request}{Environment.NewLine}Response：null", "AndonLog");
				return result;
			}
			SerilogServer.LogDebug($"【SendQATimeToAndon】Request：{request}{Environment.NewLine}Response：{FAJsonConvert.ToJson(apiResult)}", "AndonLog");
			result.success = true;
			result.msg = "操作成功！";
			result = apiResult;
			return result;
		}

		/// <summary>
		/// QA通过后，每【2】小时检查一次工单是否结束，如果未结束则再检查【2】小时内是否有特检取样。
		/// </summary>
		/// <param name="logsheetId"></param>
		/// <param name="qaTime"></param>
		/// <returns></returns>
		public async Task<MessageModel<dynamic>> CheckSamplingOnTime(string logsheetId, DateTime qaTime)
		{
			var result = new MessageModel<dynamic>
			{
				msg = "操作失败！",
				success = false
			};
			SerilogServer.LogDebug($"【CheckSamplingOnTime】request：logsheetId：{logsheetId},qaTime：{qaTime}", "AndonLog");
			var logsheet = await _logsheetdal.FindEntity(logsheetId);
			if (logsheet == null)
			{
				result.msg = "未找到此logsheetId";
				return result;
			}
			var poProducedExecution = await _dal14.FindEntity(logsheet.PoExecutionId);
			if (poProducedExecution == null)
			{
				result.msg = "未找到此PoExecutionId";
				return result;
			}
			var productionOrder = await _dal5.FindEntity(poProducedExecution.ProductionOrderId);
			if (productionOrder == null)
			{
				result.msg = "未找到此ProductionOrderId";
				return result;
			}
			if (productionOrder.PoStatus != "6")
			{
				result.msg = $"工单{productionOrder.ProductionOrderNo}未运行";
				return result;
			}
			//找到当前工单的执行记录
			var poProducedExecutions = await _dal14.FindList(x => x.ProductionOrderId == poProducedExecution.ProductionOrderId);
			//找到当前工单的未结束的执行记录
			var poProducedExecutions1 = poProducedExecutions.FindAll(x => x.ProductionOrderId == poProducedExecution.ProductionOrderId && x.Status == "1" && x.EndTime == null);
			if (!poProducedExecutions1.Any())
			{
				result.msg = $"工单{productionOrder.ProductionOrderNo}未运行";
				return result;
			}
			var now = DateTime.Now;
			//发送新的时间给ANDON
			await SendQATimeToAndon(poProducedExecution.ProductionOrderId, now);
			var poExecutionIds = poProducedExecutions.Select(x => x.ID);
			//找到当前工单指定时间范围内相关的特检取样表单
			var logsheets = await _logsheetdal.FindList(x => poExecutionIds.Contains(x.PoExecutionId) && x.Type == 4 && x.CreateDate >= qaTime && x.CreateDate <= now);
			//规定时间内未进行特检取样 触发ANDON报警
			if (!logsheets.Any())
			{
				var batch = await _dal6.FindEntity(poProducedExecution.BatchId);
				double time = 2;
				//double time = GetMinutesDifference(qaTime, now, "Hour");
				var request = BuildAndonModel("QUALITY", "QUALITY02", productionOrder.LineCode, "ProductLine", DateTime.Now,
					//"工单：【工单号（批号）】已超过2小时未取样",
					"工单：【{0}】已超过{1}小时未取样",
					new object[] {
											$"{productionOrder.ProductionOrderNo}（{batch?.Number ?? ""}）",
											time }
					);
				//result.response = request;
				return await ExecuteAndon(request);
			}


			result.success = true;
			result.msg = "操作成功！";
			return result;
		}

		#endregion

		#endregion

		#region 实时触发 count：5

		/// <summary>
		/// 备料超时通知：工单开始时，判断下一个工单，如果未完成备料则触发andon
		/// </summary>
		/// <param name="poExecutionId"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> CheckNextOrderMaterialPrep(string poExecutionId)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			SerilogServer.LogDebug($"【CheckNextOrderMaterialPrep】PoExecutionId：{poExecutionId}", "AndonLog");
			string productionOrderNo = "";
			var poProducedExecution = await _dal14.FindEntity(poExecutionId);
			if (poProducedExecution == null)
			{
				result.msg = "未找到poExecutionId";
				return result;
			}
			var equipment = await _dal15.FindEntity(poProducedExecution.RunEquipmentId);
			if (equipment == null)
			{
				result.msg = "未找到poProducedExecution.RunEquipmentId";
				return result;
			}
			var bProductionOrder = await _bproDal.FindEntity(poProducedExecution.ProductionOrderId);
			if (bProductionOrder == null)
			{
				result.msg = "未找到ProductionOrderId";
				return result;
			}
			var whereExpression = Expressionable.Create<BProductionOrderListViewEntity>()
				.And(a => a.SapOrderType == bProductionOrder.SapOrderType)
				.And(a => a.PoStatus == "2" || a.PoStatus == "5" || a.PoStatus == "6")
				.ToExpression();
			var data = await _bproDal.Db.Queryable<BProductionOrderListViewEntity>()
					.Where(whereExpression)
					.OrderBy(x => x.LineCode)
					.OrderBy(x => x.PlanStartTime)
					.OrderBy(x => x.FormulaSequence)
					.OrderBy(x => x.Sequence).ToListAsync();
			var nextOrderIndex = data.IndexOf(bProductionOrder) + 1;
			if (nextOrderIndex >= data.Count)
			{
				result.msg = "未找到下一个工单";
				return result;
			}
			var nextOrder = data[nextOrderIndex];
			//找到未完成备料的批次
			var batchs = await _dal6.FindList(x => x.ProductionOrderId == nextOrder.ID && (x.PrepStatus == "2" || x.PrepStatus == "10" || x.PrepStatus == "11"));
			if (batchs.Any())
			{
				productionOrderNo = bProductionOrder.ProductionOrderNo;
				//roductionOrderNo = $"{bProductionOrder.ProductionOrderNo}({bProductionOrder.Sequence}/{bProductionOrder.Count})";
				var request = BuildAndonModel("PRODUCTION", "PRODUCTION02", equipment?.EquipmentCode ?? poProducedExecution.RunEquipmentId, "Equipment", DateTime.Now,
				//"工单【020115776224】已开始，下一个工单【020115776225】还未完成备料",
				"工单【{0}】已开始，下一个工单【{2}】还未完成备料",
				new object[] {
					productionOrderNo,
					nextOrder.ProductionOrderNo
					}
				);
				return await ExecuteAndon(request);
			}
			result.success = true;
			result.msg = "操作成功！";
			return result;
		}

		/// <summary>
		/// 取样送检结果不达标：检验表单完成时，如果结果不通过，将检验单信息发送给E-Andon系统
		/// </summary>
		/// <param name="logsheetId"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> CheckInspectionResult(string logsheetId)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			SerilogServer.LogDebug($"【CheckInspectionResult】request：{logsheetId}", "AndonLog");
			var logsheet = await _logsheetdal.FindEntity(logsheetId);
			if (logsheet == null)
			{
				result.msg = "未找到此表单Id";
				return result;
			}
			//if (logsheet.Status == 2)
			if (true)
			{
				var parameterGroup = await _parameterGroupdal.FindEntity(logsheet.ParameterGroupId);
				if (parameterGroup != null)
				{
					var functionProperty = await _funpdal.FindEntity(x => x.EquipmentId == logsheet.EquipmentId && x.FunctionCode == "POManagement" && x.PropertyCode == "CookInspectionLogsheet");
					var pv = functionProperty?.PropertyValue ?? functionProperty?.DefaultValue;
					if (pv == parameterGroup.Code)
					{
						var logsheetDetails = await _logsheetDetaildal.FindList(x => x.LogsheetId == logsheetId);
						var inspectionResult = logsheetDetails.Find(x => x.ParameterName == "通过结果")?.Value;
						if (inspectionResult != "通过")
						{
							StringBuilder stringBuilder = new();
							var strList = new List<string>();
							foreach (var item in logsheetDetails)
							{
								strList.Add($"{item.ParameterName}：{item.Value}");
							}
							stringBuilder.AppendJoin('，', strList);
							var equipment = await _dal15.FindEntity(logsheet.EquipmentId);
							string productionOrderNo = "";
							var poProducedExecution = await _dal14.FindEntity(logsheet.PoExecutionId);
							if (poProducedExecution == null)
							{
								productionOrderNo = $"PoExecutionId:{logsheet.PoExecutionId}";
							}
							else
							{
								var bProductionOrder = await _bproDal.FindEntity(poProducedExecution.ProductionOrderId);
								if (bProductionOrder == null)
								{
									productionOrderNo = $"ProductionOrderId:{poProducedExecution.ProductionOrderId}";
								}
								else
								{
									productionOrderNo = bProductionOrder.ProductionOrderNo;
									//roductionOrderNo = $"{bProductionOrder.ProductionOrderNo}({bProductionOrder.Sequence}/{bProductionOrder.Count})";
								}
							}
							double time = GetMinutesDifference(logsheet.CreateDate, DateTime.Now);
							var request = BuildAndonModel("QUALITY", "QUALITY03", equipment?.EquipmentCode ?? logsheet.EquipmentId, "Equipment", DateTime.Now,
							//工单【020115776224】的【出料质检单】检验结果不通过，具体检验数据：项目1：检验值10.0，项目2：检验值：20.0。",
							"工单【{0}】的【{1}】检验结果不通过，具体检验数据：{2}。",
							new object[] {
							productionOrderNo,
							parameterGroup?.GroupName ?? logsheet.ParameterGroupId,
							stringBuilder }
									);
							//取样送检结果不达标需把logsheetId发送给Andon
							//MessageModel<string> apiResult = await HttpHelper.PostAsync<string>("ANDON", "api/Andon/ExecuteAndon?logsheetId=/"+logsheetId, _user.GetToken(), request);
							//if (apiResult == null)
							//{
							//	result.success = false;
							//	result.msg = "接口调用返回为空！";
							//	return result;
							//}
							return await ExecuteAndon(request);
						}
					}
				}
			}
			result.success = true;
			result.msg = "操作成功！";
			return result;
		}

		/// <summary>
		/// 工单修改：MES工单修改（数量，生产线、生产中心）时触发
		/// </summary>
		/// <param name="productionOrderNo">工单号</param>
		/// <param name="productLine">产线编码</param>
		/// <param name="editInfo">修改内容</param>
		/// <returns></returns>
		public async Task<MessageModel<string>> EditOrder(string productionOrderNo, string productLine, List<dynamic> editInfo)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			if (editInfo == null || editInfo.Count == 0)
			{
				return result;
			}
			SerilogServer.LogDebug($"【EditOrder】ProductionOrderNo：{productionOrderNo}，ProductLine：{productLine}，EditInfo：{FAJsonConvert.ToJson(editInfo)}", "AndonLog");
			try
			{
				StringBuilder stringBuilder = new();
				var strList = new List<string>();
				foreach (var item in editInfo)
				{
					strList.Add($"{item.ParameterName}为【{item.Value}】");
				}
				stringBuilder.AppendJoin('，', strList);
				var request = BuildAndonModel("PRODUCTION", "PRODUCTION08", productLine, "ProductLine", DateTime.Now,
					//"工单【C202411150047】修改了数量为【100】",
					"工单【{0}】修改了{1}",
					new object[] {
					productionOrderNo,
					stringBuilder
						}
					);
				return await ExecuteAndon(request);
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				return result;
			}
		}

		/// <summary>
		/// 线边原物料低于水位线：查询线边原物料低于水位线（物料安全线）触发
		/// </summary>
		/// <param name="productLine">线边库编码</param>
		/// <param name="materials">materials</param>
		/// <returns></returns>
		public async Task<MessageModel<string>> CheckRawWaterLevel()
		{

			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			string productLine = "";
			List<string> materials = new List<string>();
			//缺少计算安全水位逻辑

			if (materials == null || materials.Count == 0)
			{
				return result;
			}
			SerilogServer.LogDebug($"【CheckRawWaterLevel】ProductLine：{productLine}，Materials：{FAJsonConvert.ToJson(materials)}", "AndonLog");
			try
			{
				StringBuilder stringBuilder = new();
				stringBuilder.AppendJoin('，', materials);
				var request = BuildAndonModel("INVENTORY", "INVENTORY06", productLine, "ProductLine", DateTime.Now,
					//"以下物料【物料编码+物料名称】线边库存低于水位线",
					"以下物料【{0}】线边库存低于水位线",
					new object[] {
					stringBuilder
						}
					);
				return await ExecuteAndon(request);
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				return result;
			}
		}

		/// <summary>
		/// 不良品超过阈值：每次出现不良品时，判断1小时内不良品是否超过指定个数时，发送不良品信息给E-Andon系统
		/// </summary>
		/// <param name="logsheetId">logsheetId</param>
		/// <param name="hour">小时数</param>
		/// <param name="quantity">数量</param>
		/// <returns></returns>
		public async Task<MessageModel<string>> CheckQuantityOfRejects(string logsheetId, int hour, int quantity)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			SerilogServer.LogDebug($"【CheckQuantityOfRejects】hour：{hour}，quantity：{quantity}");
			var endTime = DateTime.Now;
			var startTime = endTime.AddHours(-hour);
			var logsheet = await _logsheetdal.FindEntity(logsheetId);
			if (logsheet != null)
			{
				// 获取所有设备数据
				var allEquipments = await _dal.Db.Queryable<EquipmentEntity>().Where(x => x.Level == "Unit").ToListAsync();

				// 查找指定 ID 的设备
				var specifiedEquipment = allEquipments.FirstOrDefault(e => e.ID == logsheet.EquipmentId);
				if (specifiedEquipment == null)
				{
					result.msg = $"设备 ID {logsheet.EquipmentId} 不存在。";
					return result;
				}

				// 查找对应的 Line
				var line = FindLine(allEquipments, specifiedEquipment.ID);
				if (line == null)
				{
					result.msg = ($"设备 ID {specifiedEquipment.ID} 没有找到对应的 Line");
					return result;
				}

				// 查找 Line 下的所有 Unit
				var unitsUnderLine = new List<EquipmentEntity>();
				FindUnitsRecursively(allEquipments, line.ID, unitsUnderLine);

				var logsheets = await _logsheetdal.FindList(x => x.Status == 2 && x.ModifyDate >= startTime && x.ModifyDate <= endTime && unitsUnderLine.Any(x2 => x.EquipmentId == x2.ID));
				var logsheetDetails = await _logsheetDetaildal.FindList(x => logsheets.Any(x2 => x2.ID == x.LogsheetId) && x.ParameterName == "通过结果");
				int quantityOfRejects = 0;
				foreach (var item in logsheets)
				{
					var logsheetDetail = logsheetDetails.Find(x => x.LogsheetId == item.ID);
					if (logsheetDetail != null && !string.IsNullOrEmpty(logsheetDetail.Value) && logsheetDetail.Value != "通过")
					{
						quantityOfRejects++;
					}
				}
				if (quantityOfRejects > quantity)
				{
					var request = BuildAndonModel("QUALITY", "QUALITY05", line.EquipmentCode, "ProductLine", DateTime.Now,
					//"产线【产线名称】1小时内已出现【N（传入参数）】个不良品",
					"产线【{0}】1小时内已出现【{1}】个不良品",
					new object[] {
					hour,
					quantity
					}
					);
					return await ExecuteAndon(request);
				}
			}
			result.success = true;
			result.msg = "操作成功！";
			return result;

		}

		/// <summary>
		/// 平均重量不在指定范围内的次数大于指定阈值：每次称量后判断当前这次称量任务称量不合格次数
		/// </summary>
		/// <param name="recordId">logsheetId</param>
		/// <returns></returns>
		public async Task<MessageModel<string>> CheckWeight(string recordId)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			SerilogServer.LogDebug($"【CheckWeight】recordId：{recordId}");
			var dataItem = (await _dal33.FindList(x => x.ItemCode == "CheckWeightCount"))?.FirstOrDefault();
			if (dataItem == null)
			{
				result.msg = "未找到dataItem，请检查数据字典配置！";
				return result;
			}

			var dataItemDetail = (await _dal34.FindList(x => x.ItemId == dataItem.ID))?.FirstOrDefault();
			if (dataItemDetail == null)
			{
				result.msg = "未找到dataItemDetail，请检查数据字典配置！";
				return result;
			}

			if (!int.TryParse(dataItemDetail.ItemValue, out int rate))
			{
				result.msg = "dataItemDetail.ItemValue 转换decimal类型失败，请检查数据字典配置！";
				return result;
			}

			// 获取数据
			var weightRecord = (await _dal.Db.Queryable<WeightRecordEntity>().Where(x => x.ID == recordId).ToListAsync())?.FirstOrDefault();
			if (weightRecord == null)
			{
				result.msg = "未找到weightRecord！";
				return result;
			}

			var weightRecordLists = await _dal.Db.Queryable<WeightRecordListEntity>().Where(x => x.RecordId == recordId).OrderBy(x => x.Num).ToListAsync();
			if (weightRecordLists == null || weightRecordLists.Count == 0)
			{
				result.msg = "未找到weightRecordList！";
				return result;
			}

			var weightRecordLists2 = weightRecordLists.Where(x => !string.IsNullOrEmpty(x.Weight)).ToList();
			if (weightRecordLists2 == null || weightRecordLists2.Count == 0)
			{
				result.msg = "未找到符合条件的weightRecordList！";
				return result;
			}

			var rate1 = (decimal)weightRecordLists2.Count / weightRecordLists.Count() * 100;
			if (rate1 < rate)
			{
				result.success = true;
				result.msg = $"当前有值的数据不足{rate}%";
				return result;
			}

			var productionOrder = await _dal5.FindEntity(weightRecord.OrderId);
			if (productionOrder == null)
			{
				result.msg = "未找到工单！";
				return result;
			}
			var sappackorder = (await _dal9.FindList(x => x.Aufnr == productionOrder.ProductionOrderNo))?.FirstOrDefault();
			if (sappackorder == null)
			{
				result.msg = "未找到SAP工单！";
				return result;
			}
			var materialIds = (await _dal2.FindList(x => x.Type == "ZSFG")).Select(x => x.ID).ToList() ?? new List<string>();

			var dshzs = (await _dal26.FindList(x =>
				x.ProductionOrderId == productionOrder.ID &&
				x.AdjustPercentQuantity != null &&
				materialIds.Contains(x.MaterialId)
				))
				?.Sum(x => x.AdjustPercentQuantity) ?? 0;

			var v1 = sappackorder.MngPu * sappackorder.Psmng;
			if (v1 == 0)
			{
				result.msg = "工单计划总产出为0！";
				return result;
			}
			var ul = dshzs / v1 * 1000;//上限
			var ll = sappackorder.Ntgew * 1000; //下限
			var acount = weightRecordLists2.Where(x => decimal.Parse(x.Weight) < ll || decimal.Parse(x.Weight) > ul)?.Count() ?? 0;
			var rate2 = (decimal)acount / weightRecordLists.Count() * 100;
			if (rate2 > rate)
			{
				var request = BuildAndonModel("QUALITY", "QUALITY07", productionOrder.LineCode, "ProductLine", DateTime.Now,
				//"当前抽样批【50%】罐装不符合规定",
				"当前抽样批【{0}%】罐装不符合规定,上限：{1}，下限：{2}，MngPu：{3}，Psmng：{4}，酱料带损耗：{5}",
				new object[] {
					rate,
					ul,
					ll,
					sappackorder.MngPu,
					sappackorder.Psmng,
					dshzs
				}
				);
				return await ExecuteAndon(request);
			}

			result.success = true;
			result.msg = "操作成功！";
			return result;

		}

		#region 包装工单变更，变动日期在最近2天之内

		/// <summary>
		/// 包装工单变更，变动日期在最近2天之内
		/// </summary>
		/// <param name="aufnr">工单</param>
		/// <param name="lineCode">产线编号</param>
		/// <param name="changInfo">变动内容</param>
		/// <returns></returns>
		public async Task<MessageModel<string>> PackOrderChange(string aufnr, string lineCode, DateTime plandate, string changInfo)
		{

			changInfo = $"\n工单号:【{aufnr}】\n"
				+ $"计划日期:【{plandate.ToString("yyyy-MM-dd")}】\n"
				+ changInfo;
			var request = BuildAndonModel("PRODUCTION", "PRODUCTION03", lineCode, "ProductLine", DateTime.Now, changInfo);
			SerilogServer.LogDebug($"【PackOrderChange】aufnr：{aufnr}，changInfo：{changInfo}", "PackOrderChangeAdnon");
			return await ExecuteAndon(request);
		}

		#endregion

		#endregion

		#endregion


		#region
		public class UnitWithLine
		{
			public string UnitID { get; set; }
			public string UnitParentId { get; set; }
			public string UnitCode { get; set; }
			public string UnitName { get; set; }
			public string LineID { get; set; }
			public string LineParentId { get; set; }
			public string LineCode { get; set; }
			public string LineName { get; set; }
		}

		/// <summary>
		/// 获取产线
		/// </summary>
		/// <param name="allEquipments"></param>
		/// <param name="currentId"></param>
		/// <returns></returns>
		private static EquipmentEntity FindLine(List<EquipmentEntity> allEquipments, string currentId)
		{
			var currentEquipment = allEquipments.FirstOrDefault(e => e.ID == currentId);
			if (currentEquipment == null)
			{
				return null;
			}

			if (currentEquipment.Level == "Line")
			{
				return currentEquipment;
			}

			if (!string.IsNullOrEmpty(currentEquipment.ParentId))
			{
				return FindLine(allEquipments, currentEquipment.ParentId);
			}

			return null;
		}

		/// <summary>
		/// 获取EquipmentLine
		/// </summary>
		/// <returns></returns>
		public async Task<List<UnitWithLine>> GetEquipmentWithLine()
		{
			// 获取所有设备数据
			var allEquipments = await _dal.Db.Queryable<EquipmentEntity>().ToListAsync();

			// 找到所有 Level 为 Unit 的设备
			var units = allEquipments.Where(e => e.Level == "Unit").ToList();

			// 结果列表
			var result = new List<UnitWithLine>();

			// 递归查找每个 Unit 对应的 Line
			foreach (var unit in units)
			{
				var line = FindLine(allEquipments, unit.ID);
				if (line != null)
				{
					result.Add(new UnitWithLine
					{
						UnitID = unit.ID,
						UnitParentId = unit.ParentId,
						UnitCode = unit.EquipmentCode,
						UnitName = unit.EquipmentName,
						LineID = line.ID,
						LineParentId = line.ParentId,
						LineCode = line.EquipmentCode,
						LineName = line.EquipmentName
					});
				}
			}
			return result;
		}

		/// <summary>
		/// 根据id获取下级所有Unit
		/// </summary>
		/// <param name="allEquipments"></param>
		/// <param name="parentId"></param>
		/// <param name="units"></param>
		private static void FindUnitsRecursively(List<EquipmentEntity> allEquipments, string parentId, List<EquipmentEntity> units)
		{
			var children = allEquipments.Where(e => e.ParentId == parentId).ToList();
			foreach (var child in children)
			{
				if (child.Level == "Unit")
				{
					units.Add(child);
				}
				else
				{
					FindUnitsRecursively(allEquipments, child.ID, units);
				}
			}
		}

		/// <summary>
		/// 构建请求实体
		/// </summary>
		/// <param name="mainAlarmType"></param>
		/// <param name="subAlarmType"></param>
		/// <param name="code"></param>
		/// <param name="type"></param>
		/// <param name="createDate"></param>
		/// <param name="format"></param>
		/// <param name="args"></param>
		/// <returns></returns>
		public static dynamic BuildAndonModel(string mainAlarmType, string subAlarmType, string code, string type, DateTime createDate, string format, params object?[] args)
		{
			StringBuilder alarmContent = new();
			alarmContent.AppendFormat(format, args);
			dynamic model = new ExpandoObject();
			model.MainAlarmType = mainAlarmType;
			model.SubAlarmType = subAlarmType;
			switch (type)
			{
				case "Equipment":
					model.EquipmentCode = code;
					break;
				case "Area":
					model.AreaCode = code;
					break;
				case "ProductLine":
					model.ProductLine = code;
					break;
				default:
					break;
			}
			model.AlarmContent = alarmContent.ToString();
			model.CreateDate = createDate;
			return model;
		}

		/// <summary>
		/// 执行调用API接口触发Andon
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> ExecuteAndon(dynamic request)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			SerilogServer.LogDebug($"【ExecuteAndon】Request：{FAJsonConvert.ToJson(request)}{Environment.NewLine}", "AndonLog");
			var apiResult = await HttpHelper.PostAsync<string>("ANDON", "andon/AlarmRecord/SaveForm", null, request);
			if (apiResult == null)
			{
				result.success = false;
				result.msg = "接口调用返回为空！";
				SerilogServer.LogDebug($"【ExecuteAndon】Request：{FAJsonConvert.ToJson(request)}{Environment.NewLine}Response：null", "AndonLog");
				return result;
			}
			SerilogServer.LogDebug($"【ExecuteAndon】Request：{FAJsonConvert.ToJson(request)}{Environment.NewLine}Response：{FAJsonConvert.ToJson(apiResult)}", "AndonLog");
			result.success = true;
			result.msg = "操作成功！";
			result = apiResult;
			return result;
		}

		/// <summary>
		/// 获取时间差
		/// </summary>
		/// <param name="startTime"></param>
		/// <param name="endTime"></param>
		/// <param name="format">要转换的单位</param>
		/// <returns></returns>
		public static double GetMinutesDifference(DateTime startTime, DateTime endTime, string format = "Minute")
		{
			TimeSpan timeDifference = endTime - startTime;
			double minutesDifference = 0;
			switch (format)
			{
				case "Day":
					minutesDifference = timeDifference.TotalDays;
					break;
				case "Hour":
					minutesDifference = timeDifference.TotalHours;
					break;
				case "Millisecond":
					minutesDifference = timeDifference.TotalMilliseconds;
					break;
				case "Minute":
					minutesDifference = timeDifference.TotalMinutes;
					break;
				case "Second":
					minutesDifference = timeDifference.TotalSeconds;
					break;
				default:
					minutesDifference = timeDifference.TotalMinutes;
					break;
			}
			return Math.Round(minutesDifference, 1);
		}

		#endregion

	}
}