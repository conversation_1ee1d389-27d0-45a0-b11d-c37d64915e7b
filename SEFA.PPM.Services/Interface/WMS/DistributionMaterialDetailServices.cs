using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SEFA.Base.Common.Helper;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Model;
using SEFA.PPM.IServices.Interface.WMS;
using SEFA.PPM.Model.Models.Interface.WMS;
using SEFA.PPM.Model.ViewModels.Interface.WMS;
using SEFA.PPM.Repository.Interface.WMS;
using SqlSugar;

namespace SEFA.PPM.Services.Interface.WMS
{
    /// <summary>
    /// 叫料申请明细服务实现类
    /// </summary>
    public class DistributionMaterialDetailServices : IDistributionMaterialDetailServices
    {
        private readonly IDistributionMaterialDetailRepository _detailRepository;
        private readonly IUnitOfWork _unitOfWork;

        public DistributionMaterialDetailServices(
            IDistributionMaterialDetailRepository detailRepository,
            IUnitOfWork unitOfWork)
        {
            _detailRepository = detailRepository;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// 根据主表ID获取叫料申请明细列表
        /// </summary>
        /// <param name="requestId">主表ID</param>
        /// <returns>叫料申请明细列表</returns>
        public async Task<List<DistributionMaterialDetailEntity>> GetByRequestIdAsync(string requestId)
        {
            return await _detailRepository.FindList(requestId);
        }

        /// <summary>
        /// 根据ID获取叫料申请明细
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns>叫料申请明细信息</returns>
        public async Task<DistributionMaterialDetailEntity> GetByIdAsync(string id)
        {
            return await _detailRepository.FindEntity(id);
        }

        /// <summary>
        /// 新增叫料申请明细
        /// </summary>
        /// <param name="detailEntity">叫料申请明细信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> AddAsync(DistributionMaterialDetailEntity detailEntity)
        {
            _unitOfWork.BeginTran();
            try
            {
                var result = await _detailRepository.Add(detailEntity) > 0;
                if (!result)
                {
                    _unitOfWork.RollbackTran();
                    return false;
                }

                _unitOfWork.CommitTran();
                return true;
            }
            catch
            {
                _unitOfWork.RollbackTran();
                throw;
            }
        }

        /// <summary>
        /// 批量新增叫料申请明细
        /// </summary>
        /// <param name="details">叫料申请明细列表</param>
        /// <returns>是否成功</returns>
        public async Task<bool> AddRangeAsync(List<DistributionMaterialDetailEntity> details)
        {
            _unitOfWork.BeginTran();
            try
            {
                var result = await _detailRepository.Add(details) > 0;
                if (!result)
                {
                    _unitOfWork.RollbackTran();
                    return false;
                }

                _unitOfWork.CommitTran();
                return true;
            }
            catch
            {
                _unitOfWork.RollbackTran();
                throw;
            }
        }

        /// <summary>
        /// 更新叫料申请明细
        /// </summary>
        /// <param name="detailEntity">叫料申请明细信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateAsync(DistributionMaterialDetailEntity detailEntity)
        {
            _unitOfWork.BeginTran();
            try
            {
                var result = await _detailRepository.Update(detailEntity);
                if (!result)
                {
                    _unitOfWork.RollbackTran();
                    return false;
                }

                _unitOfWork.CommitTran();
                return true;
            }
            catch
            {
                _unitOfWork.RollbackTran();
                throw;
            }
        }

        /// <summary>
        /// 删除叫料申请明细（逻辑删除）
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteAsync(string id)
        {
            _unitOfWork.BeginTran();
            try
            {
                var result = await _detailRepository.DeleteById(id);
                if (!result)
                {
                    _unitOfWork.RollbackTran();
                    return false;
                }

                _unitOfWork.CommitTran();
                return true;
            }
            catch
            {
                _unitOfWork.RollbackTran();
                throw;
            }
        }

        /// <summary>
        /// 根据主表ID删除叫料申请明细（逻辑删除）
        /// </summary>
        /// <param name="requestId">主表ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteByRequestIdAsync(string requestId)
        {
            _unitOfWork.BeginTran();
            try
            {
                var result = await _detailRepository.Delete(a => a.RequestId == requestId);
                if (!result)
                {
                    _unitOfWork.RollbackTran();
                    return false;
                }

                _unitOfWork.CommitTran();
                return true;
            }
            catch
            {
                _unitOfWork.RollbackTran();
                throw;
            }
        }

        /// <summary>
        /// 获取叫料申请明细列表（不分页）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>叫料申请明细列表</returns>
        public async Task<List<DistributionMaterialDetailView>> GetDetailListAsync(
            DistributionMaterialDetailQueryDto queryDto)
        {
            try
            {
                var whereExpression = BuildWhereExpression(queryDto);

                var query = _detailRepository.Db
                    .Queryable<DistributionMaterialDetailEntity, DistributionMaterialRequestEntity>((detail, request) =>
                        new JoinQueryInfos(JoinType.Left, detail.RequestId == request.ID))
                    .Where(whereExpression)
                    .Select((detail, request) => new DistributionMaterialDetailView
                    {
                        Id = detail.ID,
                        RequestId = detail.RequestId,
                        RequestSheetNo = request.RequestSheetNo,
                        LineWarehouseCode = request.LineWarehouseCode,
                        Plant = detail.Plant,
                        MaterialId = detail.MaterialId,
                        MaterialCode = detail.MaterialCode,
                        MaterialName = detail.MaterialName,
                        MaterialVersionId = detail.MaterialVersionId,
                        MaterialVersionCode = detail.MaterialVersionCode,
                        BatchNo = detail.BatchNo,
                        PalletNo = detail.PalletNo,
                        BarCode = detail.BarCode,
                        Quantity = detail.Quantity,
                        Unit = detail.Unit,
                        Density = detail.Density,
                        CoAContent = detail.CoAContent,
                        Remark = detail.Remark,
                        IsPutInStorage = detail.IsPutInStorage,
                        CreateDate = detail.CreateDate,
                        CreateUserId = detail.CreateUserId,
                        ModifyDate = detail.ModifyDate,
                        ModifyUserId = detail.ModifyUserId
                    })
                    .OrderBy(x => x.CreateDate, OrderByType.Desc);

                return await query.ToListAsync();
            }
            catch (Exception ex)
            {
                SerilogServer.LogError(ex, $"获取叫料申请明细列表失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取叫料申请明细分页列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>叫料申请明细分页列表</returns>
        public async Task<PageModel<DistributionMaterialDetailView>> GetDetailPageListAsync(
            DistributionMaterialDetailQueryDto queryDto)
        {
            PageModel<DistributionMaterialDetailView> result = new PageModel<DistributionMaterialDetailView>();
            try
            {
                var whereExpression = BuildWhereExpression(queryDto);

                var query = _detailRepository.Db
                    .Queryable<DistributionMaterialDetailEntity, DistributionMaterialRequestEntity>((detail, request) =>
                        new JoinQueryInfos(JoinType.Left, detail.RequestId == request.ID))
                    .Where(whereExpression)
                    .Select((detail, request) => new DistributionMaterialDetailView
                    {
                        Id = detail.ID,
                        RequestId = detail.RequestId,
                        RequestSheetNo = request.RequestSheetNo,
                        LineWarehouseCode = request.LineWarehouseCode,
                        Plant = detail.Plant,
                        MaterialId = detail.MaterialId,
                        MaterialCode = detail.MaterialCode,
                        MaterialName = detail.MaterialName,
                        MaterialVersionId = detail.MaterialVersionId,
                        MaterialVersionCode = detail.MaterialVersionCode,
                        BatchNo = detail.BatchNo,
                        PalletNo = detail.PalletNo,
                        BarCode = detail.BarCode,
                        Quantity = detail.Quantity,
                        Unit = detail.Unit,
                        Density = detail.Density,
                        CoAContent = detail.CoAContent,
                        Remark = detail.Remark,
                        IsPutInStorage = detail.IsPutInStorage,
                        CreateDate = detail.CreateDate,
                        CreateUserId = detail.CreateUserId,
                        ModifyDate = detail.ModifyDate,
                        ModifyUserId = detail.ModifyUserId
                    })
                    .OrderBy(x => x.CreateDate, OrderByType.Desc);

                RefAsync<int> totalCount = 0;
                result.data = await query.ToPageListAsync(queryDto.pageIndex, queryDto.pageSize, totalCount);
                result.dataCount = totalCount;
            }
            catch (Exception ex)
            {
                SerilogServer.LogError(ex, $"获取叫料申请明细分页列表失败: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 构建查询条件表达式
        /// </summary>
        /// <param name="queryDto">查询参数</param>
        /// <returns>查询条件表达式</returns>
        private System.Linq.Expressions.Expression<
            Func<DistributionMaterialDetailEntity, DistributionMaterialRequestEntity, bool>> BuildWhereExpression(
            DistributionMaterialDetailQueryDto queryDto)
        {
            var whereExpression = Expressionable
                .Create<DistributionMaterialDetailEntity, DistributionMaterialRequestEntity>()
                .And((detail, request) => detail.Deleted == 0 && request.Deleted == 0);

            // 时间范围查询
            if (queryDto.StartTime.HasValue)
            {
                whereExpression =
                    whereExpression.And((detail, request) => detail.CreateDate >= queryDto.StartTime.Value);
            }

            if (queryDto.EndTime.HasValue)
            {
                whereExpression = whereExpression.And((detail, request) => detail.CreateDate <= queryDto.EndTime.Value);
            }

            // 是否入库查询
            if (queryDto.IsPutInStorage.HasValue)
            {
                whereExpression = whereExpression.And((detail, request) =>
                    detail.IsPutInStorage == queryDto.IsPutInStorage.Value);
            }

            // 仓库编码模糊查询
            if (!string.IsNullOrWhiteSpace(queryDto.WarehouseCode))
            {
                whereExpression = whereExpression.And((detail, request) =>
                    request.LineWarehouseCode.Contains(queryDto.WarehouseCode));
            }

            // 物料编码模糊查询
            if (!string.IsNullOrWhiteSpace(queryDto.MaterialCode))
            {
                whereExpression =
                    whereExpression.And((detail, request) => detail.MaterialCode.Contains(queryDto.MaterialCode));
            }

            // 物料名称模糊查询
            if (!string.IsNullOrWhiteSpace(queryDto.MaterialName))
            {
                whereExpression =
                    whereExpression.And((detail, request) => detail.MaterialName.Contains(queryDto.MaterialName));
            }

            // 批次号模糊查询
            if (!string.IsNullOrWhiteSpace(queryDto.BatchNo))
            {
                whereExpression = whereExpression.And((detail, request) => detail.BatchNo.Contains(queryDto.BatchNo));
            }

            // 托盘号模糊查询
            if (!string.IsNullOrWhiteSpace(queryDto.PalletNo))
            {
                whereExpression = whereExpression.And((detail, request) => detail.PalletNo.Contains(queryDto.PalletNo));
            }

            // 工厂查询
            if (!string.IsNullOrWhiteSpace(queryDto.Plant))
            {
                whereExpression = whereExpression.And((detail, request) => detail.Plant == queryDto.Plant);
            }

            // 申请单号模糊查询
            if (!string.IsNullOrWhiteSpace(queryDto.RequestSheetNo))
            {
                whereExpression = whereExpression.And((detail, request) =>
                    request.RequestSheetNo.Contains(queryDto.RequestSheetNo));
            }

            return whereExpression.ToExpression();
        }
    }
}