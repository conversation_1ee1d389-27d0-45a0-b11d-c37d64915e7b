
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.PPM.Services
{
    public class PoProducedMaterialListViewServices : BaseServices<PoProducedMaterialListViewEntity>, IPoProducedMaterialListViewServices
    {
        private readonly IBaseRepository<PoProducedMaterialListViewEntity> _dal;
        public PoProducedMaterialListViewServices(IBaseRepository<PoProducedMaterialListViewEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<PoProducedMaterialListViewEntity>> GetList(PoProducedMaterialListViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<PoProducedMaterialListViewEntity>()
               .And(a => a.ProductionOrderId == reqModel.ProductionOrderId)
                .AndIF(!string.IsNullOrEmpty(reqModel.Key),
                a => a.MaterialCode.Contains(reqModel.Key) ||
                     a.MaterialDescription.Contains(reqModel.Key) ||
                     a.Resource.Contains(reqModel.Key) ||
                     a.SegmentName.Contains(reqModel.Key) ||
                     a.ProcName.Contains(reqModel.Key)
                )
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<PoProducedMaterialListViewEntity>> GetPageList(PoProducedMaterialListViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<PoProducedMaterialListViewEntity>()
                    .And(a => a.ProductionOrderId == reqModel.ProductionOrderId)
                    .AndIF(!string.IsNullOrEmpty(reqModel.Key),
                    a => a.MaterialCode.Contains(reqModel.Key) ||
                         a.MaterialDescription.Contains(reqModel.Key) ||
                         a.Resource.Contains(reqModel.Key) ||
                         a.SegmentName.Contains(reqModel.Key) ||
                         a.ProcName.Contains(reqModel.Key)
                    )
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

    }
}