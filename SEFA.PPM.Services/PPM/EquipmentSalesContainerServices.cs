
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using Magicodes.ExporterAndImporter.Excel;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base.Common.Common;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using System.Linq;
using SEFA.Base.Common.HttpContextUser;
using SEFA.PPM.Model.Models.Interface;
using static SEFA.PTM.Services.ConsumeViewServices;
using Microsoft.AspNetCore.DataProtection.KeyManagement;
using SEFA.Base.Common.WebApiClients.HttpApis;

namespace SEFA.PPM.Services
{
    public class EquipmentSalesContainerServices : BaseServices<EquipmentSalesContainerEntity>, IEquipmentSalesContainerServices
    {
        private readonly IBaseRepository<EquipmentSalesContainerEntity> _dal;
        private readonly IBaseRepository<SalescontainerEntity> _salescontainerDal;
        private readonly IDFMServices _dFMServices;
        private readonly IUser _user;
       public EquipmentSalesContainerServices(IBaseRepository<EquipmentSalesContainerEntity> dal, IDFMServices dFMServices,
            IBaseRepository<SalescontainerEntity> salescontainerDal,IUser user)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _dFMServices = dFMServices;
            _salescontainerDal = salescontainerDal;
            _user = user;
        }

        public async Task<List<EquipmentSalesContainerModel>> GetList(EquipmentSalesContainerRequestModel request)
        {
            var result = await _dal.Db.Queryable<EquipmentEntity, EquipmentSalesContainerEntity, SalescontainerEntity>((e, esc, sc)
              => new object[]
              {
                    JoinType.Inner, e.ID == esc.EquipmentId,
                    JoinType.Inner, esc.SalesContainerId == sc.ID
              })

           .Select((e, esc, sc) => new EquipmentSalesContainerModel
           {
               EquipmentId = e.ID,
               EquipmentCode = e.EquipmentCode,
               EquipmentName = e.EquipmentName,
               SalesContainerId = sc.ID,
               SalesContainerCode = sc.SalesContainer,
               SalesContainerName = sc.Description
           })
           .MergeTable()
              .WhereIF(!string.IsNullOrEmpty(request.EquipmentId), a => a.EquipmentId == request.EquipmentId)
           .WhereIF(!string.IsNullOrEmpty(request.Key), a =>
              (a.EquipmentName != null && a.EquipmentName.Contains(request.Key))
           || (a.EquipmentCode != null && a.EquipmentCode.Contains(request.Key))
           || (a.SalesContainerCode != null && a.SalesContainerCode.Contains(request.Key))
           || (a.SalesContainerName != null && a.SalesContainerName.Contains(request.Key)))
           .ToListAsync();
            
            return result;
        }

        public async Task<PageModel<EquipmentSalesContainerModel>> GetPageList(EquipmentSalesContainerRequestModel request)
        {
            PageModel<EquipmentSalesContainerModel> result = new PageModel<EquipmentSalesContainerModel>();
            RefAsync<int> dataCount = 0;
            var data = await _dal.Db.Queryable<EquipmentEntity, EquipmentSalesContainerEntity, SalescontainerEntity>((e, esc, sc)
             => new object[]
             {
                    JoinType.Inner, e.ID == esc.EquipmentId,
                    JoinType.Inner, esc.SalesContainerId == sc.ID
             })
              .Select((e, esc, sc) => new EquipmentSalesContainerModel
              {
                  ID = esc.ID,
                  CreateDate = esc.CreateDate,
                  CreateUserId = esc.CreateUserId,
                  ModifyDate = esc.ModifyDate,
                  ModifyUserId = esc.ModifyUserId,
                  EquipmentId = e.ID,
                  EquipmentCode = e.EquipmentCode,
                  EquipmentName = e.EquipmentName,
                  SalesContainerId = sc.ID,
                  SalesContainerCode = sc.SalesContainer,
                  SalesContainerName = sc.Description
              })
            .MergeTable()
            .WhereIF(!string.IsNullOrEmpty(request.EquipmentId), a => a.EquipmentId == request.EquipmentId)
            .WhereIF(!string.IsNullOrEmpty(request.Key), a =>
                 (a.EquipmentName != null && a.EquipmentName.Contains(request.Key))
              || (a.EquipmentCode != null && a.EquipmentCode.Contains(request.Key))
              || (a.SalesContainerCode != null && a.SalesContainerCode.Contains(request.Key))
              || (a.SalesContainerName != null && a.SalesContainerName.Contains(request.Key)))
            .ToPageListAsync(request.pageIndex, request.pageSize, dataCount);

            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        public async Task<bool> SaveForm(EquipmentSalesContainerEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }


        public async Task<ResultString> ImportData ([FromForm] FileImportDto input) {
            ResultString result = new ResultString();
            var importer = new ExcelImporter();
            var stream = input.File.OpenReadStream();
            var import = await importer.Import<EquipmentSalesContainerExcelDto>(stream);


            if (import.Data.Count() < 1)
            {
                result.AddError("表格中没有效数据");
                return result;
            }
            // 返回 导入异常信息
            if (import.Exception != null)
            {
                result.AddError(import.Exception);
                return result;
            }

            var excelData = import.Data.Where(x => x.EquipmentCode != null && x.SalesContainerCode != null).ToList();
            if (excelData.Count() < 1)
            {
                result.AddError("表格中无有效数据");
                return result;
            }

            var scList = await _salescontainerDal.FindList(a => a.Deleted == 0);
            if (scList.Count <= 0)
            {
                result.AddError($"未维护SalesContainer数据");
                return result;
            }
            var lineResponse = await _dFMServices.GetEquipmentList("Line");

            if (lineResponse == null || lineResponse.success ==false || lineResponse.response == null || lineResponse.response.Count <= 0)
            {
                result.AddError($"未找到产线数据");
                return result;
            }
            var LineList = lineResponse.response;

            var allData = await this.FindList(m => m.Deleted == 0);
            var addList = new List<EquipmentSalesContainerEntity>();
            for (int i = 0; i < excelData.Count; i++)
            {
                var item = excelData[i];

                var equipmentEnity = LineList.Where(x => x.EquipmentCode == item.EquipmentCode).FirstOrDefault();
                if (equipmentEnity == null)
                {
                    result.AddError($"第[{i + 1}]行未找到产线[{item.EquipmentCode}]");
                    return result;
                }

                var salesContainerEntity = scList.Where(a=>a.SalesContainer == item.SalesContainerCode).FirstOrDefault();
                if (salesContainerEntity == null)
                {
                    result.AddError($"第[{i + 1}]行未找到SalesContainer[{item.SalesContainerCode}]");
                    return result;
                }
                var entity = allData.Where(x => x.EquipmentId == equipmentEnity.ID && x.SalesContainerId == salesContainerEntity.ID).FirstOrDefault();
                if (entity == null)
                {
                    entity = new EquipmentSalesContainerEntity();
                    entity.EquipmentId = equipmentEnity.ID;
                    entity.SalesContainerId = salesContainerEntity.ID;
                    entity.CreateCustomGuid(_user.Name);
                    addList.Add(entity);
                }
            }
            try
            {
                if (addList.Any())
                {
                    await this.Add(addList);
                }
            }
            catch (System.Exception ex)
            {
                result.AddError(ex.Message);
            }
            result.Data += $"导入{addList.Count()}条数据";
            return result;

        }



    }
}