
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;

namespace SEFA.PPM.Services
{
    public class ChangemodelServices : BaseServices<ChangemodelEntity>, IChangemodelServices
    {
        private readonly IBaseRepository<ChangemodelEntity> _dal;
        public ChangemodelServices(IBaseRepository<ChangemodelEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<ChangemodelEntity>> GetList(ChangemodelRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<ChangemodelEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<ChangemodelEntity>> GetPageList(ChangemodelRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<ChangemodelEntity>()
                             .AndIF(!string.IsNullOrEmpty(reqModel.LineName)
                                   , p => p.LineName != null && p.LineName.Contains(reqModel.LineName))
                             .AndIF(!string.IsNullOrEmpty(reqModel.ChangeModel),p => p.ChangeModel == reqModel.ChangeModel)
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

        public async Task<bool> SaveForm(ChangemodelEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}