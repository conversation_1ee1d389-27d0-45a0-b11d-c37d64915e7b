
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.Base.Common.HttpContextUser;
using System;
using System.Linq;

namespace SEFA.PPM.Services
{
    public class PlanOrderServices : BaseServices<PlanOrderEntity>, IPlanOrderServices
    {
        private readonly IBaseRepository<PlanOrderEntity> _dal;
        private readonly IBaseRepository<SalescontainerEntity> _salesContainerdal;
        private readonly IBaseRepository<OrderrealtionEntity> _orderRealDal;
        private readonly IBaseRepository<SappackorderEntity> _sapPackOrderDal;
        private readonly IBaseRepository<SapprodversionEntity> _sapprodversionDal;
        public IUser _user;
        public PlanOrderServices(IBaseRepository<PlanOrderEntity> dal,
            IBaseRepository<SalescontainerEntity> salesContainerdal,
            IBaseRepository<OrderrealtionEntity> orderRealDal,
            IBaseRepository<SappackorderEntity> sapPackOrderDal,
            IBaseRepository<SapprodversionEntity> sapprodversionDal,
        IUser user)
        {
            this._dal = dal;
            _user = user;
            _salesContainerdal = salesContainerdal;
            _orderRealDal = orderRealDal;
            _sapPackOrderDal = sapPackOrderDal;
            _sapprodversionDal = sapprodversionDal;
            base.BaseDal = dal;
        }

        public async Task<List<PlanOrderEntity>> GetList(PlanOrderRequestModel reqModel)
        {
            var lineCodeList = new List<string>();
            if (!string.IsNullOrEmpty(reqModel.LineId))
            {
                lineCodeList = reqModel.LineId?.Split(new char[] { ',', ' ', ';', '，' }).ToList();
            }
            var SapFormulaList = new List<string>();
            if (!string.IsNullOrEmpty(reqModel.SapFormula))
            {
                SapFormulaList = reqModel.MatId?.Split(new char[] { ',', ' ', ';', '，' }).ToList();
            }
            var whereExpression = Expressionable.Create<PlanOrderEntity>()
                             .And(p => p.Deleted == 0)
                             .AndIF(SapFormulaList.Count > 0, p => SapFormulaList.Contains(p.SapFormula))
                             .AndIF(lineCodeList.Count > 0, p => lineCodeList.Contains(p.LineId))
                             .AndIF(!string.IsNullOrEmpty(reqModel.LineName), p => p.LineName.Contains(reqModel.LineName))
                             .AndIF(!string.IsNullOrEmpty(reqModel.MatCode), p => p.MatCode.Contains(reqModel.MatCode)
                                || p.MatName.Contains(reqModel.MatCode))
                             .AndIF(!string.IsNullOrEmpty(reqModel.MatId), p => p.SapFormula.Contains(reqModel.MatCode))
                             .AndIF(!string.IsNullOrEmpty(reqModel.PlanOrderNo), p => p.SapOrderNo != null && p.SapOrderNo.Contains(reqModel.PlanOrderNo))
                             .AndIF(reqModel.SapFlagList != null && reqModel.SapFlagList.Count > 0, p => reqModel.SapFlagList.Contains(p.SapFlag))
                             .AndIF(reqModel.ProduceDate.HasValue, p => p.ProduceDate == reqModel.ProduceDate.Value)
                             .AndIF(reqModel.startTime.HasValue, p => p.ProduceDate >= reqModel.startTime.Value)
                             .AndIF(reqModel.endTime.HasValue, p => p.ProduceDate <= reqModel.endTime.Value)
                             .ToExpression();
            var data = await _dal.FindList(whereExpression, p => p.ProduceDate);
            foreach (var item in data)
            {
                var x = await _salesContainerdal.FindEntity(p => p.SalesContainer == item.SapContainer);
                item.SapContainer = x != null ? x.Description : item.SapContainer;
            }
            return data;
        }

        public async Task<PageModel<PlanOrderEntity>> GetPageList(PlanOrderRequestModel reqModel)
        {
            var lineCodeList = new List<string>();
            if (!string.IsNullOrEmpty(reqModel.LineId))
            {
                lineCodeList = reqModel.LineId?.Split(new char[] { ',', ' ', ';', '，' }).ToList();
            }
            var SapFormulaList = new List<string>();
            if (!string.IsNullOrEmpty(reqModel.SapFormula))
            {
                SapFormulaList = reqModel.MatId?.Split(new char[] { ',', ' ', ';', '，' }).ToList();
            }
            var whereExpression = Expressionable.Create<PlanOrderEntity>()
                             .And(p => p.Deleted == 0 || (p.SapFlag == 4  || p.SapFlag == 2))
                             .AndIF(SapFormulaList.Count > 0, p => SapFormulaList.Contains(p.SapFormula))
                             .AndIF(lineCodeList.Count > 0, p => lineCodeList.Contains(p.LineId))
                             .AndIF(!string.IsNullOrEmpty(reqModel.LineName), p => p.LineName.Contains(reqModel.LineName))
                             .AndIF(!string.IsNullOrEmpty(reqModel.MatCode), p => p.MatCode.Contains(reqModel.MatCode)
                                || p.MatName.Contains(reqModel.MatCode))
                             .AndIF(!string.IsNullOrEmpty(reqModel.MatId), p => p.SapFormula.Contains(reqModel.MatCode))
                             .AndIF(!string.IsNullOrEmpty(reqModel.PlanOrderNo), p => p.SapOrderNo != null && p.SapOrderNo.Contains(reqModel.PlanOrderNo))
                             .AndIF(reqModel.SapFlagList != null && reqModel.SapFlagList.Count > 0, p => reqModel.SapFlagList.Contains(p.SapFlag))
                             .AndIF(reqModel.ProduceDate.HasValue, p => p.ProduceDate == reqModel.ProduceDate.Value)
                             .AndIF(reqModel.startTime.HasValue, p => p.ProduceDate >= reqModel.startTime.Value)
                             .AndIF(reqModel.endTime.HasValue, p => p.ProduceDate <= reqModel.endTime.Value)
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize, " PRODUCE_DATE asc , LINE_id asc, SapFormula asc");
            foreach (var item in data.data)
            {
                var ol = await _orderRealDal.FindList(p => p.CookorderId == item.ID);
                if (ol.Count > 0)
                {
                    var sid = ol.Select(p => p.FillorderId).ToList();
                    var sorder = await _sapPackOrderDal.FindList(p => sid.Contains(p.ID));

                    if (sorder.Count() > 0)
                    {
                        item.FillLine = sorder[0].Arbpl;
                        var gp = sorder.GroupBy(p => p.Bezei);
                        string txt = "";
                        foreach (var g in gp)
                        {
                            var container = g.Key;
                            var qty = Convert.ToInt32(g.Sum(p => p.PsmngComp));
                            txt = txt + $"{container}: {qty} kg,";
                        }
                        item.SapContainer = txt.TrimEnd(',');
                    }
                    else
                    {
                        var x = await _salesContainerdal.FindEntity(p => p.SalesContainer == item.SapContainer);
                        item.SapContainer = x != null ? x.Description : item.SapContainer;
                    }
                }
                var pv = await _sapprodversionDal.FindList(p => p.Matnr == item.MatCode && p.Verid == item.ProdVersion && p.Text1.StartsWith("P3"));
                if (pv.Count() > 0)
                {
                    item.ProdVersionText = pv[0].Text1;
                }
                else
                {
                    item.ProdVersionText = item.ProdVersion;
                }
            }

            return data;
        }

        public async Task<bool> SaveForm(PlanOrderEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                entity.Create(_user.Name.ToString());
                return await this.Add(entity) > 0;
            }
            else
            {
                entity.Modify(entity.ID, _user.Name.ToString());
                return await this.Update(entity);
            }
        }
    }
}