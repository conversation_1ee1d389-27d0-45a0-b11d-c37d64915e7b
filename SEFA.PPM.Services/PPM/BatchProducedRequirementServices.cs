
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.PPM.Services
{
    public class BatchProducedRequirementServices : BaseServices<BatchProducedRequirementEntity>, IBatchProducedRequirementServices
    {
        private readonly IBaseRepository<BatchProducedRequirementEntity> _dal;
        public BatchProducedRequirementServices(IBaseRepository<BatchProducedRequirementEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<BatchProducedRequirementEntity>> GetList(BatchProducedRequirementRequestModel reqModel)
        {
            List<BatchProducedRequirementEntity> result = new List<BatchProducedRequirementEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BatchProducedRequirementEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<BatchProducedRequirementEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        public async Task<PageModel<BatchProducedRequirementEntity>> GetPageList(BatchProducedRequirementRequestModel reqModel)
        {
            PageModel<BatchProducedRequirementEntity> result = new PageModel<BatchProducedRequirementEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BatchProducedRequirementEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<BatchProducedRequirementEntity>()
                .Where(whereExpression)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        public async Task<bool> SaveForm(BatchProducedRequirementEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}