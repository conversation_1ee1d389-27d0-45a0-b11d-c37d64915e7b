using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SEFA.Base.Services.BASE;
using SEFA.MKM.Model.Models;
//using SEFA.DFM.Model.Models;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SqlSugar;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SEFA.PPM.Model.Models.MKM;

namespace SEFA.PPM.Services
{
    public class StandardPeriodLotServices : BaseServices<StandardPeriodLotEntity>, IStandardPeriodLotServices
    {
        private readonly IBaseRepository<StandardPeriodLotEntity> _dal;
        private readonly IBaseRepository<EquipmentEntity> _equipmentDal;

        public StandardPeriodLotServices(IBaseRepository<StandardPeriodLotEntity> dal, IBaseRepository<EquipmentEntity> equipmentDal)
        {
            this._dal = dal;
            base.BaseDal = dal;
            this._equipmentDal = equipmentDal;
        }

        public async Task<List<StandardPeriodLotEntity>> GetList(StandardPeriodLotRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<StandardPeriodLotEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<StandardPeriodLotEntity>> GetPageList(StandardPeriodLotRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<StandardPeriodLotEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);

            return data;
        }

        /// <summary>
        /// 根据父级代码获取特定类型的子节点
        /// </summary>
        /// <param name="areaCode">车间代码</param>
        /// <param name="level">子类型</param>
        /// <returns></returns>
        public async Task<List<EquipmentEntity>> GetLine(string areaCode, string level)
        {
            var whereExpression = Expressionable.Create<EquipmentEntity>()
                .AndIF(!string.IsNullOrEmpty(areaCode), p => p.EquipmentName.Contains(areaCode))
                .And(p => p.Level == "Area")
                .And(p => p.Deleted == 0)
                .ToExpression();

            var fatherList = await _equipmentDal.FindList(whereExpression);
            if (fatherList == null || fatherList.Count == 0)
            {
                return new List<EquipmentEntity>();
            }

            var lineWhereExpression = Expressionable.Create<EquipmentEntity>()
                .And(p => fatherList.Select(m => m.ID).Contains(p.ParentId))
                .AndIF(!string.IsNullOrEmpty(level), p => p.Level == level)
                .And(p => p.Deleted == 0)
                .ToExpression();
            var lineList = await _equipmentDal.FindList(lineWhereExpression);

            return lineList.OrderBy(a => a.EquipmentName).ThenBy(a => a.SortNumber).ToList();
        }

        public async Task<bool> SaveForm(StandardPeriodLotEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}