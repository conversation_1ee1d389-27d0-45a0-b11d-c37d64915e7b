
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base.Common.Common;
using SEFA.DFM.Model.ViewModels;
using System;
using SEFA.Base.Common.HttpContextUser;
using SEFA.DFM.Model.Models;
using SEFA.Base.IRepository.UnitOfWork;
using System.Reactive;
using System.Linq;
using static SEFA.PTM.Services.ConsumeViewServices;
using SEFA.PPM.Model.ViewModels.SIM.View;

namespace SEFA.PPM.Services
{
    public class DowntimetgtServices : BaseServices<DowntimetgtEntity>, IDowntimetgtServices
    {
        private readonly IBaseRepository<DowntimetgtEntity> _dal;
        private readonly IBaseRepository<EquipmentEntity> _eq;
        private readonly IKpitgtServices _kpitgtServices;
        private readonly IUser _user;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IBaseRepository<MaterialGroupEntity> _mterGroup;
        private readonly IBaseRepository<DFM.Model.Models.UnitmanageEntity> _unit;
        private readonly IBaseRepository<DowntimetgtViewEntity> _DowntimetgtViewEntity;
        public DowntimetgtServices(IBaseRepository<DowntimetgtEntity> dal, IKpitgtServices kpitgtServices, IUser user, IBaseRepository<EquipmentEntity> eq, IUnitOfWork unitOfWork, IBaseRepository<DFM.Model.Models.UnitmanageEntity> unit, IBaseRepository<DowntimetgtViewEntity> downtimetgtViewEntity)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _kpitgtServices = kpitgtServices;
            _user = user;
            _eq = eq;
            _unitOfWork = unitOfWork;
            _unit = unit;
            _DowntimetgtViewEntity = downtimetgtViewEntity;
        }

        public async Task<List<DowntimetgtEntity>> GetList(DowntimetgtRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<DowntimetgtEntity>()
                                   .AndIF(reqModel.Year != null, p => p.Year == reqModel.Year)
                             .AndIF(reqModel.Month != null, p => p.Month == reqModel.Month)
                             .AndIF(!string.IsNullOrEmpty(reqModel.LineId), p => p.LineId.Equals(reqModel.LineId))
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            var whereUnitExpression = Expressionable.Create<DFM.Model.Models.UnitmanageEntity>()
          .ToExpression();
            var unitData = await _unit.FindList(whereUnitExpression);
            foreach (var item in data)
            {
                var unitModel = unitData.Where(p => p.ID.Equals(item.Unit)).FirstOrDefault();
                if (unitModel != null)
                {
                    item.Unit = unitModel.Name;
                }
                else
                {
                    item.Unit = null;
                }

            }
            return data;
        }
        public async Task<List<DowntimetgtViewEntity>> ExportData(DowntimetgtRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<DowntimetgtViewEntity>()
                             .AndIF(reqModel.Year != null, p => p.Year == reqModel.Year)
                             .AndIF(!string.IsNullOrEmpty(reqModel.LineId), p => p.LineId.Equals(reqModel.LineId))
                             .ToExpression();
            var data = await _DowntimetgtViewEntity.FindList(whereExpression);
            var results = (from a in data
                           select new DowntimetgtModel
                           {
                               LineName = a.LineName,
                               //ModelRefName = a.ModelRefName,
                               Year = a.Year,
                               Tgt = a.Tgt,
                               UnitName = a.UnitName

                           }).ToList();
            return data;
        }

        public async Task<PageModel<DowntimetgtViewEntity>> GetPageList(DowntimetgtViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<DowntimetgtViewEntity>()
                             .AndIF(!string.IsNullOrEmpty(reqModel.Year), p => p.Year ==Convert.ToInt32(reqModel.Year))
                            // .AndIF(!string.IsNullOrEmpty(reqModel.Month ), p => p.Month == Convert.ToInt32(reqModel.Month))
                             .AndIF(!string.IsNullOrEmpty(reqModel.LineId), p => p.LineId.Equals(reqModel.LineId))
                             .ToExpression();
            var data = await _DowntimetgtViewEntity.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
            return data;
        }

/*        public async Task<bool> SaveForm(DowntimetgtEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }*/
        public async Task<MessageModel<string>> SaveForm(DowntimetgtEntity entity)
        {
            var data = new MessageModel<string>();
            data.success = false;
            data.msg = "添加成功";
            var lineDate = await _eq.FindEntity(entity.ModelRef);
            if (lineDate == null)
            {
                data.msg = "未找到模型";
                return data;
            }
            var unit = await _unit.FindEntity(entity.Unit);
            if (unit == null)
            {
                data.msg = "单位不存在";
                return data;
            }
            if (lineDate.Level == "Segment" || lineDate.Level == "Line")
            {
                if (lineDate.Level == "Segment")
                {
                    entity.LineId = lineDate.LineId;
                }
                else
                {
                    entity.LineId = lineDate.ID;
                }
            }
            else
            {
                data.msg = "物料模型需要选择产线或工作中心";
                return data;
            }
            if (string.IsNullOrEmpty(entity.ID))
            {
                entity.Create(_user.Name.ToString());
                bool prodTgtContainer = await _dal.Add(entity) > 0;
                if (!prodTgtContainer)
                {
                    _unitOfWork.RollbackTran();

                    return data;
                }
                else
                {
                    _unitOfWork.CommitTran();
                    data.success = true;
                    return data;
                }
            }
            else
            {
                entity.Modify(entity.ID, _user.Name.ToString());
                data.success = await _dal.Update(entity);
                if (data.success)
                {
                    data.success = true;
                    data.msg = "更新成功";
                    return data;
                }
                else
                {
                    data.msg = "更新失败";
                    return data;
                }
            }
        }
        public async Task<ResultString> ImportData([FromForm] FileImportDto input)
        {

            ResultString result = new ResultString();
            try
            {
                _unitOfWork.BeginTran();
                var stream = input.File.OpenReadStream();
                // 检查文件是否存在
                if (stream == null)
                {
                    result.AddError("未找到文件,请重新上传");
                    return result;
                }
                //获取表格数据
                var fileData = await _kpitgtServices.ReadExcel(stream);
                if (fileData.Rows.Count < 1)
                {
                    result.AddError("表格中没有效数据");
                    return result;
                }
                List<DowntimetgtEntity> excelDataList = new List<DowntimetgtEntity>();
                List<DowntimetgtEntity> upDataList = new List<DowntimetgtEntity>();
                List<DowntimetgtEntity> insertDataList = new List<DowntimetgtEntity>();
                var whereDowntimeExpression = Expressionable.Create<DowntimetgtEntity>()
                                     .ToExpression();
                var downtimeDataList = await _dal.FindList(whereDowntimeExpression);
                var whereUnitExpression = Expressionable.Create<DFM.Model.Models.UnitmanageEntity>()
                                                        .ToExpression();
                var unitDataList = await _unit.FindList(whereUnitExpression);
                var whereExpression = Expressionable.Create<EquipmentEntity>()
                      .ToExpression();
                var eqDataList = await _eq.FindList(whereExpression);
                string year = string.Empty;
                string unit = string.Empty;
                for (int i = 0; i < fileData.Rows.Count; i++)
                {
                    if (i == 0)
                    {
                        //获取年份
                        year = fileData.Rows[i]["Column2"] == null ? "" : fileData.Rows[i]["Column2"].ToString();
                        if (string.IsNullOrEmpty(year))
                        {
                            result.AddError("表格中年份不能为空");
                            return result;
                        }
                        //获取年份
                        unit = fileData.Rows[i]["Column4"] == null ? "" : fileData.Rows[i]["Column4"].ToString();
                        if (string.IsNullOrEmpty(unit))
                        {
                            result.AddError("表格中单位不能为空");
                            return result;
                        }
                    }
                    else if (i == 1)
                    {
                        continue;
                    }
                    else
                    {
                        var line = fileData.Rows[i]["Column1"] == null ? "" : fileData.Rows[i]["Column1"].ToString();
                        //判断空
                        if (string.IsNullOrEmpty(line))
                        {
                            result.AddError(string.Format(@"第'{0}'行，产线列为空，导入失败", i + 1));
                            return result;
                        }
                        var modelRef = fileData.Rows[i]["Column2"] == null ? "" : fileData.Rows[i]["Column2"].ToString();
                        //判断空
                        if (string.IsNullOrEmpty(modelRef))
                        {
                            result.AddError(string.Format(@"第'{0}'行，物理模型列为空，导入失败", i + 1));
                            return result;
                        }
                        else
                        {
                            var eqData = eqDataList.Where(p => p.EquipmentName.Equals(modelRef)).FirstOrDefault();
                            if (eqData == null)
                            {
                                result.AddError(string.Format(@"第'{0}'行，物理模型列未匹配到模型数据，导入失败", i + 1));
                                return result;
                            }
                        }
                        var tgt = fileData.Rows[i]["Column3"] == null ? "" : fileData.Rows[i]["Column3"].ToString();
                        //判断空
                        if (string.IsNullOrEmpty(tgt))
                        {
                            continue;
                        }
                        DowntimetgtEntity entity = new DowntimetgtEntity();
                        entity.CreateCustomGuid(_user.Name);
                        entity.LineId = line;
                        entity.ModelRef = modelRef;
                        entity.Year = Convert.ToInt32(year);
                        entity.Tgt = Convert.ToDecimal(tgt);
                        entity.Unit = unit;
                        excelDataList.Add(entity);
                    }

                }
                //检查是否存在相同的数据如果有则更新，反之则插入
                foreach (var item in excelDataList)
                {
                    //检查输入的单位是否能在维护的表中找到，没有则新增
                    var unitData = unitDataList.Where(p => p.Name.Equals(item.Unit)).FirstOrDefault();
                    if (unitData != null)
                    {
                        item.Unit = unitData.ID;
                    }
                    else
                    {
                        DFM.Model.Models.UnitmanageEntity unitmanageEntity = new DFM.Model.Models.UnitmanageEntity();
                        unitmanageEntity.Name = item.Unit;
                        unitmanageEntity.Enable = 1;
                        unitmanageEntity.Deleted = 0;
                        var unitAdd = _unit.Add(unitmanageEntity);
                        item.Unit = unitmanageEntity.ID;
                    }
                    var oldData = downtimeDataList.Where(p => p.LineId.Equals(item.LineId) &&
                                                         p.Year == item.Year && p.Unit.Equals(item.Unit) &&
                                                         p.ModelRef.Equals(item.ModelRef)
                                                         ).FirstOrDefault();
                    if (oldData != null)
                    {
                        oldData.Tgt = item.Tgt;
                        upDataList.Add(oldData);
                    }
                    else
                    {
                        insertDataList.Add(item);
                    }
                }
                if (insertDataList != null && insertDataList.Count > 0)
                {
                    if (await _dal.Add(insertDataList) <= 0)
                    {
                        _unitOfWork.RollbackTran();
                        result.AddError("插入失败");
                        return result;
                    }
                }
                if (upDataList != null && upDataList.Count > 0)
                {
                    if (!await _dal.Update(upDataList))
                    {
                        _unitOfWork.RollbackTran();
                        result.AddError("更新失败");
                        return result;
                    }
                }
                _unitOfWork.CommitTran();
                result.Succeed = true;
                result.Data = string.Format("导入成功，共插入{0}条数据，更新{1}条数据", insertDataList.Count, upDataList.Count);
                return result;
            }
            catch (Exception e)
            {
                _unitOfWork.RollbackTran();
                result.AddError(e.StackTrace.ToString());
                return result;
            }
        }
    }
}