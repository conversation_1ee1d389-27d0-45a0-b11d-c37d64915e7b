
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using Magicodes.ExporterAndImporter.Excel;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base.Common.Common;
using SEFA.Base.Common.HttpContextUser;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using System.Linq;
using static SEFA.PTM.Services.ConsumeViewServices;
using System.Data;
using System.IO;
using System;
using MiniExcelLibs;
using SharpCompress.Common;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Logical;
using SEFA.PPM.Model.Models.Interface;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.Base.Repository.Base;
using SEFA.PPM.Model.Models.PTM;
using System.Text.RegularExpressions;
using static MongoDB.Libmongocrypt.CryptContext;
using System.Reactive;
using SEFA.PPM.Model.ViewModels.MKM.View;
using static SEFA.PPM.Services.KpitgtServices;
using System.Reflection;
using NetTaste;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using static SEFA.PPM.Services.OeeReportViewServices;
using SQLitePCL;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using SEFA.Base.Common.LogHelper;
using SEFA.PPM.Model.ViewModels.SIM.View;
using SEFA.PPM.Model.ViewModels.SIM;
using StackExchange.Profiling.Internal;
using SEFA.PPM.Model.Models.SIM;
using SEFA.Base.Common.WebApiClients.HttpApis;
using System.Xml.Linq;
using System.Globalization;
using static System.Collections.Specialized.BitVector32;
using SEFA.MKM.Model.Models;
using SEFA.PPM.Services.Helper;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using System.Collections;
using NodaTime;
using EquipmentEntity = SEFA.DFM.Model.Models.EquipmentEntity;
using static SEFA.PPM.Services.InterfaceServices;
using Abp.Linq.Expressions;
using Remotion.Linq.Clauses;
using BatchEntity = SEFA.PPM.Model.Models.BatchEntity;
using Microsoft.VisualBasic;
using SEFA.PPM.Model.Models.MKM;

namespace SEFA.PPM.Services
{
    public partial class KpitgtServices : BaseServices<KpitgtEntity>, IKpitgtServices
    {
        private readonly IBaseRepository<LosstgtEntity> _dalLosstgtEntity;
        private readonly IBaseRepository<PoProducedExecutionEntity> _dalPoProducedExecutionEntity;
        private readonly IBaseRepository<ProductionOrderEntity> _dalProductionOrderEntity;
        private readonly IBaseRepository<PoConsumeMaterialListViewEntity> _dalPoConsumeMaterialListViewEntity;
        private readonly IBaseRepository<ImtableRowmatLossEntity> _dalImtableRowmatLossEntity;
        private readonly IBaseRepository<DFM.Model.Models.MaterialEntity> _dalMaterialEntity;
        private readonly IBaseRepository<HistoryViewEntity> _dalHistoryViewEntityModel;
        private readonly IBaseRepository<ImtablePakmatLossEntity> _dalImtablePakmatLossEntity;

        private readonly IBaseRepository<Model.Models.BatchEntity> _BatchEntity;
        private readonly IBaseRepository<ProductionOrderEntity> _ProductionOrderEntity;
        private readonly IBaseRepository<PoProducedExecutionEntity> _PoProducedExecutionEntity;
        private readonly IBaseRepository<LogsheetEntity> _LogsheetEntity;
        private readonly IBaseRepository<LogsheetDetailEntity> _LogsheetDetailServices;

        private readonly IBaseRepository<DowntimeGroupEntity> _DowntimeGroupEntity;
        private readonly IBaseRepository<DowntimeReasonEntity> _DowntimeReasonEntity;
        private readonly IBaseRepository<DowntimeEntity> _DowntimeEntity;
        private readonly IBaseRepository<LinerelationEntity> _LinerelationEntityDal;

        private readonly IBaseRepository<VerifiyDetailEntity> _dalVerifiyDetailEntity;
        private readonly IBaseRepository<KpitgtEntity> _dal;
        private readonly IBaseRepository<DFM.Model.Models.EquipmentEntity> _eq;
        private readonly IUser _user;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IBaseRepository<DataItemDetailEntity> _dataItem;
        private readonly IBaseRepository<DFM.Model.Models.UnitmanageEntity> _unit;
        private readonly IBaseRepository<DFM.Model.Models.EquipmentEntity> _EquipmentEntity;
        private readonly IBaseRepository<KpiTgtViewEntity> _KpiTgtViewEntity;
        private readonly IBaseRepository<InfluxOpcTagEntity> _InfluxOpcTagEntit;
        private readonly IInfluxDbServices _IInfluxDbServices;
        private readonly IBaseRepository<SafetytgtViewEntity> _SafetytgtViewEntity;

        private readonly IBaseRepository<ProducedActualViewEntity> _ProducedActualViewEntity;
        private readonly IBaseRepository<ConsumeActualViewEntity> _ConsumeActualViewEntity;
        private readonly IBaseRepository<PoProducedActualEntity> _PoProducedActualEntity;
        private readonly IBaseRepository<PoConsumeActualEntity> _PoConsumeActualEntity;
        private readonly IBaseRepository<EnergyByorderEntity> _EnergyByorderEntity;

        private readonly IBaseRepository<OeeReportViewEntity> _OeeReportViewEntity;
        private readonly IBaseRepository<PerformanceEntity> _PerformanceEntity;

        private readonly IBaseRepository<ConfirmationEntity> _ConfirmationEntity;


        public KpitgtServices(IBaseRepository<KpitgtEntity> dal, IBaseRepository<DFM.Model.Models.EquipmentEntity> eq, IUser user, IUnitOfWork unitOfWork, IBaseRepository<DataItemDetailEntity> dataItem, IBaseRepository<DFM.Model.Models.UnitmanageEntity> unit, IBaseRepository<DFM.Model.Models.EquipmentEntity> EquipmentEntity, IBaseRepository<KpiTgtViewEntity> kpiTgtViewEntity, IBaseRepository<LosstgtEntity> dalLosstgtEntity, IBaseRepository<PoProducedExecutionEntity> dalPoProducedExecutionEntity, IBaseRepository<ProductionOrderEntity> dalProductionOrderEntity, IBaseRepository<ImtableRowmatLossEntity> dalImtableRowmatLossEntity, IBaseRepository<DFM.Model.Models.MaterialEntity> dalMaterialEntity, IBaseRepository<HistoryViewEntity> dalHistoryViewEntityModel, IBaseRepository<ImtablePakmatLossEntity> dalImtablePakmatLossEntity, IBaseRepository<VerifiyDetailEntity> dalVerifiyDetailEntity, IBaseRepository<InfluxOpcTagEntity> influxOpcTagEntit, IInfluxDbServices iInfluxDbServices, IBaseRepository<Model.Models.BatchEntity> batchEntity, IBaseRepository<ProductionOrderEntity> productionOrderEntity, IBaseRepository<PoProducedExecutionEntity> poProducedExecutionEntity, IBaseRepository<LogsheetEntity> logsheetEntity, IBaseRepository<LogsheetDetailEntity> logsheetDetailServices, IBaseRepository<DowntimeGroupEntity> downtimeGroupEntity, IBaseRepository<DowntimeReasonEntity> downtimeReasonEntity, IBaseRepository<DowntimeEntity> downtimeEntity, IBaseRepository<SafetytgtViewEntity> safetytgtViewEntity, IBaseRepository<ProducedActualViewEntity> producedActualViewEntity, IBaseRepository<ConsumeActualViewEntity> consumeActualViewEntity, IBaseRepository<PoProducedActualEntity> poProducedActualEntity, IBaseRepository<PoConsumeActualEntity> poConsumeActualEntity, IBaseRepository<EnergyByorderEntity> energyByorderEntity, IBaseRepository<OeeReportViewEntity> OeeReportViewEntity, IBaseRepository<PerformanceEntity> performanceEntity, IBaseRepository<ConfirmationEntity> confirmationEntity = null, IBaseRepository<LinerelationEntity> linerelationEntityDal = null)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _eq = eq;
            _user = user;
            _unitOfWork = unitOfWork;
            _dataItem = dataItem;
            _unit = unit;
            _EquipmentEntity = EquipmentEntity;
            _KpiTgtViewEntity = kpiTgtViewEntity;
            _dalLosstgtEntity = dalLosstgtEntity;
            _dalPoProducedExecutionEntity = dalPoProducedExecutionEntity;
            _dalProductionOrderEntity = dalProductionOrderEntity;
            _dalImtableRowmatLossEntity = dalImtableRowmatLossEntity;
            _dalMaterialEntity = dalMaterialEntity;
            _dalHistoryViewEntityModel = dalHistoryViewEntityModel;
            _dalImtablePakmatLossEntity = dalImtablePakmatLossEntity;
            _dalVerifiyDetailEntity = dalVerifiyDetailEntity;
            _InfluxOpcTagEntit = influxOpcTagEntit;
            _IInfluxDbServices = iInfluxDbServices;
            _BatchEntity = batchEntity;
            _ProductionOrderEntity = productionOrderEntity;
            _PoProducedExecutionEntity = poProducedExecutionEntity;
            _LogsheetEntity = logsheetEntity;
            _LogsheetDetailServices = logsheetDetailServices;
            _DowntimeGroupEntity = downtimeGroupEntity;
            _DowntimeReasonEntity = downtimeReasonEntity;
            _DowntimeEntity = downtimeEntity;
            _SafetytgtViewEntity = safetytgtViewEntity;
            _ProducedActualViewEntity = producedActualViewEntity;
            _ConsumeActualViewEntity = consumeActualViewEntity;
            _PoProducedActualEntity = poProducedActualEntity;
            _PoConsumeActualEntity = poConsumeActualEntity;
            _EnergyByorderEntity = energyByorderEntity;
            _OeeReportViewEntity = OeeReportViewEntity;
            _PerformanceEntity = performanceEntity;
            _ConfirmationEntity = confirmationEntity;
            _LinerelationEntityDal = linerelationEntityDal;
        }
        public async Task<List<KpiTgtViewModel>> GetList(KpiTgtViewRequestModel reqModel)
        {
            List<KpiTgtViewModel> kpiTgtViewModels = new List<KpiTgtViewModel>();

            var whereExpression = Expressionable.Create<KpiTgtViewEntity>()
                 .AndIF(!string.IsNullOrEmpty(reqModel.Year), p => p.Year == Convert.ToInt32(reqModel.Year))
                 .AndIF(!string.IsNullOrEmpty(reqModel.Month), p => p.Month == Convert.ToInt32(reqModel.Month))
                 .AndIF(!string.IsNullOrEmpty(reqModel.DataName), p => p.DataName.Equals(reqModel.DataName))
                 .ToExpression();
            var data = await _KpiTgtViewEntity.FindList(whereExpression);

            var datas = data.GroupBy(p => new { dataName = p.DataName, dataType = p.DateType, modelName = p.ModelRefName, year = p.Year, unitName = p.UnitName })
                .Select(p => new { p.Key.dataName, p.Key.dataType, p.Key.modelName, p.Key.year, p.Key.unitName }).ToList();
            for (int i = 0; i < datas.Count; i++)
            {
                KpiTgtViewModel kpiTgtViewModel = new KpiTgtViewModel();
                var str = data.Where(p => p.DataName == datas[i].dataName
                && p.DateType == datas[i].dataType
                && p.ModelRefName == datas[i].modelName
                && p.Year == datas[i].year
                && p.UnitName == datas[i].unitName).ToList();
                List<KPIModel> kPIModels = new List<KPIModel>();
                for (int k = 1; k <= 12; k++)
                {
                    KPIModel kPIModel = new KPIModel();
                    var model = str.Where(p => p.Month == k).FirstOrDefault();
                    var model1 = str.Where(p => p.DataName != null).FirstOrDefault();
                    if (model != null)
                    {
                        kPIModel.DataName = model.DataName;
                        kPIModel.DataType = model.DateType;
                        kPIModel.ModelName = model.ModelRefName;
                        kPIModel.UnitName = model.UnitName;
                        kPIModel.Year = model.Year;
                        kPIModel.Month = k;
                        kPIModel.m = model.Tgt.ToString("0.###");
                    }
                    else
                    {
                        kPIModel.DataName = model1.DataName;
                        kPIModel.DataType = model1.DateType;
                        kPIModel.ModelName = model1.ModelRefName;
                        kPIModel.UnitName = model1.UnitName;
                        kPIModel.Year = model1.Year;
                        kPIModel.Month = k;
                        kPIModel.m = "";
                    }
                    kPIModels.Add(kPIModel);
                }
                var k1 = kPIModels.Where(p => p.Month == 1).FirstOrDefault();
                var k2 = kPIModels.Where(p => p.Month == 2).FirstOrDefault();
                var k3 = kPIModels.Where(p => p.Month == 3).FirstOrDefault();
                var k4 = kPIModels.Where(p => p.Month == 4).FirstOrDefault();
                var k5 = kPIModels.Where(p => p.Month == 5).FirstOrDefault();
                var k6 = kPIModels.Where(p => p.Month == 6).FirstOrDefault();
                var k7 = kPIModels.Where(p => p.Month == 7).FirstOrDefault();
                var k8 = kPIModels.Where(p => p.Month == 8).FirstOrDefault();
                var k9 = kPIModels.Where(p => p.Month == 9).FirstOrDefault();
                var k10 = kPIModels.Where(p => p.Month == 10).FirstOrDefault();
                var k11 = kPIModels.Where(p => p.Month == 11).FirstOrDefault();
                var k12 = kPIModels.Where(p => p.Month == 12).FirstOrDefault();

                kpiTgtViewModel.DataName = k1.DataName;
                kpiTgtViewModel.DataType = k1.DataType;
                kpiTgtViewModel.ModelName = k1.ModelName;
                kpiTgtViewModel.UnitName = k1.UnitName;
                kpiTgtViewModel.Year = k1.Year;
                kpiTgtViewModel.m1 = k1.m;
                kpiTgtViewModel.m2 = k2.m;
                kpiTgtViewModel.m3 = k3.m;
                kpiTgtViewModel.m4 = k4.m;
                kpiTgtViewModel.m5 = k5.m;
                kpiTgtViewModel.m6 = k6.m;
                kpiTgtViewModel.m7 = k7.m;
                kpiTgtViewModel.m8 = k8.m;
                kpiTgtViewModel.m9 = k9.m;
                kpiTgtViewModel.m10 = k10.m;
                kpiTgtViewModel.m11 = k11.m;
                kpiTgtViewModel.m12 = k12.m;
                kpiTgtViewModels.Add(kpiTgtViewModel);
            }

            return kpiTgtViewModels;
        }
        public class KPIModel
        {
            public string DataName { get; set; }
            public string DataType { get; set; }
            public string ModelName { get; set; }
            public string UnitName { get; set; }
            public int Year { get; set; }
            public int Month { get; set; }
            public string m { get; set; }
        }
        public async Task<List<KpitgtEntity>> GetDataListBySearch(KpitgtRequestModel model)
        {
            DateTime startDate;
            DateTime endDate;
            var whereExpression = Expressionable.Create<KpitgtEntity>()
                 .ToExpression();
            var data = await _dal.FindList(whereExpression);
            List<KpitgtEntity> selectData = new List<KpitgtEntity>();
            if (model.StartTime.HasValue && model.EndTime.HasValue)
            {
                if (model.StartTime.Value > model.EndTime.Value)
                {
                    return new List<KpitgtEntity>();
                }
                startDate = Convert.ToDateTime(model.StartTime.Value.ToString("yyyy-MM"));
                endDate = Convert.ToDateTime(model.EndTime.Value.ToString("yyyy-MM"));
                int months = (endDate.Year - startDate.Year) * 12 + endDate.Month - startDate.Month;
                for (int i = 0; i < months; i++)
                {
                    foreach (var item in data)
                    {

                        if (item.Year == startDate.Year && item.Month == startDate.Month)
                        {
                            selectData.Add(item);
                            startDate.AddMonths(1);
                        }
                    }
                }
                var groupData = selectData.GroupBy(p => new { p.DataName, p.DateType, p.ModelRef, p.Unit }).Select(p => new { p.Key.ModelRef, p.Key.DateType, p.Key.DataName, p.Key.Unit, Tgt = p.Average(x => x.Tgt) }).ToList();
                selectData = new List<KpitgtEntity>();
                foreach (var item in groupData)
                {
                    KpitgtEntity resModel = new KpitgtEntity();
                    resModel.DataName = item.DataName;
                    resModel.DateType = item.DateType;
                    resModel.ModelRef = item.ModelRef;
                    resModel.Unit = item.Unit;
                    resModel.Tgt = item.Tgt;
                    selectData.Add(resModel);
                }
                data = selectData;
            }
            //var whereEqxpression = Expressionable.Create<EquipmentEntity>()
            //  .ToExpression();
            //var eqDataList = await _eq.FindList(whereEqxpression);
            //foreach (var item in data)
            //{
            //    switch (item.DataName)
            //    {
            //        case "0":
            //            item.DataName = "自然月总制造费用";
            //            break;
            //        case "1":
            //            item.DataName = "自然月总折旧费用";
            //            break;
            //        case "2":
            //            item.DataName = "自然月总人工费用";
            //            break;
            //        case "3":
            //            item.DataName = "自然月总动力耗用费用";
            //            break;
            //        case "4":
            //            item.DataName = "自然月总维修费用";
            //            break;
            //        case "5":
            //            item.DataName = "自然月总消耗品费用";
            //            break;
            //        case "6":
            //            item.DataName = "月度天然气用量";
            //            break;
            //        case "7":
            //            item.DataName = "月度蒸汽用量";
            //            break;
            //        case "8":
            //            item.DataName = "月度电用量";
            //            break;
            //        case "9":
            //            item.DataName = "月度水用量";
            //            break;
            //    }
            //    switch (item.DateType)
            //    {
            //        case "0":
            //            item.DateType = "预算";
            //            break;
            //        case "1":
            //            item.DateType = "实际";
            //            break;
            //    }
            //    var eq = eqDataList.Where(p => p.ID.Equals(item.ModelRef)).FirstOrDefault();
            //    if (eq != null) 
            //    {
            //        item.ModelRef = eq.EquipmentName;
            //    }
            //}
            return data;
        }

        /// <summary>
        /// KPI指标查询
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<PageModel<KpiTgtViewEntity>> GetPageList(KpiTgtViewRequestModel reqModel)
        {

            var whereExpression = Expressionable.Create<KpiTgtViewEntity>()
                     .AndIF(!string.IsNullOrEmpty(reqModel.Year), p => p.Year == Convert.ToInt32(reqModel.Year))
                     .AndIF(!string.IsNullOrEmpty(reqModel.Month), p => p.Month == Convert.ToInt32(reqModel.Month))
                     .AndIF(!string.IsNullOrEmpty(reqModel.DataName), p => p.DataName.Contains(reqModel.DataName))
                     .ToExpression();
            var data = await _KpiTgtViewEntity.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return data;
        }
        public async Task<List<string>> GetDataName()
        {
            List<string> res = new List<string>();
            var whereExpression = Expressionable.Create<KpitgtEntity>()
                     .ToExpression();
            var data = await _dal.FindList(whereExpression);
            res = data.GroupBy(p => p.DataName).Select(p => p.Key).ToList();
            return res;
        }
        public async Task<List<string>> GetDataType()
        {
            List<string> res = new List<string>();
            var whereExpression = Expressionable.Create<DataItemDetailEntity>()
                     .And(p => p.ItemCode.Equals("KPI DATA TYPE"))
                     .ToExpression();
            var data = await _dataItem.FindList(whereExpression);
            res = data.GroupBy(p => p.ItemName).Select(p => p.Key).ToList();
            return res;
        }
        #region MyRegion

        public class MothModel
        {
            public string m { get; set; }
            public string v { get; set; }
        }
        #endregion
        public async Task<MessageModel<string>> ImportData(IEnumerable<dynamic> data)
        {
            MessageModel<string> result = new MessageModel<string>()
            {
                success = false,
                msg = "导入失败!",
                response = "操作失败！"
            };
            try
            {
                var kpiDataList = await _dal.Query();
                var unitDataList = await _unit.Query();
                var eqDataList = await _eq.Query();

                var upKpitgtEntityList = new List<KpitgtEntity>();
                var addKpitgtEntityList = new List<KpitgtEntity>();
                int rowIndex = 0;
                foreach (IDictionary<string, object> row in data)
                {
                    var Col1 = row["数据名称"]?.ToString();
                    var Col2 = row["数据类型"]?.ToString();
                    var Col3 = row["模型区域"]?.ToString();
                    var Col4 = row["单位"]?.ToString();
                    var Col5 = row["年份"]?.ToString();
                    var Col6 = row["1月"]?.ToString();
                    var Col7 = row["2月"]?.ToString();
                    var Col8 = row["3月"]?.ToString();
                    var Col9 = row["4月"]?.ToString();
                    var Col10 = row["5月"]?.ToString();
                    var Col11 = row["6月"]?.ToString();
                    var Col12 = row["7月"]?.ToString();
                    var Col13 = row["8月"]?.ToString();
                    var Col14 = row["9月"]?.ToString();
                    var Col15 = row["10月"]?.ToString();
                    var Col16 = row["11月"]?.ToString();
                    var Col17 = row["12月"]?.ToString();
                    #region 判断空
                    //判断空
                    if (string.IsNullOrEmpty(Col1))
                    {
                        result.response = string.Format(string.Format(@"第'{0}'行，数据名称为空，导入失败", rowIndex + 1));
                        return result;
                    }
                    if (string.IsNullOrEmpty(Col2))
                    {
                        result.response = (string.Format(@"第'{0}'行，数据类型列为空，导入失败", rowIndex + 1));
                        return result;
                    }
                    if (string.IsNullOrEmpty(Col3))
                    {
                        result.response = (string.Format(@"第'{0}'行，物理模型列为空，导入失败", rowIndex + 1));
                        return result;
                    }
                    if (string.IsNullOrEmpty(Col5))
                    {
                        result.response = (string.Format(@"第'{0}'行，年份列为空，导入失败", rowIndex + 1));
                        return result;
                    }
                    #endregion
                    var eqData = eqDataList.Where(p => p.EquipmentName == Col3).FirstOrDefault();
                    if (eqData == null)
                    {
                        result.response = $"未找到模型区域：{Col3}"; ;
                        return result;
                    }
                    var equipmentID = eqData.ID;
                    if (string.IsNullOrEmpty(Col4))
                    {
                        result.response = (string.Format(@"第'{0}'行，单位列为空，导入失败", rowIndex + 1));
                        return result;
                    }

                    //检查输入的单位是否能在维护的表中找到，没有则新增
                    string Unit;
                    var unitData = unitDataList.Where(p => p.Name == Col4.Trim().ToUpper()).FirstOrDefault();
                    if (unitData != null)
                    {
                        Unit = unitData.ID;
                    }
                    else
                    {
                        DFM.Model.Models.UnitmanageEntity unitmanageEntity = new DFM.Model.Models.UnitmanageEntity();
                        unitmanageEntity.CreateCustomGuid(_user.Name.ToString());
                        unitmanageEntity.Name = Col4.Trim();
                        unitmanageEntity.Enable = 1;
                        unitmanageEntity.Deleted = 0;
                        var unitAdd = _unit.Add(unitmanageEntity);
                        Unit = unitmanageEntity.ID;
                    }

                    List<MothModel> mothModels = new List<MothModel>();
                    mothModels.Add(new MothModel { m = "1", v = Col6 });
                    mothModels.Add(new MothModel { m = "2", v = Col7 });
                    mothModels.Add(new MothModel { m = "3", v = Col8 });
                    mothModels.Add(new MothModel { m = "4", v = Col9 });
                    mothModels.Add(new MothModel { m = "5", v = Col10 });
                    mothModels.Add(new MothModel { m = "6", v = Col11 });
                    mothModels.Add(new MothModel { m = "7", v = Col12 });
                    mothModels.Add(new MothModel { m = "8", v = Col13 });
                    mothModels.Add(new MothModel { m = "9", v = Col14 });
                    mothModels.Add(new MothModel { m = "10", v = Col15 });
                    mothModels.Add(new MothModel { m = "11", v = Col16 });
                    mothModels.Add(new MothModel { m = "12", v = Col17 });

                    foreach (var item in mothModels)
                    {
                        if (item.v != "" && item.v != null)
                        {
                            var oldData = kpiDataList.Where(p => p.DataName.Equals(Col1) && p.DateType.Equals(Col2) &&
                               p.Year == Convert.ToInt32(Col5) &&
                               p.Month == Convert.ToInt32(item.m) &&
                               p.ModelRef == eqData.ID &&
                               p.Unit == Unit &&
                               p.ModelRef == equipmentID
                               ).FirstOrDefault();


                            if (oldData == null)
                            {
                                oldData = new KpitgtEntity();
                                oldData.CreateCustomGuid(_user.Name);
                                addKpitgtEntityList.Add(oldData);
                            }
                            else
                            {
                                oldData.Modify(oldData.ID, _user.Name);
                                upKpitgtEntityList.Add(oldData);
                            }

                            oldData.DataName = Col1;
                            oldData.DateType = Col2;
                            oldData.ModelRef = equipmentID;
                            oldData.Year = Convert.ToInt32(Col5);
                            oldData.Month = Convert.ToInt32(item.m);
                            oldData.Tgt = Convert.ToDecimal(item.v);
                            oldData.Unit = Unit;
                        }

                    }

                    rowIndex++;
                }
                int updateCount = 0;
                int addCount = 0;
                _unitOfWork.BeginTran();
                if (upKpitgtEntityList.Count > 0)
                {
                    await _dal.Update(upKpitgtEntityList);
                    updateCount = upKpitgtEntityList.Count;
                }
                if (addKpitgtEntityList.Count > 0)
                {
                    addCount = await _dal.Add(addKpitgtEntityList);
                }
                _unitOfWork.CommitTran();
                result.msg = "导入成功";
                result.response = $"操作成功！新增数据{addCount}条，更新数据{updateCount}条";
            }
            catch (Exception e)
            {
                result.response = e.StackTrace.ToString();
                return result;
            }
            result.success = true;
            return result;
        }
        public async Task<DataTable> ReadExcel(Stream stream)
        {
            await Task.Delay(1000);

            //var rows = stream.QueryRange(startCell: "A1", endCell: "P18").Cast<IDictionary<string, object>>();
            var rows = stream.QueryRange(startCell: "A1").Cast<IDictionary<string, object>>();
            DataTable fileData = new DataTable();
            for (int i = 1; i <= 16; i++)
            {
                string coil = "Column" + i.ToString();
                fileData.Columns.Add(coil);
            }
            foreach (var row in rows)
            {
                var dataRow = fileData.NewRow();
                int m = 0;
                foreach (var kvp in row)
                {
                    dataRow[m] = kvp.Value;
                    m++;

                }
                fileData.Rows.Add(dataRow);
            }
            return fileData;
        }
        public async Task<bool> SaveForm(KpitgtEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
        /// <summary>
        /// 获取物理模型
        /// </summary>
        /// <param name="poId"></param>
        /// <returns></returns>
        public async Task<List<TreeModel>> GetEquipmentTree()
        {
            //获取表单信息
            MessageModel<List<TreeModel>> apiResult_TreeModel = await HttpHelper.PostAsync<List<TreeModel>>("DFM", "api/Equipment/GetEquipmentTree", _user.GetToken(), null);
            List<TreeModel> TreeModelRes = apiResult_TreeModel.response;
            if (TreeModelRes != null && TreeModelRes.Count > 0)
            {
                //截取到层级为seqment的数据
                foreach (var item in TreeModelRes)
                {
                    //车间层级
                    var area = item.children;
                    if (area != null && area.Count > 0)
                    {
                        //循环车间层级
                        foreach (var mm in area)
                        {
                            //产线层级
                            var line = mm.children;
                            if (line != null && line.Count > 0)
                            {
                                foreach (var qq in line)
                                {
                                    //工作中心层级
                                    var segment = qq.children;
                                    if (segment != null && segment.Count > 0)
                                    {
                                        foreach (var xx in segment)
                                        {
                                            //工作中心的下一级
                                            var nextLeave = xx.children;
                                            //如果工作中心的下一级还有数据则置为空
                                            if (nextLeave != null && nextLeave.Count > 0)
                                            {
                                                xx.children = new List<TreeModel>();
                                            }

                                        }

                                    }

                                }

                            }

                        }

                    }

                }
                return TreeModelRes;
            }
            else
            {
                return new List<TreeModel>();
            }

        }


        #region 中间表定时服务 

        #region 原料损耗率  RawMaterialLossRate

        /// <summary>
        /// 定义物料组
        /// </summary>
        public class MaterialModel
        {
            public string MATERIAL_GROUP_NAME { get; set; }
            public string GROUP_TYPE { get; set; }
            public string NAME { get; set; }
            public string CODE { get; set; }
            public string MID { get; set; }

            public decimal SUM { get; set; }
        }

        /// <summary>
        /// 原料损耗率写入
        /// </summary>
        /// <returns></returns>
        public async Task<string> LossRate_Day()
        {
            DateTime star = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd") + " 00:00:00");
            DateTime end = star.AddDays(1).AddSeconds(-1);
            string msg = await RawMaterialLossRate_Day(star, end);
            return msg;
        }

        public async Task<string> RawMaterialLossRate_Day(DateTime star, DateTime end)
        {
            try
            {
                string msgxx = string.Format(@"原料损耗率计算{0},", "进入函数");
                SerilogServer.LogDebug(msgxx, "RawMaterialLossRateDay");

                //获取原料关联关系
                #region 多表关联(查询物料数据)

                //查询原料组类型数据
                var whereExpression = Expressionable.Create<DFM.Model.Models.MaterialGroupEntity,
                   DFM.Model.Models.MaterialGroupMappingEntity, DFM.Model.Models.MaterialEntity>()
                   .And((a, b, c) => a.GroupType == "MaterialGroup")
                   .ToExpression();

                var MList = await _dal.QueryMuch<DFM.Model.Models.MaterialGroupEntity,
                     DFM.Model.Models.MaterialGroupMappingEntity, DFM.Model.Models.MaterialEntity, MaterialModel>(
                      (a, b, c) => new object[]
                      {
                     JoinType.Inner, a.ID == b.MaterialId,
                     JoinType.Inner, c.ID==b.MaterialId
                      },
                      (a, b, c) => new MaterialModel
                      {
                          MATERIAL_GROUP_NAME = a.MaterialGroupName,
                          GROUP_TYPE = a.GroupType,
                          NAME = c.NAME,
                          CODE = c.Code,
                          MID = c.ID
                      },
                      whereExpression
                     );

                #endregion


                if (MList == null || MList.Count <= 0)
                {
                    SerilogServer.LogDebug($"原料损耗率计算没有找到需要操作的组", "RawMaterialLossRateDay");
                    return "失败，没有找到需要操作的组";
                }

                SerilogServer.LogDebug($"原料损耗率计算,物料总数：[{MList.Count + ":"}]", "RawMaterialLossRateDay");

                #region 拿盘存数据

                //这里进行拿盘存数据
                var mIDS = MList.GroupBy(P => P.MID).Select(P => P.Key);
                //拿盘存数据
                var verifiyDetailData = await _dalVerifiyDetailEntity.Db.Queryable<VerifiyDetailEntity>().In(p => p.MaterialId, mIDS).Where(p => p.CreateDate >= star && p.CreateDate <= end && p.Result == "差异").ToListAsync();

                SerilogServer.LogDebug($"原料损耗率计算,拿盘存数据：[{verifiyDetailData.Count + ":"}]", "RawMaterialLossRateDay");
                #endregion

                //获取所有组目标值
                var groupMType = MList.GroupBy(p => p.MATERIAL_GROUP_NAME).Select(p => p.Key).ToArray();
                //查询完成的工单 ZXH2制造 不等于 ZXH2包装  （制造工单）
                var proList = await _dalProductionOrderEntity.Db.Queryable<ProductionOrderEntity>().Where(p => p.PoStatus == "3" && p.SapOrderType == "ZXH2" && (p.EndTime != null) && p.EndTime.Value >= star && p.EndTime.Value <= end).ToListAsync();
                var proArray = proList.GroupBy(p => p.ID).Select(p => p.Key).ToArray();
                if (proArray != null && proArray.Length > 0)
                {
                    //拿所有的消耗需求
                    var poConSumeList = await _dalPoConsumeMaterialListViewEntity.Db.Queryable<PoConsumeMaterialListViewEntity>().In(p => p.ProductionOrderId, proArray).ToListAsync();
                    //这里执行数据计算
                    // Quantity BOM值   AdjustPercentQuantity  预调值    ActualConsume 实际耗用值
                    //汇总数据
                    var groupM = poConSumeList.GroupBy(p => p.MaterialCode)
                        .Select(p => new
                        {
                            MCode = p.Key,
                            Quantity = p.Sum(p => p.Quantity == null ? 0 : p.Quantity.Value),
                            AdjustPercentQuantity = p.Sum(p => p.AdjustPercentQuantity == null ? 0 : p.AdjustPercentQuantity.Value),
                            ActualConsume = p.Sum(p => p.ActualConsume)
                        }).ToArray();
                    if (groupM == null || groupM.Length <= 0)
                    {
                        SerilogServer.LogDebug($"原料损耗率计算,poConSumeList：[{poConSumeList.Count + ":"}]", "RawMaterialLossRateDay");
                        return "成功，无数据";
                    }

                    List<ImtableRowmatLossEntity> listCreate = new List<ImtableRowmatLossEntity>();
                    //循环
                    for (int i = 0; i < groupMType.Length; i++)
                    {
                        //物料组
                        string mGroup = groupMType[i];
                        if (!string.IsNullOrEmpty(mGroup))
                        {
                            SerilogServer.LogDebug($"原料损耗率计算,进行了分组", "RawMaterialLossRateDay");

                            #region 这里拿组绑定盘存差异值

                            //筛选组下的物料
                            var gmIDS = MList.Where(p => p.GROUP_TYPE == mGroup).GroupBy(p => p.MID).Select(p => p.Key).ToList();
                            var sumNumber = verifiyDetailData.Where(P => gmIDS.Contains(P.MaterialId) && P.Differencenumber != null).Sum(P => P.Differencenumber.Value);

                            #endregion

                            ImtableRowmatLossEntity model = new ImtableRowmatLossEntity();
                            model.CreateCustomGuid(_user.Name);
                            model.ReceivedQty = 0; //废弃
                            model.ReturnQty = 0; //废弃
                            model.UsedQty = 0; //废弃
                            model.RemainQty = 0; //废弃
                                                 //拿组物料
                            var forMList = MList.Where(p => p.MATERIAL_GROUP_NAME == mGroup).GroupBy(p => p.CODE).Select(p => p.Key).ToList();//拿损耗
                            var shData = groupM.Where(p => forMList.Contains(p.MCode)).ToList();
                            // Quantity BOM值   AdjustPercentQuantity  预调值    ActualConsume 实际耗用值
                            decimal quantity = shData.Sum(p => p.Quantity);
                            decimal adjustPercentQuantity = shData.Sum(p => p.AdjustPercentQuantity);
                            decimal actualConsume = shData.Sum(p => p.ActualConsume);
                            int year = star.Year;
                            int month = star.Month;
                            int day = star.Day;
                            model.RowmaterialGroup = mGroup;
                            model.Year = year;
                            model.Month = month;
                            model.Day = day;
                            model.BomWithoutloss = quantity;//不计损耗标准BOM
                                                            // 投料消耗 + 重调 + 预调）+每日库存盘点的盘盈盘亏）-理论耗用
                            model.ActualConsumption = actualConsume + adjustPercentQuantity + Convert.ToDecimal(sumNumber) - quantity;
                            model.InventoryAdjust = Convert.ToDecimal(sumNumber);//盘赢盘亏值(需补充)
                            listCreate.Add(model);
                        }
                    }

                    _unitOfWork.BeginTran();
                    bool result = true;
                    if (listCreate.Count > 0)
                    {
                        result = await _dalImtableRowmatLossEntity.Add(listCreate) > 0;
                        string msg = string.Format(@"原料损耗率计算,插入结{0}", result);
                        SerilogServer.LogDebug(msg, "RawMaterialLossRateDay");
                    }
                    if (result == true)
                    {
                        _unitOfWork.CommitTran();
                        return "成功";
                    }
                    else
                    {
                        _unitOfWork.RollbackTran();
                        return "失败";
                    }
                }
                else
                {
                    SerilogServer.LogDebug($"原料损耗率计算,无数据", "RawMaterialLossRateDay");
                    return "成功，无数据";
                }
            }
            catch (Exception)
            {
                _unitOfWork.RollbackTran();
                return "失败";
            }


        }

        #endregion

        #region 包材损耗率 PackagingMaterialsLossRate(大小分类 产线工作中心) PC和EA是1:1

        /// <summary>
        /// 包材损耗率写入
        /// </summary>
        /// <returns></returns>
        public async Task<string> PackagingLossRate_Day()
        {
            DateTime star = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd") + " 00:00:00");
            DateTime end = star.AddDays(1).AddSeconds(-1);
            string msg = await PackagingMaterialsLossRateMType_Day(star, end);
            return msg;
        }

        public async Task<string> PackagingMaterialsLossRateMType_Day(DateTime star, DateTime end)
        {
            try
            {
                string msgx = string.Format(@"包材损耗率计算{0},", "进入函数");
                SerilogServer.LogDebug(msgx, "PackagingMaterialsLossRateMTypeDay");

                #region 产线

                //拿所有产线
                var equpmentList = await _eq.FindList(p => p.Level == "Line");

                #endregion

                #region 物料分类

                //筛选物料(包材用)
                var mList = await _dalMaterialEntity.FindList(p => p.Type == "ZPKG" || p.Type == "ZLBS");
                //筛选分类（总的分类）
                var mTypes = mList.GroupBy(p => new { p.MatklZh, p.Kschl1 }).Select(p => new { p.Key.MatklZh, p.Key.Kschl1 }).ToList();

                #endregion

                #region 工单产线

                //查询完成的工单 ZXH2制造 不等于 ZXH2包装  （制造工单）
                var proList = await _dalProductionOrderEntity.Db.Queryable<ProductionOrderEntity>().Where(p => p.PoStatus == "3" && p.SapOrderType != "ZXH2" && (p.EndTime != null) && p.EndTime.Value >= star && p.EndTime.Value <= end).ToListAsync();
                var proArray = proList.GroupBy(p => p.ID).Select(p => p.Key).ToArray();
                //var workCenter= proList.GroupBy(p=>p.SegmentCode).Select(p=>p.Key).ToList();

                #endregion

                if (proArray == null || proArray.Length <= 0)
                {
                    string msg = string.Format(@"包材损耗率计算{0},", "失败，未找到工单");
                    SerilogServer.LogDebug(msg, "PackagingMaterialsLossRateMTypeDay");
                    return "失败，未找到工单";
                }

                #region 拿消耗并按照物料分组(所有工单的数据)

                //拿所有的消耗需求
                var poConSumeList = await _dalPoConsumeMaterialListViewEntity.Db.Queryable<PoConsumeMaterialListViewEntity>().In(p => p.ProductionOrderId, proArray).ToListAsync();
                //这里执行数据计算
                // Quantity BOM值   AdjustPercentQuantity  预调值    ActualConsume 实际耗用值
                ////汇总数据
                //var groupM = poConSumeList.GroupBy(p => p.MaterialCode)
                //    .Select(p => new
                //    {
                //        MCode = p.Key,
                //        Quantity = p.Sum(p => p.Quantity == null ? 0 : p.Quantity.Value),
                //        AdjustPercentQuantity = p.Sum(p => p.AdjustPercentQuantity == null ? 0 : p.AdjustPercentQuantity.Value),
                //        ActualConsume = p.Sum(p => p.ActualConsume)
                //    }).ToList();

                #endregion

                #region 工单报工数据

                //这里拿工单的报工数据
                var hisList = await _dalHistoryViewEntityModel.Db.Queryable<HistoryViewEntity>().In(p => p.Pid, proArray).Where(p => p.SendStates == "已发送").ToListAsync();

                #endregion

                string bigType = string.Empty;
                string minType = string.Empty;

                List<ImtablePakmatLossEntity> listInsert = new List<ImtablePakmatLossEntity>();

                //产线  
                for (int i = 0; i < equpmentList.Count; i++)
                {
                    string lineCode = equpmentList[i].EquipmentCode;
                    var lineList = proList.Where(p => p.LineCode == lineCode).ToList();
                    //存在数据
                    if (lineList != null && lineList.Count > 0)
                    {
                        string msg = string.Format(@"包材损耗率计算{0},", "存在产线数据");
                        SerilogServer.LogDebug(msg, "PackagingMaterialsLossRateMTypeDay");
                        //筛选工单
                        var pIDS = lineList.GroupBy(P => P.ID).Select(P => P.Key).ToList();
                        if (pIDS != null && pIDS.Count > 0)
                        {
                            string pIDSMSG = string.Format(@"包材损耗率计算{0},", "存在工单数据");
                            SerilogServer.LogDebug(pIDSMSG, "PackagingMaterialsLossRateMTypeDay");
                            //需求表
                            var poConList = poConSumeList.Where(p => pIDS.Contains(p.ID)).ToList();
                            //这里计算需求表
                            var mQtyList = poConList.GroupBy(p => p.MaterialCode)
                                      .Select(p => new
                                      {
                                          MCode = p.Key,
                                          Quantity = p.Sum(p => p.Quantity == null ? 0 : p.Quantity.Value),
                                          AdjustPercentQuantity = p.Sum(p => p.AdjustPercentQuantity == null ? 0 : p.AdjustPercentQuantity.Value),
                                          ActualConsume = p.Sum(p => p.ActualConsume)
                                      }).ToList();

                            //分组汇总数据
                            var hMListData = (from a in mQtyList
                                              join b in mList on a.MCode equals b.Code
                                              select new
                                              {
                                                  a.MCode,
                                                  a.Quantity,
                                                  a.AdjustPercentQuantity,
                                                  a.ActualConsume,
                                                  b.MatklZh,
                                                  b.Kschl1
                                              }).ToList();

                            //这里拿报工表（拿报工数据）
                            var bgList = hisList.Where(p => pIDS.Contains(p.ID)).ToList();
                            var hisListSum = bgList.GroupBy(p => p.MCode)
                                      .Select(p => new
                                      {
                                          MCode = p.Key,
                                          IQuantity = p.Sum(p => p.IQuantity == null ? 0 : p.IQuantity.Value)
                                      }).ToList();

                            if (hMListData != null && hMListData.Count > 0)
                            {
                                string hMListDatamsg = string.Format(@"包材损耗率计算{0},", "存在分组汇总数据");
                                SerilogServer.LogDebug(hMListDatamsg, "PackagingMaterialsLossRateMTypeDay");
                                for (int x = 0; x < hMListData.Count; x++)
                                {
                                    string code = hMListData[x].MCode;
                                    bigType = hMListData[x].MatklZh;
                                    minType = hMListData[x].Kschl1;
                                    ImtablePakmatLossEntity model = new ImtablePakmatLossEntity();
                                    model.CreateCustomGuid(_user.Name);
                                    int year = star.Year;
                                    int month = star.Month;
                                    int day = star.Day;
                                    model.Year = year;
                                    model.Month = month;
                                    model.Day = day;
                                    model.LineId = lineCode; //产线
                                    model.WorkCenter = " "; //工作中心 为空
                                                            //大小分类
                                    model.Pakgroup = bigType;
                                    model.SecondPakgroup = minType;
                                    model.StdLoss = 0;//为空
                                    model.ConsumedActual = hisListSum.Where(p => p.MCode == code).Sum(p => p.IQuantity);//SAP报工消耗数量
                                    model.BomWithoutLoss = hMListData[x].Quantity; //不计损耗标准BOM
                                    listInsert.Add(model);
                                    string listInsertmsg = string.Format(@"包材损耗率计算{0},", "listInsert插入了数据");
                                    SerilogServer.LogDebug(listInsertmsg, "PackagingMaterialsLossRateMTypeDay");
                                }
                            }
                        }
                    }
                }

                _unitOfWork.BeginTran();

                bool result = true;
                if (listInsert.Count > 0)
                {
                    string msg = string.Format(@"原料损耗率计算,插入结果{0}", result);
                    SerilogServer.LogDebug(msg, "PackagingMaterialsLossRateMTypeDay");
                    result = await _dalImtablePakmatLossEntity.Add(listInsert) > 0;
                }
                if (result == true)
                {
                    _unitOfWork.CommitTran();
                    return "成功";
                }
                else
                {
                    _unitOfWork.RollbackTran();
                    return "失败";
                }
            }
            catch (Exception ex)
            {

                return "失败";
            }
        }

        #endregion



        #endregion


        #region SIM2_制造车间

        #region 计划执行信息

        /// <summary>
        /// 计划执行信息(查询当天数据)
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<ProOrderMesModel> GetProOrderInfo(MPDRequeatModel reqModel)
        {
            ProOrderMesModel proOrderMesModel = new ProOrderMesModel();
            try
            {
                DateTime todayStart = reqModel.startTime;
                DateTime todayEnd = reqModel.endTime;
                //DateTime todayStart = DateTime.Today; // 当天的00:00:00
                //DateTime todayEnd = todayStart.AddDays(1).AddSeconds(-1); // 当天的23:59:59                
                //筛选计划信息
                var proOrderMode = await _ProductionOrderEntity.FindList(p => p.PlanDate >= todayStart && p.PlanDate <= todayEnd);


                var equ = await _EquipmentEntity.FindList(p => p.Level == "Line");
                var curLine = equ.FirstOrDefault(a => a.ID == reqModel.Lineid);
                if (curLine == null)
                {
                    proOrderMesModel.DataState = "传参无效:" + reqModel.Lineid;
                    return proOrderMesModel;
                }
                var poList = proOrderMode.Where(a => a.LineCode == curLine.EquipmentCode && a.OrderType != "O" && a.SapFlag > 0).ToList();

                if (equ == null)
                {
                    proOrderMesModel.DataState = "传参无效:" + reqModel.Lineid;
                    return proOrderMesModel;
                }
                else
                {
                    proOrderMesModel.DataState = "传参:" + reqModel.Lineid;
                }

                var batchModel = await _BatchEntity.FindList(p => p.ProductionOrderId != "" && p.LineId == reqModel.Lineid);

                var results = from a in batchModel
                              join b in proOrderMode on a.ProductionOrderId equals b.ID
                              //into abGroup  from b in abGroup.DefaultIfEmpty()
                              where
                              b.SapOrderType == "ZXH2"
                              //&& b.PlanDate >= todayStart && b.PlanDate <= todayEnd
                              //  && a.LineId == reqModel.Lineid
                              select new
                              {
                                  a.PrepStatus,
                                  a.ID,
                                  b.PlanDate,
                                  a.LineId,
                                  b.SapOrderType
                              };

                var results1 = from a in proOrderMode
                               join b in equ on a.LineCode equals b.EquipmentCode
                               where a.PDType == "FILL" && a.SapOrderType == "ZXH1"
                               //&& a.PlanDate >= todayStart && a.PlanDate <= todayEnd
                               //   && b.ID == reqModel.Lineid
                               select new
                               {
                                   a.ID,
                                   a.PlanDate,
                                   a.SapOrderType,
                                   a.PoStatus,
                                   Equipmentid = b.ID,
                                   a.PDType
                               };

                //制造工单（工单批次数）
                var zzList = results.Count();
                //已备 3、4、6、7、8、9 //已投 9

                //已备锅数+已投锅数
                var YBList = results.Where(p => p.PrepStatus == "3" || p.PrepStatus == "4" || p.PrepStatus == "6" || p.PrepStatus == "7" || p.PrepStatus == "8" || p.PrepStatus == "9").Count();//|| p.PrepStatus == "9"

                //已投锅数
                var YTList = results.Where(p => p.PrepStatus == "9").Count();

                //灌装
                var GZList = results1.Where(p => p.PoStatus == "3").Count(); //results1.Where(p => p.PoStatus == "6").Count(); //3 完成  6 运行中
                                                                             //灌装总数
                var GZCount = results1.Count();

                //备料工单计划完成率
                var PlanBl = 0.00m;
                var Procook = 0.00m;
                if (zzList > 0)
                {
                    PlanBl = Math.Round(Convert.ToDecimal(YBList) / Convert.ToDecimal(zzList), 2) * 100;
                }
                if (poList.Count > 0)
                {
                    //Procook = Math.Round(Convert.ToDecimal(GZList) / Convert.ToDecimal(GZCount), 2) * 100;
                    proOrderMesModel.FinishCookPoCount = poList.Count(a => a.PoStatus == "3");
                    proOrderMesModel.TotalCookPoCount = poList.Count;
                    Procook = Math.Round(Convert.ToDecimal(proOrderMesModel.FinishCookPoCount) / Convert.ToDecimal(proOrderMesModel.TotalCookPoCount), 2) * 100;
                }
                //煮制工单完成率

                proOrderMesModel.PreparedPlanRate = PlanBl;
                proOrderMesModel.CookPlanRate = Procook;
                proOrderMesModel.PreparedNumber = YBList;
                proOrderMesModel.PreparedTotal = zzList;
                proOrderMesModel.FeedNunber = YTList;
                proOrderMesModel.FeedTotal = zzList;
                proOrderMesModel.BottlNumber = GZList;
                proOrderMesModel.BottlTotal = GZCount;
                return proOrderMesModel;
            }
            catch (Exception ex)
            {
                proOrderMesModel.DataState = ex.StackTrace;
                return proOrderMesModel;
            }

        }
        #endregion

        #region 当前备料、煮制进度对照图
        /// <summary>
        /// 当前备料、煮制进度对照图(查询当天数据)
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<MaterialProgressDay> MaterialProgress(MPDRequeatModel reqModel)
        {
            MaterialProgressDay materialProgressDay = new MaterialProgressDay();
            try
            {
                DateTime todayStart = reqModel.startTime;
                DateTime todayEnd = reqModel.endTime;
                //DateTime todayStart = DateTime.Today; // 当天的00:00:00
                //DateTime todayEnd = todayStart.AddDays(1).AddSeconds(-1); // 当天的23:59:59

                var eq = await _EquipmentEntity.FindEntity(p => p.ID == reqModel.Lineid);
                if (eq == null)
                {
                    materialProgressDay.DataState = "传参无效:" + reqModel.Lineid;
                    return materialProgressDay;
                }
                else
                {
                    materialProgressDay.DataState = "传参:" + reqModel.Lineid;
                }
                //else
                //{
                //    //"产线不存在"
                //}
                var proOrderMode = await _ProductionOrderEntity.FindList(p => p.PlanDate >= todayStart && p.PlanDate <= todayEnd);

                var batchModel = await _BatchEntity.FindList(p => p.ProductionOrderId != "" && p.LineId == reqModel.Lineid);

                //  var batchModel = await _BatchEntity.FindList(p => p.ProductionOrderId != "");
                //var proOrderMode = await _ProductionOrderEntity.FindList(p => p.PlanStartTime != null);
                var equ = await _EquipmentEntity.FindList(p => p.Level == "Line");
                var results = from a in batchModel
                              join b in proOrderMode on a.ProductionOrderId equals b.ID
                              //into abGroup from b in abGroup.DefaultIfEmpty()
                              where b.SapOrderType == "ZXH2"
                              && b.PlanDate >= todayStart && b.PlanDate <= todayEnd
                              && a.LineId == reqModel.Lineid
                              select new
                              {
                                  a.PrepStatus,
                                  a.ID,
                                  b.PlanDate,
                                  a.LineId,
                                  b.SapOrderType,
                                  b.PoStatus
                              };
                if (results != null)
                {
                    //制造工单
                    var zzList = results.Count();
                    //已备 3、4、6、7、8、9 //已投 9
                    //备料  备料中	12
                    //称量完成    11
                    // 复称完成    10
                    //备料中()
                    var BLZCount = results.Where(p => p.PrepStatus == "10" || p.PrepStatus == "11" || p.PrepStatus == "12").Count();
                    //未开始备料
                    var WKSBLCount = results.Where(p => p.PrepStatus == "2").Count();
                    //已备锅数
                    var YBCount = results.Where(p => p.PrepStatus == "3" || p.PrepStatus == "4" || p.PrepStatus == "6" || p.PrepStatus == "7" || p.PrepStatus == "8" || p.PrepStatus == "9").Count();

                    materialProgressDay.YBCount = YBCount;//备料总数量
                    materialProgressDay.BLZCount = BLZCount;//已备料总数量
                    materialProgressDay.WKSBLCount = WKSBLCount;//未开始备料
                                                                //总备料进度
                    var BLProgress = BLZCount + WKSBLCount + YBCount;
                    materialProgressDay.BLCOUNT = BLProgress;//总数量
                }
                else
                {
                    materialProgressDay.YBCount = 0;
                    materialProgressDay.BLZCount = 0;
                    materialProgressDay.WKSBLCount = 0;
                    materialProgressDay.BLCOUNT = 0;
                }

                #region 当日煮料进度


                //工单
                var orderEntities = proOrderMode.Where(p => p.PoStatus == "6" && p.SapOrderType == "ZXH2" && p.LineCode == eq.EquipmentCode).ToList();
                var porExecution = await _PoProducedExecutionEntity.FindList(x => orderEntities.Any(p => p.ID == x.ProductionOrderId));
                var Logsheet = await _LogsheetEntity.FindList(p => porExecution.Any(x => x.ID == p.PoExecutionId));
                var LogsheetDetail = await _LogsheetDetailServices.FindList(p => Logsheet.Any(x => x.ID == p.LogsheetId) && p.ParameterName == "通过结果" && (p.Value == "" || p.Value == null));
                var results2 = from a in orderEntities
                               join b in porExecution on a.ID equals b.ProductionOrderId
                               join c in Logsheet on b.ID equals c.PoExecutionId
                               join d in LogsheetDetail on c.ID equals d.LogsheetId
                               select new
                               {
                                   a.ID,
                                   a.ProductionOrderNo
                               };
                var Yt = results2.GroupBy(p => p.ProductionOrderNo).ToList();




                var orderEntities1 = proOrderMode.Where(p => p.SapOrderType == "ZXH2" && (p.PoStatus == "6" || p.PoStatus == "3") && p.LineCode == eq.EquipmentCode).ToList();

                var porExecution1 = await _PoProducedExecutionEntity.FindList(x => orderEntities1.Any(p => p.ID == x.ProductionOrderId));
                var Logsheet1 = await _LogsheetEntity.FindList(p => porExecution1.Any(x => x.ID == p.PoExecutionId));

                var logsheetDetails = await _LogsheetDetailServices.FindList(p => Logsheet1.Any(x => x.ID == p.LogsheetId) && p.ParameterName == "通过结果" && (p.Value != "" || p.Value != null));
                var results3 = from a in orderEntities1
                               join b in porExecution1 on a.ID equals b.ProductionOrderId
                               join c in Logsheet1 on b.ID equals c.PoExecutionId
                               join d in logsheetDetails on c.ID equals d.LogsheetId
                               select new
                               {
                                   a.ID,
                                   a.ProductionOrderNo
                               };
                var Yg = results3.GroupBy(p => p.ProductionOrderNo).Distinct().ToList();






                var YBL = results.Where(p => p.PrepStatus == "3" && p.PoStatus == "2").Count();
                var YTL = Yt.Count;//已投
                var YZL = Yg.Count;//已煮料
                var ProduceTotal = YBL + YTL + YZL;
                #endregion

                materialProgressDay.YZL = YZL;
                materialProgressDay.YTL = YTL;
                materialProgressDay.YBL = YBL;
                materialProgressDay.ProduceTotal = ProduceTotal;
                return materialProgressDay;
            }
            catch (Exception ex)
            {
                materialProgressDay.DataState = ex.StackTrace;
                return materialProgressDay;
            }

        }


        #endregion

        #region 流程等待时间
        //查询流程等待的
        public async Task<ProcessWaiting> ProcessWaiting(MPDRequeatModel reqModel)
        {
            ProcessWaiting processWaiting = new ProcessWaiting();
            var linerIDList = await _LinerelationEntityDal.FindList(p => p.PrelineId == reqModel.Lineid);

            if (linerIDList == null || linerIDList.Count <= 0)
            {
                processWaiting.WaitTime = 0;
                processWaiting.CipTime = 0;
                processWaiting.TotalTime = processWaiting.WaitTime + processWaiting.CipTime;//(dlTime + cipTime) / 60;
                return processWaiting;
            }

            var lineListID = linerIDList.Select(p => p.LineId).ToList();
            var gzjList = await _EquipmentEntity.FindList(p => p.Level == "Unit" && lineListID.Contains(p.LineId) && p.Enabled == 1 && p.Deleted == 0 && p.EquipmentCode.Contains("Filler"));

            if (gzjList == null || gzjList.Count <= 0)
            {
                processWaiting.WaitTime = 0;
                processWaiting.CipTime = 0;
                processWaiting.TotalTime = processWaiting.WaitTime + processWaiting.CipTime;//(dlTime + cipTime) / 60;
                return processWaiting;
            }

            var tesultIDS = gzjList.Select(p => p.ID).ToList();



            //待料
            var sumTime = await _PerformanceEntity.FindList(p => tesultIDS.Contains(p.EquipmentId) && p.Reason == "待料" && p.Group == "流程等待" && p.ProductionOrderNo != null && p.ProductionOrderNo != "" && p.EndTimeUtc >= reqModel.startTime && p.EndTimeUtc <= reqModel.endTime);
            //var sumTime2 = sumTime.Where(p => LineLIst.Any(s => s.ID == p.LineId)).ToList();
            var dlTime = sumTime.Sum(p => p.TimeDifferenceInSeconds);

            //待CIP
            var sumTime1 = await _PerformanceEntity.FindList(p => tesultIDS.Contains(p.EquipmentId) && p.Reason == "待CIP" && p.Group == "流程等待" && p.ProductionOrderNo != null && p.ProductionOrderNo != "" && p.EndTimeUtc >= reqModel.startTime && p.EndTimeUtc <= reqModel.endTime);
            //var sumTime2 = sumTime.Where(p => LineLIst.Any(s => s.ID == p.LineId)).ToList();
            var cipTime = sumTime1.Sum(p => p.TimeDifferenceInSeconds);
            /*            //找group为流程等待的ID
                        var downtimeGroup = await _DowntimeGroupEntity.FindEntity(p => p.Description == "流程等待");
                        if (downtimeGroup != null)
                        {
                            //查询制造车间 产线 设备
                            // var area = await _EquipmentEntity.FindEntity(p =>p.EquipmentCode== "CookingArea");
                            //var eqLine = await _EquipmentEntity.FindList(p => p.ParentId== area.ID && p.Level== "Line" && p.Deleted==0 && p.Enabled==1);
                            var eqUnit = await _EquipmentEntity.FindList(p => p.LineId == reqModel.Lineid && p.Level == "Unit" && p.Deleted == 0 && p.Enabled == 1);

                            //查询group下的Reason
                            var downtimeReason = await _DowntimeReasonEntity.FindList(p => p.GroupId == downtimeGroup.ID);
                            var downTime = await _DowntimeEntity.FindList(p => downtimeReason.Any(x => x.ID == p.ReasonId) && p.StartTimeUtc != null && p.EndTimeUtc != null && p.CreateDate >= reqModel.startTime && p.CreateDate <= reqModel.endTime);
                            var downTimemodel = from a in downTime
                                                join b in eqUnit on a.EquipmentId equals b.ID
                                                select new
                                                {
                                                    a.ID,
                                                    a.StartTimeUtc,
                                                    a.EndTimeUtc
                                                };


                            double totalTime = 0;//总时间
                            double waitTime = 0;//待料时间
                            double cipTime = 0;//待CIP时间
                            foreach (var item in downTimemodel)
                            {
                                DateTime start = Convert.ToDateTime(item.StartTimeUtc); // 起始日期和时间
                                DateTime end = Convert.ToDateTime(item.EndTimeUtc); // 结束日期和时间
                                TimeSpan duration = end - start; // 计算时间间隔
                                double hours = duration.TotalHours; // 获取小时数
                                totalTime += hours;
                            }

                            //待料时间计算
                            //  var downTime1 = await _DowntimeEntity.FindList(p => downtimeReason1.Any(x => x.ID == p.ReasonId) && p.StartTimeUtc != null && p.EndTimeUtc != null && p.CreateDate >= reqModel.startTime && p.CreateDate <= reqModel.endTime);
                            //var downtimeReason1 = await _DowntimeReasonEntity.FindList(p => p.GroupId == downtimeGroup.ID && p.Description=="待料");
                            var downtimeReason1 = await _DowntimeReasonEntity.FindList(p => p.GroupId == downtimeGroup.ID);
                            var downTime1 = await _DowntimeEntity.FindList(p => p.StartTimeUtc != null && p.EndTimeUtc != null && p.CreateDate >= reqModel.startTime && p.CreateDate <= reqModel.endTime);
                            var downTimemodel1 = from a in downTime1
                                                 join c in downtimeReason1 on a.ReasonId equals c.ID
                                                 join b in eqUnit on a.EquipmentId equals b.ID
                                                 where c.Description == "待料"
                                                 select new
                                                 {
                                                     a.ID,
                                                     a.StartTimeUtc,
                                                     a.EndTimeUtc
                                                 };

                            foreach (var item in downTimemodel1)
                            {
                                DateTime start = Convert.ToDateTime(item.StartTimeUtc); // 起始日期和时间
                                DateTime end = Convert.ToDateTime(item.EndTimeUtc); // 结束日期和时间
                                TimeSpan duration = end - start; // 计算时间间隔
                                double hours = duration.TotalHours; // 获取小时数
                                waitTime += hours;
                            }

                            //CIP时间计算
                            // var downtimeReason2= await _DowntimeReasonEntity.FindList(p => p.GroupId == downtimeGroup.ID && p.Description == "待CIP");
                            //var downTime2 = await _DowntimeEntity.FindList(p => downtimeReason2.Any(x => x.ID == p.ReasonId) && p.StartTimeUtc != null && p.EndTimeUtc != null);
                            var downTimemodel2 = from a in downTime1
                                                 join c in downtimeReason1 on a.ReasonId equals c.ID
                                                 join b in eqUnit on a.EquipmentId equals b.ID
                                                 where c.Description == "待CIP"
                                                 select new
                                                 {
                                                     a.ID,
                                                     a.StartTimeUtc,
                                                     a.EndTimeUtc
                                                 };

                            foreach (var item in downTimemodel2)
                            {
                                DateTime start = Convert.ToDateTime(item.StartTimeUtc); // 起始日期和时间
                                DateTime end = Convert.ToDateTime(item.EndTimeUtc); // 结束日期和时间
                                TimeSpan duration = end - start; // 计算时间间隔
                                double hours = duration.TotalHours; // 获取小时数
                                cipTime += hours;
                            }
                            processWaiting.TotalTime = totalTime;
                            processWaiting.WaitTime = waitTime;
                            processWaiting.CipTime = cipTime;
                        }
                        else
                        {
                            //未找到原因为流程等待
                        }*/

            processWaiting.WaitTime = dlTime / 60 / 60;
            processWaiting.CipTime = cipTime / 60 / 60;

            processWaiting.TotalTime = processWaiting.WaitTime + processWaiting.CipTime;//(dlTime + cipTime) / 60;
            return processWaiting;
        }



        #endregion

        #region 生产状态指示灯

        public async Task<List<SIMEquipmentStatusModel>> EquipmentStatus()
        {
            //查找所有灌装机
            var eqList = await _EquipmentEntity.FindList(p => p.EquipmentName.Contains("灌装机") && p.Deleted == 0 && p.Enabled == 1);
            return await GetEquipmentStatusList(eqList);

            List<SIMEquipmentStatusModel> l2Model = new List<SIMEquipmentStatusModel>();

            foreach (var item in eqList)
            {
                SIMEquipmentStatusModel l2 = new SIMEquipmentStatusModel();

                string Status = "";
                var Influxdbquantity = 0;
                //查找设备状态点位
                var opc = await _InfluxOpcTagEntit.FindEntity(p => p.EquipmentId == item.ID && p.Name == "设备状态");
                if (opc != null)
                {
                    try
                    {
                        //获取点位状态
                        Influxdbquantity = (int)Convert.ToDecimal((await _IInfluxDbServices.GetLastInfluxData(DateTime.Now.AddMinutes(-10), DateTime.Now, null, opc.Tag))?.Value);
                    }
                    catch
                    {
                        Influxdbquantity = 0;
                    }

                    #region 截取字符进行状态比对
                    //按设定找点位描述最后一位#号
                    var str = opc.Describe.LastIndexOf("#");
                    if (str != -1)
                    {
                        //截取#后的数据
                        var str1 = opc.Describe.Substring(str + 1);
                        //按;分割
                        string[] parts = str1.Split(";");
                        for (int j = 0; j < parts.Length; j++)
                        {
                            //按=分割
                            string[] parts2 = parts[j].Split("=");
                            if (Influxdbquantity.ToString() == parts2[1])
                            {
                                Status = parts2[0];
                            }
                            //值存在&符号走
                            if (parts2[1].Contains("&"))
                            {
                                //按&符号截取
                                string[] parts1 = parts2[1].Split("&");
                                for (int h = 0; h < parts1.Length; h++)
                                {
                                    //当获取的点位值和查到的描述一致返回状态描述
                                    if (parts1[h] == Influxdbquantity.ToString())
                                    {
                                        Status = parts2[0];
                                        break;
                                    }
                                }
                            }
                            if (Status != "")
                            {
                                break;
                            }
                        }
                        #endregion

                        //获取设备状态和设备名称
                        l2.EquipmentStatus = Status == "" ? "未启动" : Status;
                        if (item.EquipmentName.Contains("#"))
                        {
                            //获取产线名截取#号后数据
                            l2.EquipmentName = item.EquipmentName.Substring(item.EquipmentName.IndexOf('#') + 1);
                        }
                        else
                        {
                            l2.EquipmentName = item.EquipmentName;
                        }
                        l2Model.Add(l2);
                    }
                    else
                    {
                        //没找到点位则赋值状态为无排产
                        l2.EquipmentStatus = "未启动";
                        if (item.EquipmentName.Contains("#"))
                        {
                            //获取产线名截取#号后数据
                            l2.EquipmentName = item.EquipmentName.Substring(item.EquipmentName.IndexOf('#') + 1);
                        }
                        else
                        {
                            l2.EquipmentName = item.EquipmentName;
                        }

                        l2Model.Add(l2);
                    }
                }
                else
                {
                    //没找到点位则赋值状态为无排产
                    l2.EquipmentStatus = "未启动";
                    if (item.EquipmentName.Contains("#"))
                    {
                        //获取产线名截取#号后数据
                        l2.EquipmentName = item.EquipmentName.Substring(item.EquipmentName.IndexOf('#') + 1);
                    }
                    else
                    {
                        l2.EquipmentName = item.EquipmentName;
                    }

                    l2Model.Add(l2);
                }


            }
            return l2Model;
        }

        public async Task<List<SIMEquipmentStatusModel>> GetEquipmentStatusList(List<EquipmentEntity> eqList)
        {
            List<SIMEquipmentStatusModel> result = new List<SIMEquipmentStatusModel>();
            if (eqList == null)
            {
                return result;
            }
            var ids = eqList?.Select(x => x.ID) ?? new List<string>();
            var performanceEntities = await (_PerformanceEntity.Db.Queryable<PerformanceEntity>()
                   .Where(x => ids.Contains(x.EquipmentId) && x.EndTimeUtc == null)
                   .OrderByDescending(x => x.StartTimeUtc)).ToListAsync();
            foreach (var item in eqList)
            {
                var performanceEntity = performanceEntities.FirstOrDefault(x => x.EquipmentId == item.ID);
                SIMEquipmentStatusModel l2 = new SIMEquipmentStatusModel();
                string Status = "未启动";
                l2.StatusHour = "0";
                if (performanceEntity != null)
                {
                    switch (performanceEntity.Categroy)
                    {
                        case "生产运行":
                            Status = "运行中";
                            break;
                        case "计划停机":
                            Status = "停机";
                            break;
                        case "非计划停机":
                            Status = "故障";
                            break;
                        default:
                            break;
                    }
                    //l2.StatusHour = (DateTime.Now - performanceEntity.StartTimeUtc.Value).TotalHours.ToString();
                }
                l2.EquipmentStatus = Status;
                if (item.EquipmentName.Contains("#"))
                {
                    //获取产线名截取#号后数据
                    l2.EquipmentName = item.EquipmentName.Substring(item.EquipmentName.IndexOf('#') + 1);
                }
                else
                {
                    l2.EquipmentName = item.EquipmentName;
                }
                result.Add(l2);
            }
            return result;
        }
        #endregion

        #region 质量及安全信息
        /// <summary>
        /// 煮缸调节调取存储过程
        /// </summary>
        public static string GetAdjustmentRate1Sql(DateTime startTime, DateTime endTime, string MaterialCode, string FormulaType, string LineName, string WorkCenter, int GroupByFormula, int GroupByWorkCenter, int GroupByMaterialCategory, int GroupByMaterial, int GroupByLine)
        {
            return string.Format("exec [dbo].[sp_Report_GetCookingAdjustInfo] '{0}','{1}','{2}','{3}','{4}','{5}','{6}','{7}','{8}','{9}','{10}'", startTime, endTime, MaterialCode, FormulaType, LineName, WorkCenter, GroupByFormula, GroupByWorkCenter, GroupByMaterialCategory, GroupByMaterial, GroupByLine);
        }
        /// <summary>
        /// 确认投诉数量
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<int> SafeMessage(KpiValueRequestSingleModel reqModel, string safeType)
        {
            var counts = 0;
            try
            {
                var eq = await _EquipmentEntity.FindEntity(p => p.EquipmentCode == reqModel.LineCode || p.ID == reqModel.LineCode);
                if (eq == null || eq.Level == "")
                {
                    return -1;
                }

                if (eq.Level == "Plant")
                {
                    var SafeMessage = await _SafetytgtViewEntity.FindList(p => p.DateOfOccurrence >= reqModel.StartTime && p.DateOfOccurrence <= reqModel.EndTime && p.EventType.Trim() == safeType);
                    counts = SafeMessage.Count();
                }
                else if (eq.Level == "Line")
                {
                    var Segment = await _EquipmentEntity.FindList(p => p.ParentId == eq.ID && p.Level == "Segment");

                    List<string> str = new List<string>();
                    str.Add(eq.ID);
                    var ids = Segment.Select(p => p.ID).ToList();
                    if (ids != null && ids.Count >= 0)
                    {
                        str.AddRange(ids);
                        var SafeMessage = await _SafetytgtViewEntity.FindList(p => p.DateOfOccurrence >= reqModel.StartTime && p.DateOfOccurrence <= reqModel.EndTime && str.Contains(p.ModelRef) && p.EventType.Trim() == safeType);
                        counts = SafeMessage.Count();
                    }
                    else
                    {
                        var SafeMessage = await _SafetytgtViewEntity.FindList(p => p.DateOfOccurrence >= reqModel.StartTime && p.DateOfOccurrence <= reqModel.EndTime && p.ModelRef.Contains(eq.ID) && p.EventType.Trim() == safeType);
                        counts = SafeMessage.Count();
                    }
                }
                //找车间
                else
                {
                    var lineid = await _EquipmentEntity.FindList(p => p.ParentId == eq.ID && p.Level == "Line");
                    var lineids = lineid.Select(p => p.ID).ToList();
                    var Segmentid = await _EquipmentEntity.FindList(p => p.ParentId == eq.ID && p.Level == "Segment");
                    var Segmentids = Segmentid.Select(p => p.ID).ToList();
                    Segmentids.AddRange(lineids);
                    if (Segmentids.Count > 0)
                    {
                        var SafeMessage = await _SafetytgtViewEntity.FindList(p => p.DateOfOccurrence >= reqModel.StartTime && p.DateOfOccurrence <= reqModel.EndTime && Segmentids.Contains(p.ModelRef) && p.EventType.Trim() == safeType);
                        counts = SafeMessage.Count();
                    }
                }
            }
            catch (Exception)
            {
                return counts;
            }

            return counts;
        }
        #region 煮缸调节率
        /// <summary>
        /// 煮缸调节率
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<decimal> CookTanlAdjust1(KpiValueRequestSingleModel reqModel)
        {
            var counts = 0.0000m;
            try
            {
                if (string.IsNullOrEmpty(reqModel.StartTime.ToString()) || string.IsNullOrEmpty(reqModel.EndTime.ToString()))
                {
                    return counts;
                }
                var sql = GetAdjustmentRate1Sql(Convert.ToDateTime(reqModel.StartTime), Convert.ToDateTime(reqModel.EndTime), "", "", "", "", 0, 0, 0, 0, 1);
                var Model = await Task.Run(() =>
                   _dal.Db.Ado.GetDataTable(sql)
                 );
                List<CookTanModel> cookTanModels = new List<CookTanModel>();
                foreach (DataRow item in Model.Rows)
                {
                    CookTanModel cookTan = new CookTanModel();
                    cookTan.Adjusted = item["Adjusted"].ToString() == "" ? "0" : item["Adjusted"].ToString();
                    cookTan.lineName = item["LineName"].ToString();
                    cookTanModels.Add(cookTan);
                }

                var eq = await _EquipmentEntity.FindEntity(p => p.EquipmentCode == reqModel.LineCode || p.ID == reqModel.LineCode);
                if (eq == null || eq.Level == "")
                {
                    return -1;
                }
                if (eq.Level == "Plant")
                {
                    if (cookTanModels.Count > 0)
                    {
                        counts = cookTanModels.Sum(p => Convert.ToDecimal(p.Adjusted));
                    }
                }
                else if (eq.Level == "Line")
                {
                    if (cookTanModels.Count > 0)
                    {
                        counts = cookTanModels.Where(p => p.lineName == eq.EquipmentName).Sum(p => Convert.ToDecimal(p.Adjusted));
                    }

                }
                //车间找产线
                else
                {
                    var Line = await _EquipmentEntity.FindList(p => p.ParentId == eq.ID && p.Level == "Line");
                    var LineNames = Line.Select(p => p.EquipmentName).ToList();
                    if (cookTanModels.Count > 0)
                    {
                        foreach (var item in LineNames)
                        {
                            counts += cookTanModels.Where(p => p.lineName == item).Sum(p => Convert.ToDecimal(p.Adjusted));
                        }
                    }
                }
            }
            catch (Exception)
            {

            }

            return counts;
        }

        /// <summary>
        /// 煮缸调节率
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<decimal> CookTanlAdjust(KpiValueRequestSingleModel reqModel)
        {
            const string parameterName = "通过结果";
            const string valueToCheck = "调节";
            const string groupName = "出料质检单";

            var result = 0.0000m;
            var v1 = 0.0000m; // 调节缸数
            var v2 = 0.0000m; // 总缸数

            try
            {
                if (string.IsNullOrEmpty(reqModel.StartTime.ToString()) || string.IsNullOrEmpty(reqModel.EndTime.ToString()))
                {
                    return result;
                }

                var eq = await _EquipmentEntity.FindEntity(p => p.EquipmentCode == reqModel.LineCode || p.ID == reqModel.LineCode);
                if (eq == null || string.IsNullOrEmpty(eq.Level))
                {
                    return -1;
                }
                SerilogServer.LogDebug("开始加载数据", "CookTanlAdjust");

                // 先筛选 ParameterGroupEntity
                var parameterGroupEntities = await _dal.Db.Queryable<ParameterGroupEntity>()
                    .Where(pg => pg.GroupName == groupName)
                    .ToListAsync();

                // 先筛选 LogsheetEntity
                var logsheetEntities = await _dal.Db.Queryable<LogsheetEntity>()
                    .Where(l => (l.Status == 1 || l.Status == 2))
                    .ToListAsync();
                var batchIds = logsheetEntities.Select(x => x.BatchId) ?? new List<string>();

                // 先筛选出batchEntity
                var batchEntities = await _dal.Db.Queryable<BatchEntity>().Where(x => batchIds.Contains(x.ID)).ToListAsync();

                // 先筛选 LogsheetDetailEntity
                var logsheetDetailEntities = await _dal.Db.Queryable<LogsheetDetailEntity>()
                    .Where(ld => !string.IsNullOrEmpty(ld.Value) &&
                                 ld.ParameterName == parameterName &&
                                 ld.ModifyDate >= reqModel.StartTime &&
                                 ld.ModifyDate <= reqModel.EndTime)
                    .ToListAsync();

                // 先筛选 ProductionOrderEntity
                var productionOrderEntities = await _dal.Db.Queryable<ProductionOrderEntity>()
                    .ToListAsync();

                // 进行 LINQ 查询
                var cookTanModels = (from b in batchEntities
                                     join l in logsheetEntities on b.ID equals l.BatchId
                                     join ld in logsheetDetailEntities on l.ID equals ld.LogsheetId
                                     join pg in parameterGroupEntities on l.ParameterGroupId equals pg.ID
                                     join po in productionOrderEntities on b.ProductionOrderId equals po.ID
                                     where ld.ParameterName == parameterName
                                     select new
                                     {
                                         BatchId = b.ID,
                                         b.ProductionOrderId,
                                         po.ProductionOrderNo,
                                         po.LineCode,
                                         ld.LogsheetId,
                                         l.Status,
                                         pg.GroupName,
                                         ld.ParameterName,
                                         ld.Value,
                                         ld.ModifyDate,
                                     }).ToList();

                SerilogServer.LogDebug("数据加载完成", "CookTanlAdjust");

                if (cookTanModels?.Count > 0)
                {
                    int CountBatches(IEnumerable<dynamic> models, Func<dynamic, bool> predicate = null)
                    {
                        var query = models.Select(x => x.BatchId);
                        if (predicate != null)
                        {
                            query = query.Where(x => models.Any(p => p.BatchId == x && predicate(p)));
                        }
                        return query.Distinct().Count();
                    }

                    switch (eq.Level)
                    {
                        case "Plant":
                            v2 = CountBatches(cookTanModels);
                            v1 = CountBatches(cookTanModels, p => p.Value == valueToCheck);
                            break;
                        case "Line":
                            v2 = CountBatches(cookTanModels, p => p.LineCode == eq.EquipmentCode);
                            v1 = CountBatches(cookTanModels, p => p.LineCode == eq.EquipmentCode && p.Value == valueToCheck);
                            break;
                        default:
                            var lineCodes = await _EquipmentEntity.Db.Queryable<EquipmentEntity>()
                                .Where(p => p.ParentId == eq.ID && p.Level == "Line")
                                .Select(p => p.EquipmentCode)
                                .ToListAsync();
                            if (lineCodes?.Count > 0)
                            {
                                v2 = CountBatches(cookTanModels, p => lineCodes.Contains(p.LineCode));
                                v1 = CountBatches(cookTanModels, p => lineCodes.Contains(p.LineCode) && p.Value == valueToCheck);
                            }
                            break;
                    }
                }

                SerilogServer.LogDebug("计算完成", "CookTanlAdjust");
            }
            catch (Exception ex)
            {
                SerilogServer.LogDebug($"CookTanlAdjust 出现错误:{ex.ToString()}", "CookTanlAdjust");
            }

            //try
            //{
            //	if (string.IsNullOrEmpty(reqModel.StartTime.ToString()) || string.IsNullOrEmpty(reqModel.EndTime.ToString()))
            //	{
            //		return result;
            //	}

            //	var eq = await _EquipmentEntity.FindEntity(p => p.EquipmentCode == reqModel.LineCode || p.ID == reqModel.LineCode);
            //	if (eq == null || string.IsNullOrEmpty(eq.Level))
            //	{
            //		return -1;
            //	}
            //	SerilogServer.LogDebug("开始加载数据", "CookTanlAdjust");

            //	var cookTanModels = await _dal.Db.Queryable<BatchEntity>()
            //		.InnerJoin<LogsheetEntity>((b, l) => b.ID == l.BatchId)
            //		.InnerJoin<LogsheetDetailEntity>((b, l, ld) => ld.LogsheetId == l.ID)
            //		.InnerJoin<ParameterGroupEntity>((b, l, ld, pg) => pg.ID == l.ParameterGroupId)
            //		.InnerJoin<ProductionOrderEntity>((b, l, ld, pg, po) => po.ID == b.ProductionOrderId)
            //		.Where((b, l, ld, pg, po) =>
            //			(l.Status == 1 || l.Status == 2) &&
            //			pg.GroupName == groupName &&
            //			ld.ParameterName == parameterName &&
            //			!string.IsNullOrEmpty(ld.Value) &&
            //			ld.ModifyDate >= reqModel.StartTime &&
            //			ld.ModifyDate <= reqModel.EndTime)
            //		.Select((b, l, ld, pg, po) => new
            //		{
            //			BatchId = b.ID,
            //			b.ProductionOrderId,
            //			po.ProductionOrderNo,
            //			po.LineCode,
            //			ld.LogsheetId,
            //			l.Status,
            //			pg.GroupName,
            //			ld.ParameterName,
            //			ld.Value,
            //			ld.ModifyDate,
            //		})
            //		.ToListAsync();


            //	SerilogServer.LogDebug("数据加载完成", "CookTanlAdjust");

            //	if (cookTanModels?.Count > 0)
            //	{
            //		int CountBatches(IEnumerable<dynamic> models, Func<dynamic, bool> predicate = null)
            //		{
            //			return predicate == null
            //				? models.GroupBy(x => x.BatchId).Count()
            //				: models.Where(predicate).GroupBy(x => x.BatchId).Count();


            //		}

            //		switch (eq.Level)
            //		{
            //			case "Plant":
            //				v2 = CountBatches(cookTanModels);
            //				v1 = CountBatches(cookTanModels, p => p.Value == valueToCheck);
            //				break;
            //			case "Line":
            //				v2 = CountBatches(cookTanModels, p => p.LineCode == eq.EquipmentCode);
            //				v1 = CountBatches(cookTanModels, p => p.LineCode == eq.EquipmentCode && p.Value == valueToCheck);
            //				break;
            //			default:
            //				var lineCodes = await _EquipmentEntity.Db.Queryable<EquipmentEntity>()
            //					.Where(p => p.ParentId == eq.ID && p.Level == "Line")
            //					.Select(p => p.EquipmentCode)
            //					.ToListAsync();
            //				if (lineCodes?.Count > 0)
            //				{
            //					v2 = CountBatches(cookTanModels, p => lineCodes.Contains(p.LineCode));
            //					v1 = CountBatches(cookTanModels, p => lineCodes.Contains(p.LineCode) && p.Value == valueToCheck);
            //				}
            //				break;
            //		}
            //	}

            //	SerilogServer.LogDebug("计算完成", "CookTanlAdjust");
            //}
            //catch (Exception ex)
            //{
            //	SerilogServer.LogError(ex, $"CookTanlAdjust 出现错误:{ex.StackTrace.ToString()}", "CookTanlAdjust");
            //}

            if (v2 != 0)
            {
                result = v1 * 100 / v2;
            }

            return result;
        }
        #endregion

        #region 产出率

        /// <summary>
        /// 产出率
        /// </summary>
        /// <returns></returns>
        public async Task<decimal> GetYield1(KpiValueRequestSingleModel reqModel)
        {
            //待确认没有单位转换后在处理
            //找已发送SAP的工单记录
            var counts = 0.000m;
            try
            {
                var eq = await _EquipmentEntity.FindEntity(p => p.EquipmentCode == reqModel.LineCode || p.ID == reqModel.LineCode);
                if (eq == null || eq.Level == "")
                {
                    return -1;
                }
                if (eq.Level == "Plant")
                {
                    var actuals = 0.000m;
                    var ConsumeActuals = 0.000m;
                    //暂时注释已发送SAP条件后期再更换
                    //var actual = await _ProducedActualViewEntity.FindList(p => p.SendExternal == 1 && p.Sapordertype == "ZXH2" && p.SendModifydate >= reqModel.StartTime && p.SendModifydate <= reqModel.StartTime);
                    var actual = await _ProducedActualViewEntity.FindList(p => p.PoStatus == 3 && p.Sapordertype == "ZXH2" && p.SendModifydate >= reqModel.StartTime && p.SendModifydate <= reqModel.EndTime);
                    if (actual.Count() > 0)
                    {
                        actuals = actual.Sum(p => p.ActualProduce);
                    }
                    //暂时注释已发送SAP条件后期再更换
                    // var ConsumeActual = await _ConsumeActualViewEntity.FindList(p => p.Sapordertype == "ZXH2" && p.PlanStartTime >= reqModel.StartTime && p.PlanStartTime <= reqModel.StartTime);
                    var ConsumeActual = await _ConsumeActualViewEntity.FindList(p => p.PoStatus == 3 && p.Sapordertype == "ZXH2" && p.PlanStartTime >= reqModel.StartTime && p.PlanStartTime <= reqModel.StartTime);
                    if (ConsumeActual.Count() > 0)
                    {
                        ConsumeActuals = actual.Sum(p => p.ActualProduce);
                    }
                    if (ConsumeActuals > 0)
                    {
                        counts = actuals / ConsumeActuals;
                    }
                }
                else
                {
                    var actuals = 0.000m;
                    var ConsumeActuals = 0.000m;
                    //暂时注释已发送SAP条件后期再更换
                    //var actual = await _ProducedActualViewEntity.FindList(p => p.SendExternal == 1 && p.Sapordertype == "ZXH2" && p.SendModifydate >= reqModel.StartTime && p.SendModifydate <= reqModel.StartTime && p.LineId == eq.ID);
                    var actual = await _ProducedActualViewEntity.FindList(p => p.PoStatus == 3 && p.Sapordertype == "ZXH2" && p.SendModifydate >= reqModel.StartTime && p.SendModifydate <= reqModel.EndTime && p.LineId == eq.ID);
                    if (actual.Count() > 0)
                    {
                        actuals = actual.Sum(p => p.ActualProduce);
                    }
                    //暂时注释已发送SAP条件后期再更换
                    //var ConsumeActual = await _ConsumeActualViewEntity.FindList(p => p.Sapordertype == "ZXH2" && p.PlanStartTime >= reqModel.StartTime && p.PlanStartTime <= reqModel.StartTime);
                    var ConsumeActual = await _ConsumeActualViewEntity.FindList(p => p.PoStatus == 3 && p.Sapordertype == "ZXH2" && p.PlanStartTime >= reqModel.StartTime && p.PlanStartTime <= reqModel.StartTime);
                    if (ConsumeActual.Count() > 0)
                    {
                        ConsumeActuals = actual.Sum(p => p.ActualProduce);
                    }
                    if (ConsumeActuals > 0)
                    {
                        counts = actuals / ConsumeActuals;
                    }
                }
            }
            catch (Exception)
            {

            }

            return counts;
        }

        /// <summary>
        /// 产出率
        /// </summary>
        /// <returns></returns>
        public async Task<decimal> GetYield(KpiValueRequestSingleModel reqModel)
        {
            //待确认没有单位转换后在处理
            //找已发送SAP的工单记录
            var produceActuals = 0.000m;
            var consumeActuals = 0.000m;
            var result = 0.000m;
            try
            {
                var eq = await _EquipmentEntity.FindEntity(p => p.EquipmentCode == reqModel.LineCode || p.ID == reqModel.LineCode);
                if (eq == null || eq.Level == "")
                {
                    return -1;
                }
                List<string> orderIds = new List<string>();
                //if (eq.Level == "Plant" || eq.Level == "Area")
                //{
                //    orderIds = await _dal.Db.Queryable<CookConfirmationEntity>().Where(x => x.Status == 1 && x.SendTime >= reqModel.StartTime && x.SendTime <= reqModel.EndTime).Select(x => x.OrderId).ToListAsync();
                //}
                //else
                //{
                //    orderIds = await _dal.Db.Queryable<CookConfirmationEntity>()
                //       .InnerJoin<ProductionOrderEntity>((x, p) => x.OrderId == p.ID)
                //       .Where((x, p) => x.Status == 1 && x.SendTime >= reqModel.StartTime && x.SendTime <= reqModel.EndTime && p.LineCode == eq.EquipmentCode)
                //       .Select((x, p) => x.OrderId)
                //       .ToListAsync();
                //}
				if (eq.Level == "Plant" || eq.Level == "Area")
				{
					orderIds = await _dal.Db.Queryable<ProductionOrderEntity>().Where(x => x.PoStatus != "4" && x.PlanDate >= reqModel.StartTime && x.PlanDate <= reqModel.EndTime).Select(x => x.ID).ToListAsync();
				}
				else
				{
					orderIds = await _dal.Db.Queryable<ProductionOrderEntity>().Where(x => x.PoStatus != "4" && x.PlanDate >= reqModel.StartTime && x.PlanDate <= reqModel.EndTime && x.LineCode == eq.EquipmentCode).Select(x => x.ID).ToListAsync();
				}
				if (orderIds?.Count > 0)
                {
                    produceActuals = await _dal.Db.Queryable<PoProducedActualEntity>().Where(x => orderIds.Contains(x.ProductionOrderId)).SumAsync<decimal?>("Quantity") ?? produceActuals;
                    consumeActuals = await _dal.Db.Queryable<PoConsumeActualEntity>().Where(x => orderIds.Contains(x.ProductionOrderId)).SumAsync<decimal?>("Quantity") ?? consumeActuals;
                    if (consumeActuals != 0)
                    {
                        result = produceActuals / consumeActuals * 100;
                    }
                }
            }
            catch (Exception ex)
            {

            }
            return result;
        }

        #endregion

        #region 喉头产出率
        public async Task<decimal> GetThroatRate(KpiValueRequestSingleModel reqModel)
        {
            var counts = 0.000m;
            try
            {
                //找产线
                var eq = await _EquipmentEntity.FindEntity(p => p.EquipmentCode == reqModel.LineCode || p.ID == reqModel.LineCode);
                if (eq == null || eq.Level == "")
                {
                    return -1;
                }
                if (eq != null)
                {
                    //找工单状态为已完成=3、筛选工单结束时间、制造工单、对应产线
                    var total = 0.000m;
                    var throatQty = 0.000m;
                    var order = await _ProductionOrderEntity.FindList(p => p.PoStatus == "3" && p.EndTime >= reqModel.StartTime && p.EndTime >= reqModel.EndTime && p.SapOrderType == "ZXH2" && p.LineCode == eq.EquipmentCode);
                    if (order.Count > 0)
                    {
                        //找对应工单的实际产出
                        var actual = await _PoProducedActualEntity.FindList(p => order.Any(s => s.ID == p.ProductionOrderId));
                        total = Convert.ToDecimal(actual.Sum(p => p.Quantity));
                        //找产出区域是三楼、四楼喉头仓的数据

                        var throatRoom = await _EquipmentEntity.FindList(p => p.EquipmentCode == "ThirdFloorThroatRoom" || p.EquipmentCode == "ForthFloorThroatRoom");
                        if (throatRoom.Count > 0)
                        {
                            var throat = actual.Where(p => throatRoom.Any(s => s.ID == p.DesinationEquipmentId)).ToList();
                            throatQty = Convert.ToDecimal(throat.Sum(p => p.Quantity));
                        }
                        if (total > 0)
                        {
                            counts = (throatQty / total) * 100;
                        }
                    }


                }
            }
            catch (Exception)
            {

            }

            return counts;
        }
        #endregion

        #region 原料损耗率
        /*public async Task<decimal> GetTMaterialLoss(KpiValueRequestSingleModel reqModel)
        {
            //找时间范围内的工单
            var order = await _ProductionOrderEntity.FindList(p=>p.SapOrderType=="ZXH2" && p.QATime>= reqModel.StartTime && p.QATime <= reqModel.EndTime);
            if (order != null)
            {
                var ConsumeActual = await _PoConsumeActualEntity.FindList(p => order.Any(s => s.ID == p.ProductionOrderId));

            }
            

        }*/
        #endregion
        #endregion

        #region 能源单耗信息

        #region 用水单耗
        /// <summary>
        /// 用水单耗
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<decimal> WaterConsumption(KpiValueRequestSingleModel reqModel)
        {
            var counts = 0.000m;
            var actuals = 0.000m;
            var water = 0.000m;
            try
            {
                if (reqModel.LineCode == "" || reqModel.LineCode == null)
                {
                    return -1;
                }

                var eq = await _EquipmentEntity.FindEntity(p => p.EquipmentCode == reqModel.LineCode || p.ID == reqModel.LineCode);
                if (eq != null)
                {
                    if (eq.Level == "Plant")
                    {
                        //暂时注释已发送SAP条件后期再更换
                        //var actual = await _ProducedActualViewEntity.FindList(p => p.SendExternal == 1 && p.Sapordertype == "ZXH2" && p.SendModifydate >= reqModel.StartTime && p.SendModifydate <= reqModel.StartTime);
                        var actual = await _ProducedActualViewEntity.FindList(p => p.PoStatus == 3 && p.Sapordertype == "ZXH2" && p.SendModifydate >= reqModel.StartTime && p.SendModifydate <= reqModel.EndTime);
                        if (actual.Count() > 0)
                        {
                            actuals = actual.Sum(p => p.ActualProduce);
                        }

                        var energy = await _EnergyByorderEntity.FindList(p => p.ExecutionId != null);
                        var execyion = await _PoProducedExecutionEntity.FindList(p => energy.Any(s => s.ExecutionId == p.ID));
                        var pro = await _ProductionOrderEntity.FindList(p => execyion.Any(s => s.ProductionOrderId == p.ID));
                        var results = (from a in energy
                                       join b in execyion on a.ExecutionId equals b.ID
                                       join c in pro on b.ProductionOrderId equals c.ID
                                       select new
                                       {
                                           a.EnergyType,
                                           a.EnergyQty,
                                           c.ProductionOrderNo

                                       }).ToList();

                        var WaterList = results.Where(p => actual.Any(s => s.ProductionOrderNo == p.ProductionOrderNo && p.EnergyType == "水")).ToList();
                        water = WaterList.Sum(p => p.EnergyQty);
                        if (actuals > 0)
                        {
                            counts = water * 1000 / actuals;
                        }
                    }
                    else
                    {
                        //暂时注释已发送SAP条件后期再更换
                        // var actual = await _ProducedActualViewEntity.FindList(p => p.SendExternal == 1 && p.Sapordertype == "ZXH2" && p.SendModifydate >= reqModel.StartTime && p.SendModifydate <= reqModel.StartTime && p.LineId == eq.ID);
                        var actual = await _ProducedActualViewEntity.FindList(p => p.PoStatus == 3 && p.Sapordertype == "ZXH2" && p.SendModifydate >= reqModel.StartTime && p.SendModifydate <= reqModel.EndTime && p.LineId == eq.ID);
                        if (actual.Count() > 0)
                        {
                            actuals = actual.Sum(p => p.ActualProduce);
                        }

                        var energy = await _EnergyByorderEntity.FindList(p => p.ExecutionId != null);
                        var execyion = await _PoProducedExecutionEntity.FindList(p => energy.Any(s => s.ExecutionId == p.ID));
                        var pro = await _ProductionOrderEntity.FindList(p => execyion.Any(s => s.ProductionOrderId == p.ID));
                        var results = (from a in energy
                                       join b in execyion on a.ExecutionId equals b.ID
                                       join c in pro on b.ProductionOrderId equals c.ID
                                       select new
                                       {
                                           a.EnergyType,
                                           a.EnergyQty,
                                           c.ProductionOrderNo

                                       }).ToList();

                        var WaterList = results.Where(p => actual.Any(s => s.ProductionOrderNo == p.ProductionOrderNo && p.EnergyType == "水")).ToList();
                        water = WaterList.Sum(p => p.EnergyQty);
                        if (actuals > 0)
                        {
                            counts = water * 1000 / actuals;
                        }
                    }
                }

            }
            catch (Exception)
            {

            }
            return counts;
        }
        #endregion

        #region 用电单耗
        /// <summary>
        /// 用电单耗
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<decimal> ElectricityConsumption(KpiValueRequestSingleModel reqModel)
        {
            var counts = 0.000m;
            var actuals = 0.000m;
            var water = 0.000m;
            try
            {
                if (reqModel.LineCode == "" || reqModel.LineCode == null)
                {
                    return -1;
                }
                var eq = await _EquipmentEntity.FindEntity(p => p.EquipmentCode == reqModel.LineCode || p.ID == reqModel.LineCode);
                if (eq != null)
                {
                    if (eq.Level == "Plant")
                    {
                        //暂时注释已发送SAP条件后期再更换
                        //var actual = await _ProducedActualViewEntity.FindList(p => p.SendExternal == 1 && p.Sapordertype == "ZXH2" && p.SendModifydate >= reqModel.StartTime && p.SendModifydate <= reqModel.StartTime);
                        var actual = await _ProducedActualViewEntity.FindList(p => p.PoStatus == 3 && p.Sapordertype == "ZXH2" && p.SendModifydate >= reqModel.StartTime && p.SendModifydate <= reqModel.EndTime);
                        if (actual.Count() > 0)
                        {
                            actuals = actual.Sum(p => p.ActualProduce);
                        }

                        var energy = await _EnergyByorderEntity.FindList(p => p.ExecutionId != null);
                        var execyion = await _PoProducedExecutionEntity.FindList(p => energy.Any(s => s.ExecutionId == p.ID));
                        var pro = await _ProductionOrderEntity.FindList(p => execyion.Any(s => s.ProductionOrderId == p.ID));
                        var results = (from a in energy
                                       join b in execyion on a.ExecutionId equals b.ID
                                       join c in pro on b.ProductionOrderId equals c.ID
                                       select new
                                       {
                                           a.EnergyType,
                                           a.EnergyQty,
                                           c.ProductionOrderNo

                                       }).ToList();

                        var WaterList = results.Where(p => actual.Any(s => s.ProductionOrderNo == p.ProductionOrderNo && p.EnergyType == "电")).ToList();
                        water = WaterList.Sum(p => p.EnergyQty);
                        if (actuals > 0)
                        {
                            counts = water * 1000 / actuals;
                        }
                    }
                    else
                    {
                        //暂时注释已发送SAP条件后期再更换
                        //var actual = await _ProducedActualViewEntity.FindList(p => p.SendExternal == 1 && p.Sapordertype == "ZXH2" && p.SendModifydate >= reqModel.StartTime && p.SendModifydate <= reqModel.StartTime && p.LineId == eq.ID);
                        var actual = await _ProducedActualViewEntity.FindList(p => p.PoStatus == 3 && p.Sapordertype == "ZXH2" && p.SendModifydate >= reqModel.StartTime && p.SendModifydate <= reqModel.EndTime && p.LineId == eq.ID);
                        if (actual.Count() > 0)
                        {
                            actuals = actual.Sum(p => p.ActualProduce);
                        }

                        var energy = await _EnergyByorderEntity.FindList(p => p.ExecutionId != null);
                        var execyion = await _PoProducedExecutionEntity.FindList(p => energy.Any(s => s.ExecutionId == p.ID));
                        var pro = await _ProductionOrderEntity.FindList(p => execyion.Any(s => s.ProductionOrderId == p.ID));
                        var results = (from a in energy
                                       join b in execyion on a.ExecutionId equals b.ID
                                       join c in pro on b.ProductionOrderId equals c.ID
                                       select new
                                       {
                                           a.EnergyType,
                                           a.EnergyQty,
                                           c.ProductionOrderNo

                                       }).ToList();

                        var WaterList = results.Where(p => actual.Any(s => s.ProductionOrderNo == p.ProductionOrderNo && p.EnergyType == "电")).ToList();
                        water = WaterList.Sum(p => p.EnergyQty);
                        if (actuals > 0)
                        {
                            counts = water * 1000 / actuals;
                        }
                    }
                }


            }
            catch (Exception)
            {

            }
            return counts;
        }
        #endregion

        #region 蒸汽单耗
        /// <summary>
        /// 蒸汽单耗
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<decimal> SteamConsumption(KpiValueRequestSingleModel reqModel)
        {
            var counts = 0.000m;
            var actuals = 0.000m;
            var water = 0.000m;
            try
            {
                if (reqModel.LineCode == "" || reqModel.LineCode == null)
                {
                    return -1;
                }
                var eq = await _EquipmentEntity.FindEntity(p => p.EquipmentCode == reqModel.LineCode || p.ID == reqModel.LineCode);
                if (eq != null)
                {
                    if (eq.Level == "Plant")
                    {
                        //暂时注释已发送SAP条件后期再更换
                        //var actual = await _ProducedActualViewEntity.FindList(p => p.SendExternal == 1 && p.Sapordertype == "ZXH2" && p.SendModifydate >= reqModel.StartTime && p.SendModifydate <= reqModel.StartTime);
                        var actual = await _ProducedActualViewEntity.FindList(p => p.PoStatus == 3 && p.Sapordertype == "ZXH2" && p.SendModifydate >= reqModel.StartTime && p.SendModifydate <= reqModel.EndTime);
                        if (actual.Count() > 0)
                        {
                            actuals = actual.Sum(p => p.ActualProduce);
                        }

                        var energy = await _EnergyByorderEntity.FindList(p => p.ExecutionId != null);
                        var execyion = await _PoProducedExecutionEntity.FindList(p => energy.Any(s => s.ExecutionId == p.ID));
                        var pro = await _ProductionOrderEntity.FindList(p => execyion.Any(s => s.ProductionOrderId == p.ID));
                        var results = (from a in energy
                                       join b in execyion on a.ExecutionId equals b.ID
                                       join c in pro on b.ProductionOrderId equals c.ID
                                       select new
                                       {
                                           a.EnergyType,
                                           a.EnergyQty,
                                           c.ProductionOrderNo

                                       }).ToList();

                        var WaterList = results.Where(p => actual.Any(s => s.ProductionOrderNo == p.ProductionOrderNo && p.EnergyType == "蒸汽")).ToList();
                        water = WaterList.Sum(p => p.EnergyQty);
                        if (actuals > 0)
                        {
                            counts = water * 1000 / actuals;
                        }
                    }
                    else
                    {
                        //暂时注释已发送SAP条件后期再更换
                        //var actual = await _ProducedActualViewEntity.FindList(p => p.SendExternal == 1 && p.Sapordertype == "ZXH2" && p.SendModifydate >= reqModel.StartTime && p.SendModifydate <= reqModel.StartTime && p.LineId == eq.ID);
                        var actual = await _ProducedActualViewEntity.FindList(p => p.PoStatus == 3 && p.Sapordertype == "ZXH2" && p.SendModifydate >= reqModel.StartTime && p.SendModifydate <= reqModel.EndTime && p.LineId == eq.ID);
                        if (actual.Count() > 0)
                        {
                            actuals = actual.Sum(p => p.ActualProduce);
                        }

                        var energy = await _EnergyByorderEntity.FindList(p => p.ExecutionId != null);
                        var execyion = await _PoProducedExecutionEntity.FindList(p => energy.Any(s => s.ExecutionId == p.ID));
                        var pro = await _ProductionOrderEntity.FindList(p => execyion.Any(s => s.ProductionOrderId == p.ID));
                        var results = (from a in energy
                                       join b in execyion on a.ExecutionId equals b.ID
                                       join c in pro on b.ProductionOrderId equals c.ID
                                       select new
                                       {
                                           a.EnergyType,
                                           a.EnergyQty,
                                           c.ProductionOrderNo

                                       }).ToList();

                        var WaterList = results.Where(p => actual.Any(s => s.ProductionOrderNo == p.ProductionOrderNo && p.EnergyType == "蒸汽")).ToList();
                        water = WaterList.Sum(p => p.EnergyQty);
                        if (actuals > 0)
                        {
                            counts = water * 1000 / actuals;
                        }
                    }
                }

            }
            catch (Exception)
            {

            }
            return counts;
        }
        #endregion
        #endregion

        public class CookTanModel
        {
            public string Adjusted { get; set; }
            public string lineName { get; set; }
        }



        #endregion

        #region SIM2 包装
        #region 物料组损耗
        /// <summary>
        /// 物料组损耗
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<List<MaterialGroupLossModel>> MaterialGroupLoss(KpiValueRequestSingleModel reqModel)
        {
            var eq = await _EquipmentEntity.FindEntity(p => p.EquipmentCode == reqModel.LineCode || p.ID == reqModel.LineCode);
            List<MaterialGroupLossModel> materialGroups = new List<MaterialGroupLossModel>();
            try
            {
                var ConsumeActual = await _ConsumeActualViewEntity.FindList(p => p.Sapordertype == "ZXH1" && p.PlanDate >= reqModel.StartTime && p.PlanDate <= reqModel.EndTime && p.Resource == eq.EquipmentName);

                if (ConsumeActual.Count > 0)
                {
                    var consumeGrooup = ConsumeActual.GroupBy(p => new { p.Kschl1 }).Select(p => new { ActualConsume = p.Sum(p => p.ActualConsume), Quantity = p.Sum(p => p.Quantity), Kschl1 = p.Key.Kschl1 }).ToList();
                    foreach (var item in consumeGrooup)
                    {
                        MaterialGroupLossModel materialGroupLossModel = new MaterialGroupLossModel();
                        materialGroupLossModel.MaterialGroup = item.Kschl1;
                        materialGroupLossModel.BOMLoss = item.Quantity;
                        if (item.Quantity > 0)
                        {
                            materialGroupLossModel.lossRate = Math.Round((decimal)((item.ActualConsume - item.Quantity) / item.Quantity), 3) * 100;
                            materialGroupLossModel.DataState = "实际用量：" + item.ActualConsume + ",不带损耗标准用量" + item.Quantity;
                        }
                        else
                        {
                            materialGroupLossModel.lossRate = 0;
                        }

                        materialGroups.Add(materialGroupLossModel);

                    }
                }
            }
            catch (Exception)
            {

            }
            materialGroups = materialGroups.OrderByDescending(p => p.lossRate).ToList();
            return materialGroups;
        }
        #endregion

        #region 设备管理信息


        /// <summary>
        /// 设备管理信息
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<List<SIMEquipmentStatusModel>> EquipmentStatus(KpiValueRequestSingleModel reqModel)
        {
            List<SIMEquipmentStatusModel> sIMEquipmentStatusModels = new List<SIMEquipmentStatusModel>();
            //查找产线
            var lineModel = await _EquipmentEntity.FindEntity(p => p.ID == reqModel.LineCode || p.EquipmentCode == reqModel.LineCode && p.Deleted == 0 && p.Enabled == 1);
            if (lineModel != null)
            {
                var eqList = await _EquipmentEntity.FindList(p => p.LineId == lineModel.ID && p.Deleted == 0 && p.Enabled == 1 && (p.EquipmentName.Contains("灌装机") || p.EquipmentName.Contains("贴标机") || p.EquipmentName.Contains("装箱机") || p.EquipmentName.Contains("码垛机")));
                return await GetEquipmentStatusLists(eqList);

                foreach (var item in eqList)
                {
                    SIMEquipmentStatusModel sIMEquipmentStatus = new SIMEquipmentStatusModel();
                    if (item.EquipmentName.Contains("灌装机"))
                    {
                        var Influxdbquantity = 0;
                        string Status = "";
                        //查找设备状态点位
                        var opc = await _InfluxOpcTagEntit.FindEntity(p => p.EquipmentId == item.ID && p.Name == "设备状态");
                        if (opc != null)
                        {
                            try
                            {
                                //获取点位状态
                                Influxdbquantity = (int)Convert.ToDecimal((await _IInfluxDbServices.GetLastInfluxData(DateTime.Now.AddMinutes(-10), DateTime.Now, null, opc.Tag))?.Value);
                            }
                            catch (Exception)
                            {

                                Influxdbquantity = 0;
                            }


                            #region 截取字符进行状态比对
                            //按设定找点位描述最后一位#号
                            var str = opc.Describe.LastIndexOf("#");
                            if (str != -1)
                            {
                                //截取#后的数据
                                var str1 = opc.Describe.Substring(str + 1);
                                //按;分割
                                string[] parts = str1.Split(";");
                                for (int j = 0; j < parts.Length; j++)
                                {
                                    //按=分割
                                    string[] parts2 = parts[j].Split("=");
                                    if (Influxdbquantity.ToString() == parts2[1])
                                    {
                                        Status = parts2[0];
                                    }
                                    //值存在&符号走
                                    if (parts2[1].Contains("&"))
                                    {
                                        //按&符号截取
                                        string[] parts1 = parts2[1].Split("&");
                                        for (int h = 0; h < parts1.Length; h++)
                                        {
                                            //当获取的点位值和查到的描述一致返回状态描述
                                            if (parts1[h] == Influxdbquantity.ToString())
                                            {
                                                Status = parts2[0];
                                                break;
                                            }
                                        }
                                    }
                                    if (Status != "")
                                    {
                                        break;
                                    }
                                }
                                #endregion

                                //获取设备状态和设备名称
                                sIMEquipmentStatus.EquipmentStatus = Status == "" ? "未启动" : Status;

                                if (item.EquipmentName.Contains("#"))
                                {
                                    //获取产线名截取#号后数据
                                    sIMEquipmentStatus.EquipmentName = item.EquipmentName.Substring(0, item.EquipmentName.IndexOf('#'));
                                }
                                else
                                {
                                    sIMEquipmentStatus.EquipmentName = item.EquipmentName;
                                }
                                sIMEquipmentStatusModels.Add(sIMEquipmentStatus);
                            }
                            else
                            {
                                //没找到点位则赋值状态为无排产
                                sIMEquipmentStatus.EquipmentStatus = "未启动";
                                if (item.EquipmentName.Contains("#"))
                                {
                                    //获取产线名截取#号后数据
                                    sIMEquipmentStatus.EquipmentName = item.EquipmentName.Substring(0, item.EquipmentName.IndexOf('#'));
                                }
                                else
                                {
                                    sIMEquipmentStatus.EquipmentName = item.EquipmentName;
                                }

                                sIMEquipmentStatusModels.Add(sIMEquipmentStatus);
                            }

                        }
                        else
                        {
                            //没找到点位则赋值状态为无排产
                            sIMEquipmentStatus.EquipmentStatus = "未启动";

                            if (item.EquipmentName.Contains("#"))
                            {
                                //获取产线名截取#号后数据
                                sIMEquipmentStatus.EquipmentName = item.EquipmentName.Substring(0, item.EquipmentName.IndexOf('#'));
                            }
                            else
                            {
                                sIMEquipmentStatus.EquipmentName = item.EquipmentName;
                            }

                            sIMEquipmentStatusModels.Add(sIMEquipmentStatus);
                        }
                    }
                    else if (item.EquipmentName.Contains("贴标机"))
                    {
                        var Influxdbquantity = 0;
                        string Status = "";
                        //查找设备状态点位
                        var opc = await _InfluxOpcTagEntit.FindEntity(p => p.EquipmentId == item.ID && p.Name == "设备状态");
                        if (opc != null)
                        {
                            try
                            {
                                //获取点位状态
                                Influxdbquantity = (int)Convert.ToDecimal((await _IInfluxDbServices.GetLastInfluxData(DateTime.Now.AddMinutes(-10), DateTime.Now, null, opc.Tag))?.Value);
                            }
                            catch (Exception)
                            {

                                Influxdbquantity = 0;
                            }
                            #region 截取字符进行状态比对
                            //按设定找点位描述最后一位#号
                            var str = opc.Describe.LastIndexOf("#");
                            if (str != -1)
                            {
                                //截取#后的数据
                                var str1 = opc.Describe.Substring(str + 1);
                                //按;分割
                                string[] parts = str1.Split(";");
                                for (int j = 0; j < parts.Length; j++)
                                {
                                    //按=分割
                                    string[] parts2 = parts[j].Split("=");
                                    if (Influxdbquantity.ToString() == parts2[1])
                                    {
                                        Status = parts2[0];
                                    }
                                    //值存在&符号走
                                    if (parts2[1].Contains("&"))
                                    {
                                        //按&符号截取
                                        string[] parts1 = parts2[1].Split("&");
                                        for (int h = 0; h < parts1.Length; h++)
                                        {
                                            //当获取的点位值和查到的描述一致返回状态描述
                                            if (parts1[h] == Influxdbquantity.ToString())
                                            {
                                                Status = parts2[0];
                                                break;
                                            }
                                        }
                                    }
                                    if (Status != "")
                                    {
                                        break;
                                    }
                                }
                                #endregion

                                //获取设备状态和设备名称
                                sIMEquipmentStatus.EquipmentStatus = Status == "" ? "未启动" : Status;
                                if (item.EquipmentName.Contains("#"))
                                {
                                    //获取产线名截取#号后数据
                                    sIMEquipmentStatus.EquipmentName = item.EquipmentName.Substring(0, item.EquipmentName.IndexOf('#'));
                                }
                                else
                                {
                                    sIMEquipmentStatus.EquipmentName = item.EquipmentName;
                                }
                                sIMEquipmentStatusModels.Add(sIMEquipmentStatus);
                            }
                            else
                            {
                                //没找到点位则赋值状态为无排产
                                sIMEquipmentStatus.EquipmentStatus = "未启动";
                                if (item.EquipmentName.Contains("#"))
                                {
                                    //获取产线名截取#号后数据
                                    sIMEquipmentStatus.EquipmentName = item.EquipmentName.Substring(0, item.EquipmentName.IndexOf('#'));
                                }
                                else
                                {
                                    sIMEquipmentStatus.EquipmentName = item.EquipmentName;
                                }

                                sIMEquipmentStatusModels.Add(sIMEquipmentStatus);
                            }

                        }
                        else
                        {
                            //没找到点位则赋值状态为无排产
                            sIMEquipmentStatus.EquipmentStatus = "未启动";
                            if (item.EquipmentName.Contains("#"))
                            {
                                //获取产线名截取#号后数据
                                sIMEquipmentStatus.EquipmentName = item.EquipmentName.Substring(0, item.EquipmentName.IndexOf('#'));
                            }
                            else
                            {
                                sIMEquipmentStatus.EquipmentName = item.EquipmentName;
                            }

                            sIMEquipmentStatusModels.Add(sIMEquipmentStatus);
                        }
                    }
                    else if (item.EquipmentName.Contains("装箱机"))
                    {
                        var Influxdbquantity = 0;
                        string Status = "";
                        //查找设备状态点位
                        var opc = await _InfluxOpcTagEntit.FindEntity(p => p.EquipmentId == item.ID && p.Name == "设备状态");
                        if (opc != null)
                        {
                            try
                            {
                                //获取点位状态
                                Influxdbquantity = (int)Convert.ToDecimal((await _IInfluxDbServices.GetLastInfluxData(DateTime.Now.AddMinutes(-10), DateTime.Now, null, opc.Tag))?.Value);
                            }
                            catch (Exception)
                            {

                                Influxdbquantity = 0;
                            }
                            #region 截取字符进行状态比对
                            //按设定找点位描述最后一位#号
                            var str = opc.Describe.LastIndexOf("#");
                            if (str != -1)
                            {
                                //截取#后的数据
                                var str1 = opc.Describe.Substring(str + 1);
                                //按;分割
                                string[] parts = str1.Split(";");
                                for (int j = 0; j < parts.Length; j++)
                                {
                                    //按=分割
                                    string[] parts2 = parts[j].Split("=");
                                    if (Influxdbquantity.ToString() == parts2[1])
                                    {
                                        Status = parts2[0];
                                    }
                                    //值存在&符号走
                                    if (parts2[1].Contains("&"))
                                    {
                                        //按&符号截取
                                        string[] parts1 = parts2[1].Split("&");
                                        for (int h = 0; h < parts1.Length; h++)
                                        {
                                            //当获取的点位值和查到的描述一致返回状态描述
                                            if (parts1[h] == Influxdbquantity.ToString())
                                            {
                                                Status = parts2[0];
                                                break;
                                            }
                                        }
                                    }
                                    if (Status != "")
                                    {
                                        break;
                                    }
                                }
                                #endregion

                                //获取设备状态和设备名称
                                sIMEquipmentStatus.EquipmentStatus = Status == "" ? "未启动" : Status;
                                if (item.EquipmentName.Contains("#"))
                                {
                                    //获取产线名截取#号后数据
                                    sIMEquipmentStatus.EquipmentName = item.EquipmentName.Substring(0, item.EquipmentName.IndexOf('#'));
                                }
                                else
                                {
                                    sIMEquipmentStatus.EquipmentName = item.EquipmentName;
                                }
                                sIMEquipmentStatusModels.Add(sIMEquipmentStatus);
                            }
                            else
                            {
                                //没找到点位则赋值状态为无排产
                                sIMEquipmentStatus.EquipmentStatus = "未启动";
                                if (item.EquipmentName.Contains("#"))
                                {
                                    //获取产线名截取#号后数据
                                    sIMEquipmentStatus.EquipmentName = item.EquipmentName.Substring(0, item.EquipmentName.IndexOf('#'));
                                }
                                else
                                {
                                    sIMEquipmentStatus.EquipmentName = item.EquipmentName;
                                }

                                sIMEquipmentStatusModels.Add(sIMEquipmentStatus);
                            }

                        }
                        else
                        {
                            //没找到点位则赋值状态为无排产
                            sIMEquipmentStatus.EquipmentStatus = "未启动";
                            if (item.EquipmentName.Contains("#"))
                            {
                                //获取产线名截取#号后数据
                                sIMEquipmentStatus.EquipmentName = item.EquipmentName.Substring(0, item.EquipmentName.IndexOf('#'));
                            }
                            else
                            {
                                sIMEquipmentStatus.EquipmentName = item.EquipmentName;
                            }

                            sIMEquipmentStatusModels.Add(sIMEquipmentStatus);
                        }
                    }
                    else if (item.EquipmentName.Contains("码垛机"))
                    {
                        var Influxdbquantity = 0;
                        string Status = "";
                        //查找设备状态点位
                        var opc = await _InfluxOpcTagEntit.FindEntity(p => p.EquipmentId == item.ID && p.Name == "设备状态");
                        if (opc != null)
                        {
                            try
                            {
                                //获取点位状态
                                Influxdbquantity = (int)Convert.ToDecimal((await _IInfluxDbServices.GetLastInfluxData(DateTime.Now.AddMinutes(-10), DateTime.Now, null, opc.Tag))?.Value);
                            }
                            catch (Exception)
                            {

                                Influxdbquantity = 0;
                            }
                            #region 截取字符进行状态比对
                            //按设定找点位描述最后一位#号
                            var str = opc.Describe.LastIndexOf("#");
                            if (str != -1)
                            {
                                //截取#后的数据
                                var str1 = opc.Describe.Substring(str + 1);
                                //按;分割
                                string[] parts = str1.Split(";");
                                for (int j = 0; j < parts.Length; j++)
                                {
                                    //按=分割
                                    string[] parts2 = parts[j].Split("=");
                                    if (Influxdbquantity.ToString() == parts2[1])
                                    {
                                        Status = parts2[0];
                                    }
                                    //值存在&符号走
                                    if (parts2[1].Contains("&"))
                                    {
                                        //按&符号截取
                                        string[] parts1 = parts2[1].Split("&");
                                        for (int h = 0; h < parts1.Length; h++)
                                        {
                                            //当获取的点位值和查到的描述一致返回状态描述
                                            if (parts1[h] == Influxdbquantity.ToString())
                                            {
                                                Status = parts2[0];
                                                break;
                                            }
                                        }
                                    }
                                    if (Status != "")
                                    {
                                        break;
                                    }
                                }
                                #endregion

                                //获取设备状态和设备名称
                                sIMEquipmentStatus.EquipmentStatus = Status == "" ? "未启动" : Status;
                                if (item.EquipmentName.Contains("#"))
                                {
                                    //获取产线名截取#号后数据
                                    sIMEquipmentStatus.EquipmentName = item.EquipmentName.Substring(0, item.EquipmentName.IndexOf('#'));
                                }
                                else
                                {
                                    sIMEquipmentStatus.EquipmentName = item.EquipmentName;
                                }
                                sIMEquipmentStatusModels.Add(sIMEquipmentStatus);
                            }
                            else
                            {
                                //没找到点位则赋值状态为无排产
                                sIMEquipmentStatus.EquipmentStatus = "未启动";
                                if (item.EquipmentName.Contains("#"))
                                {
                                    //获取产线名截取#号后数据
                                    sIMEquipmentStatus.EquipmentName = item.EquipmentName.Substring(0, item.EquipmentName.IndexOf('#'));
                                }
                                else
                                {
                                    sIMEquipmentStatus.EquipmentName = item.EquipmentName;
                                }

                                sIMEquipmentStatusModels.Add(sIMEquipmentStatus);
                            }
                        }
                        else
                        {
                            //没找到点位则赋值状态为无排产
                            sIMEquipmentStatus.EquipmentStatus = "未启动";
                            if (item.EquipmentName.Contains("#"))
                            {
                                //获取产线名截取#号后数据
                                sIMEquipmentStatus.EquipmentName = item.EquipmentName.Substring(0, item.EquipmentName.IndexOf('#'));
                            }
                            else
                            {
                                sIMEquipmentStatus.EquipmentName = item.EquipmentName;
                            }

                            sIMEquipmentStatusModels.Add(sIMEquipmentStatus);
                        }
                    }
                }
            }
            return sIMEquipmentStatusModels;
        }


        public async Task<List<SIMEquipmentStatusModel>> GetEquipmentStatusLists(List<EquipmentEntity> eqList)
        {
            List<SIMEquipmentStatusModel> result = new List<SIMEquipmentStatusModel>();
            if (eqList == null)
            {
                return result;
            }
            var ids = eqList?.Select(x => x.ID) ?? new List<string>();
            var performanceEntities = await (_PerformanceEntity.Db.Queryable<PerformanceEntity>()
                   .Where(x => ids.Contains(x.EquipmentId) && x.EndTimeUtc == null)
                   .OrderByDescending(x => x.StartTimeUtc)).ToListAsync();
            foreach (var item in eqList)
            {
                var performanceEntity = performanceEntities.FirstOrDefault(x => x.EquipmentId == item.ID);
                SIMEquipmentStatusModel l2 = new SIMEquipmentStatusModel();
                string Status = "未启动";
                l2.StatusHour = "0";
                if (performanceEntity != null)
                {
                    switch (performanceEntity.Categroy)
                    {
                        case "生产运行":
                            Status = "运行中";
                            break;
                        case "计划停机":
                            Status = "停机";
                            break;
                        case "非计划停机":
                            Status = "故障";
                            break;
                        default:
                            break;
                    }
                }
                l2.EquipmentStatus = Status;
                if (item.EquipmentName.Contains("#"))
                {
                    //获取产线名截取#号后数据
                    l2.EquipmentName = item.EquipmentName.Substring(0, item.EquipmentName.IndexOf('#'));
                }
                else
                {
                    l2.EquipmentName = item.EquipmentName;
                }
                result.Add(l2);
            }
            return result;
        }
        #endregion

        #region 工单一次性完成率
        /// <summary>
        ///工单一次性完成率
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<decimal> PoCompletioRate(KpiValueRequestSingleModel reqModel)
        {
            var counts = 0m;
            try
            {
                if (reqModel.LineCode == "" || reqModel.LineCode == null)
                {
                    return -1;
                }
                var eq = await _EquipmentEntity.FindEntity(p => p.EquipmentCode == reqModel.LineCode || p.ID == reqModel.LineCode);
                if (eq != null)
                {
                    var CompleteAtOnceAll = await _dalProductionOrderEntity.FindList(p => p.PlanDate >= reqModel.StartTime && p.PlanDate <= reqModel.EndTime && p.PoStatus == "3" && p.LineCode == eq.EquipmentCode);
                    var finishCount = CompleteAtOnceAll.Where(p => p.Reason == "工单一次性完成" || p.Reason == "CompleteAtOnce").Count();

                    if (CompleteAtOnceAll.Count > 0)
                    {

                        decimal resultValue = Convert.ToDecimal(finishCount) / Convert.ToDecimal(CompleteAtOnceAll.Count) * 100;

                        return resultValue;
                    }


                }

            }
            catch (Exception)
            {

            }

            return counts;
        }
        #endregion

        #region OEE 、表现性、有效性、品质性
        public async Task<decimal> GetOEEDate(KpiValueRequestSingleModel reqModel)
        {

            if (reqModel.StartTime.ToString() == "" || reqModel.EndTime == null)
            {
                return -1;
            }
            if (string.IsNullOrEmpty(reqModel.LineCode))
            {
                return -1;
            }
            decimal OEE = 0;
            decimal V1 = 0;
            decimal V2 = 0;
            decimal V3 = 0;
            var eq = await _EquipmentEntity.FindEntity(p => p.EquipmentCode == reqModel.LineCode || p.ID == reqModel.LineCode);
            if (eq != null)
            {
                if (eq.Level == "Plant" || eq.Level == "Area")
                {
                    //查询输入年份
                    var whereExpression = Expressionable.Create<OeeReportViewEntity>()
                         .And(p => p.SendTime != null)
                         .And(p => p.SendTime.Value >= reqModel.StartTime)
                         .And(p => p.SendTime.Value <= reqModel.EndTime)
                         .And(p => p.Status == 1)
                         //.And(p => p.LineId == eq.ID)
                         .ToExpression();

                    var data = await _OeeReportViewEntity.FindList(whereExpression);

                    var YearOee1 = Convert.ToDecimal(data.Sum(p => p.OeeSpeed));//OEE标准时间
                    var YearOee2 = data.Sum(p => p.Unplannedtime);//非计划停机
                    var YearOee3 = data.Sum(p => p.Plannedtime);//计划停机
                    var YearOee5 = data.Sum(p => p.Runtime);//总运行时间
                    var YearOee6 = data.Sum(p => p.GoodCount);//良品数/总产量
                    List<decimal> totalBOMTime = new List<decimal>();
                    foreach (var item in data)
                    {
                        decimal k = 0;
                        if (item.OeeSpeed > 0)
                        {
                            k = item.GoodCount / Convert.ToDecimal(item.OeeSpeed);
                        }
                        totalBOMTime.Add(k);
                    }
                    decimal YearOee4 = totalBOMTime.Sum();//总产量标准时间

                    if (YearOee5 != 0 || YearOee3 != 0 || YearOee2 != 0)
                    {
                        //有效性
                        V1 = Math.Round(Convert.ToDecimal(YearOee5 / (YearOee5 + YearOee3 + YearOee2)), 3);
                    }
                    if (YearOee5 != 0)
                    {
                        //表现性
                        V2 = Math.Round(Convert.ToDecimal(YearOee4 / YearOee5), 3);
                    }
                    if (YearOee6 != 0)
                    {
                        //品质性
                        V3 = YearOee6 / YearOee6;
                    }
                    OEE = V1 * V2 * V3;

                }
                else
                {
                    //查询输入年份
                    var whereExpression = Expressionable.Create<OeeReportViewEntity>()
                         .And(p => p.SendTime != null)
                         .And(p => p.SendTime.Value >= reqModel.StartTime)
                         .And(p => p.SendTime.Value <= reqModel.EndTime)
                         .And(p => p.Status == 1)
                         .And(p => p.LineId == eq.ID)
                         .ToExpression();
                    var data = await _OeeReportViewEntity.FindList(whereExpression);


                    var YearOee1 = Convert.ToDecimal(data.Sum(p => p.OeeSpeed));//OEE标准时间
                    var YearOee2 = data.Sum(p => p.Unplannedtime);//非计划停机
                    var YearOee3 = data.Sum(p => p.Plannedtime);//计划停机
                    var YearOee5 = data.Sum(p => p.Runtime);//总运行时间
                    var YearOee6 = data.Sum(p => p.GoodCount);//良品数/总产量
                                                              // decimal YearOee4 = 0;
                    List<decimal> totalBOMTime = new List<decimal>();
                    foreach (var item in data)
                    {
                        decimal k = 0;
                        if (item.OeeSpeed > 0)
                        {
                            k = item.GoodCount / Convert.ToDecimal(item.OeeSpeed);
                        }
                        totalBOMTime.Add(k);
                    }
                    decimal YearOee4 = totalBOMTime.Sum();//总产量标准时间
                    if (YearOee5 != 0 || YearOee3 != 0 || YearOee2 != 0)
                    {
                        //有效性
                        V1 = Math.Round(Convert.ToDecimal(YearOee5 / (YearOee5 + YearOee3 + YearOee2)), 3);
                    }
                    if (YearOee5 != 0)
                    {
                        //表现性
                        V2 = Math.Round(Convert.ToDecimal(YearOee4 / YearOee5), 3);
                    }
                    if (YearOee6 != 0)
                    {
                        //品质性
                        V3 = YearOee6 / YearOee6;
                    }
                    OEE = V1 * V2 * V3;

                }
            }
            if (reqModel.KPIName == "OEE")
            {
                OEE = Math.Round(OEE * 100, 1);
                return OEE;
            }
            else if (reqModel.KPIName == "表现性")
            {
                V2 = Math.Round(V2 * 100, 1);
                return V2;
            }
            else if (reqModel.KPIName == "有效性")
            {
                V1 = Math.Round(V1 * 100, 1);
                return V1;
            }
            else if (reqModel.KPIName == "品质性")
            {
                V3 = Math.Round(V3 * 100, 1);
                return V3;
            }
            return 0;
        }
        #endregion

        #region 累计待料时间
        public async Task<decimal> WaitTime(KpiValueRequestSingleModel reqModel)
        {
            decimal waitingTime = 0;
            try
            {
                if (reqModel.LineCode == "" || reqModel.LineCode == null)
                {
                    return -1;
                }
                //var eq = await _EquipmentEntity.FindEntity(p => p.EquipmentCode == reqModel.LineCode || p.ID == reqModel.LineCode);
                //if (eq != null)
                //{
                //    var LineLIst = await _EquipmentEntity.FindList(p => p.ParentId == eq.ID && p.Enabled == 1 && p.Deleted == 0 && p.Level == "Line");
                //待料
                var sumTime = await _PerformanceEntity.FindList(p => p.Reason == "待料" && p.Group == "流程等待" && p.ProductionOrderNo != null && p.ProductionOrderNo != "" && p.StartTimeUtc >= reqModel.StartTime && p.EndTimeUtc <= reqModel.EndTime);
                //var sumTime2 = sumTime.Where(p => LineLIst.Any(s => s.ID == p.LineId)).ToList();
                decimal dlTime = sumTime.Sum(p => p.TimeDifferenceInSeconds);


                //运行中
                var runTime = await _PerformanceEntity.FindList(p => p.Reason == "运行中" && p.Group == "运行时" && p.ProductionOrderNo != null && p.ProductionOrderNo != "" && p.StartTimeUtc >= reqModel.StartTime && p.EndTimeUtc <= reqModel.EndTime);
                //var runTime2 = runTime.Where(p => LineLIst.Any(s => s.ID == p.LineId)).ToList();
                decimal yxTime = runTime.Sum(p => p.TimeDifferenceInSeconds);
                if (yxTime > 0)
                {
                    decimal num = 60.0m;
                    decimal dl = dlTime / num;
                    decimal yx = yxTime / num;
                    SerilogServer.LogDebug($"待料小时时间：{dl}" + $"运行中小时时间：{yx}", "WaitTimeLog");
                    waitingTime = Math.Round((dl / yx) * 100, 0);
                    SerilogServer.LogDebug($"待料时间：{waitingTime}" + $"待料时间：{dlTime}" + $"运行中：{yxTime}", "WaitTimeLog");
                }

                //}

            }
            catch (Exception)
            {

            }
            return waitingTime;
        }
        #endregion

        #region 周度产线使用率
        /// <summary>
        /// 周度产线使用率
        /// </summary>
        /// <param name="reqmodel"></param>
        /// <returns></returns>
        public async Task<List<DataSeriesModel>> GetLineUsageRate(KpiValueRequestSingleModel reqmodel)
        {
            var Sumdata = 0m;
            var k = 0.000m;
            List<DataSeriesModel> list = new List<DataSeriesModel>();
            try
            {
                var weekList = TimeDurationHelper.GetWeeks(reqmodel.StartTime.Value, reqmodel.EndTime.Value);
                DataSeriesModel target = new DataSeriesModel()
                {
                    Name = "目标",
                    Data = await GetKpiTargetList(reqmodel)
                };
                List<decimal> aData = new List<decimal>();
                List<decimal> bData = new List<decimal>();
                var eq = await _EquipmentEntity.FindEntity(p => p.EquipmentCode == reqmodel.LineCode || p.ID == reqmodel.LineCode);
                if (eq != null)
                {
                    var listCate = new List<string>()
                    {
                        "计划停机","生产运行","非计划停机"
                    };
                    var downTimes = await _dal.Db.Queryable<PerformanceEntity>()
                             .Where(p =>
                             !string.IsNullOrEmpty(p.ProductionOrderId) &&
                             listCate.Contains(p.Categroy) &&
                              p.Machine != null &&
                              p.Machine.Contains("灌装机") &&
                              p.LineId == eq.ID &&
                             ((p.StartTimeUtc >= reqmodel.StartTime && p.EndTimeUtc != null && p.EndTimeUtc <= reqmodel.EndTime) || (p.EndTimeUtc == null && p.StartTimeUtc <= reqmodel.EndTime))
                             )
                             .ToListAsync();

                    foreach (var week in weekList)
                    {
                        var start = week.WeekStart >= reqmodel.StartTime.Value.Date ? week.WeekStart : reqmodel.StartTime.Value.Date;
                        var end = week.WeekEnd.AddSeconds(-1);
                        if (week == weekList.Last())
                        {
                            end = reqmodel.EndTime.Value;
                        }
                        var p = GetWeekLineRate(start, end, eq.ID, downTimes);
                        k += p.runTime;
                        aData.Add(p.date);
                    }
                }

                DataSeriesModel actual = new DataSeriesModel()
                {
                    Name = "实际",
                    Data = aData
                };
                int workday = GetWorkingDays(reqmodel.StartTime.Value, reqmodel.EndTime.Value);
                if (k != 0 && workday != 0)
                {
                    Sumdata = Math.Round(k *100.0m/ (workday * 24), 0);
                    bData.Add(Sumdata);
                }
                DataSeriesModel sumData = new DataSeriesModel()
                {
                    Name = "汇总结果",
                    Data = bData
                };
                list.Add(target);
                list.Add(actual);
                list.Add(sumData);
            }
            catch
            {

            }
            return list;
        }

        /// <summary>
        /// 周产线使用率
        /// </summary>
        /// <param name="start"></param>
        /// <param name="end"></param>
        /// <param name="lineId"></param>
        /// <param name="downTimes"></param>
        /// <returns></returns>
        public WeekLineRate GetWeekLineRate(DateTime start, DateTime end, string lineId, List<PerformanceEntity> downTimes)
        {
            var date = 0m;
            var runTime = 0.000m;
            WeekLineRate weekLineRate = new WeekLineRate();
            try
            {
                var downTimes1 = downTimes
                  .Where(p =>
                  ((p.StartTimeUtc >= start && p.EndTimeUtc != null && p.EndTimeUtc <= end) || (p.EndTimeUtc == null && p.StartTimeUtc <= end))
                  )
                  .Select(p => new
                  {
                      p.LineId,
                      totals = (long)p.TimeDifferenceInSeconds
                  })
                  .ToList();

                runTime = downTimes1.Sum(x => x.totals) / 3600.0m;
                int workday = GetWorkingDays(start, end);
                if (workday != 0)
                {
                    date = Math.Round(runTime*100.0m / (workday * 24), 0);
                }
                weekLineRate.date = date;
                weekLineRate.runTime = runTime;
            }
            catch (Exception)
            {

            }

            return weekLineRate;
        }
        public class WeekLineRate
        {
            public decimal runTime { get; set; }
            public decimal date { get; set; }
        }
        public static int GetWorkingDays(DateTime startDate, DateTime endDate)
        {
            int workingDays = 0;
            for (DateTime currentDate = startDate; currentDate <= endDate; currentDate = currentDate.AddDays(1))
            {
                if (currentDate.DayOfWeek != DayOfWeek.Saturday && currentDate.DayOfWeek != DayOfWeek.Sunday)
                {
                    workingDays++;
                }
            }
            return workingDays;
        }

        #endregion


        #region 当日生产工单进度
        public async Task<string> ProductionSchedule(KpiValueRequestSingleModel reqmodel)
        {
            //注释 20250217 zyq
            //DateTime todayStart = DateTime.Today; // 当天的00:00:00
            //DateTime todayEnd = todayStart.AddDays(1).AddSeconds(-1); // 当天的23:59:59
            //var order = await _ProductionOrderEntity.FindList(p => p.PlanDate >= todayStart && p.PlanDate <= todayEnd );
            //double seconds = 0;
            //foreach (var item in order)
            //{
            //    if (item.EndTime != null)
            //    {
            //        TimeSpan timeDifference = (TimeSpan)(item.EndTime - item.PlanStartTime);
            //        // 将时间差转换为秒
            //        seconds += timeDifference.TotalSeconds;
            //    }
            //}
            //double hours = Math.Round(seconds / 3600.0, 0);
            //return hours.ToString();


            DateTime todayStart = DateTime.Today; // 当天的00:00:00
            DateTime todayEnd = todayStart.AddDays(1).AddSeconds(-1); // 当天的23:59:59
            var order = await _ProductionOrderEntity.FindList(p => p.PlanDate >= todayStart && p.PlanDate <= todayEnd && p.EndTime != null);
            double seconds = 0;
            order = order.OrderByDescending(p => p.EndTime).ToList();

            foreach (var item in order)
            {
                if (item.EndTime != null)
                {
                    TimeSpan timeDifference = (TimeSpan)(item.EndTime - item.PlanEndTime);  //实际结束时间-计划结束时间
                                                                                            // 将时间差转换为秒
                    seconds = timeDifference.TotalSeconds;
                    break;
                }
            }
            double hours = Math.Round(seconds / 3600.0, 0);
            return hours.ToString();
        }
        #endregion
        #endregion


        #region SIM3

        #region 实际产能利用率
        /// <summary>
        /// 实际产能利用率
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<decimal> UsageRate(KpiValueRequestSingleModel reqModel)
        {
            var counts = 0.0m;
            try
            {
                if (reqModel.StartTime == null || reqModel.EndTime == null)
                {
                    return -1;
                }
                var orderList = await _dal.Db.Queryable<ConfirmationEntity, ProductionOrderEntity, SappackorderEntity>(
                        (c, p, s) => new object[]
                        {
                    JoinType.Inner , c.OrderId == p.ID,
                    JoinType.Inner, p.ProductionOrderNo == s.Aufnr
                        })
                        .Where((c, p, s) => p.SapOrderType == "ZXH1"
                                         && s.Arbpl.Contains("FIL")
                                         //  && c.Status == 1 //暂时注释已发送SAP条件后期再更换
                                         // && p.PoStatus == "3"//暂时换成这个
                                         && s.Gstrp >= reqModel.StartTime.Value.Date
                                         && s.Gstrp <= reqModel.EndTime.Value.Date)
                        .Select((c, p, s) => new
                        {
                            c.Runtime,
                            c.Plannedtime,
                            c.Unplannedtime,
                            s.Gstrp,
                            s.Arbpl
                        })
                        .ToListAsync();

                var sumTime = orderList.Sum(p => p.Runtime + p.Plannedtime + p.Unplannedtime);

                int workdays = await _dal.Db.Queryable<SappackorderEntity>().Where(p => p.Arbpl.Contains("FIL")
                                         && p.Auart == "ZXH1"
                                         && p.Gstrp >= reqModel.StartTime.Value.Date
                                         && p.Gstrp <= reqModel.EndTime.Value.Date).GroupBy(p => new { /*p.Arbpl,*/ p.Gstrp }).CountAsync();
                if (workdays > 0)
                {
                    counts = Math.Round((sumTime / (23 * workdays * 24)) * 100, 1); //默认23条线，后续需要查询
                }
            }
            catch (Exception)
            {

            }
            return counts;
        }
        #endregion

        #region 整线直通率
        /// <summary>
        /// 整线直通率
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<decimal> LineDirectRate(KpiValueRequestSingleModel reqModel)
        {
            var counts = 0.0m;
            try
            {
                if (reqModel.StartTime == null || reqModel.EndTime == null)
                {
                    return -1;
                }
                var orderList = await _dal.Db.Queryable<ConfirmationEntity, ProductionOrderEntity, SappackorderEntity>(
                        (c, p, s) => new object[]
                        {
                    JoinType.Inner , c.OrderId == p.ID,
                    JoinType.Inner, p.ProductionOrderNo == s.Aufnr
                        })
                        .Where((c, p, s) => p.SapOrderType == "ZXH1"
                                         && s.Arbpl.Contains("FIL")
                                         // && c.Status == 1 //暂时注释已发送SAP条件后期再更换
                                         && p.PoStatus == "3"//暂时换成这个
                                         && c.ModifyDate >= reqModel.StartTime.Value.Date
                                         && c.ModifyDate <= reqModel.EndTime.Value.Date)
                        .Select((c, p, s) => new
                        {
                            c.GoodCount,
                            c.TotalCount
                        })
                        .ToListAsync();
                //良品数就等于总数不需要TotalCount
                var GoodCount = orderList.Sum(p => p.GoodCount);
                if (GoodCount > 0)
                {
                    counts = Math.Round(GoodCount / (GoodCount) * 100, 1);
                }
            }
            catch (Exception)
            {

            }
            return counts;
        }
        #endregion

        #region 生产成本信息
        /// <summary>
        /// 生产成本信息
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<List<DataSeriesModel>> GetProductionCostsMsg(KpiValueRequestSingleModel reqModel)
        {
            List<DataSeriesModel> list = new List<DataSeriesModel>();
            try
            {
                //  var k = await GetCompUsingQty((DateTime)reqModel.StartTime, (DateTime)reqModel.EndTime);
                DataSeriesModel dataSeriesModel = new DataSeriesModel();
                List<decimal> Data1 = new List<decimal>();
                List<decimal> Data2 = new List<decimal>();
                var ss = new List<string>() { "自然月人工费用", "自然月消耗品", "自然月维修费用", "自然月动力耗用费用", "自然月折旧费用", "自然月其它费用" };
                foreach (var item in ss)
                {

                    reqModel.KPIName = item;
                    var action = await GetKPITarget(reqModel, "实际");
                    Data1.Add(Convert.ToDecimal(action));
                }
                list.Add(new DataSeriesModel
                {
                    Name = "实际",
                    Data = Data1
                });
                foreach (var item in ss)
                {
                    reqModel.KPIName = item;
                    var target = await GetKPITarget(reqModel, "预算");
                    Data2.Add(Convert.ToDecimal(target));
                }
                list.Add(new DataSeriesModel
                {
                    Name = "目标",
                    Data = Data2
                });
                //单位费用
                var actionList = list.Where(P => P.Name == "实际").ToList();
                var actionNum = actionList[0].Data.Sum();
                var actionSum = 0.0m;
                //目标费用
                var targetList = list.Where(P => P.Name == "目标").ToList();
                var targetNum = targetList[0].Data.Sum();
                var targetSum = 0.0m;
                // if (k > 0)
                //  {
                actionSum = Math.Round(actionNum, 2);
                targetSum = Math.Round(targetNum, 2);
                //  }

                list.Add(new DataSeriesModel
                {
                    Name = "单位费用",
                    Data = new List<decimal> { actionSum }
                });
                list.Add(new DataSeriesModel
                {
                    Name = "目标费用",
                    Data = new List<decimal> { targetSum }
                });
            }
            catch (Exception)
            {

            }

            return list;
        }
        #endregion

        #region 订单产量状态
        /// <summary>
        /// 订单产量状态
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<List<SunburstModel>> GetOrderProductionStatus(KpiValueRequestSingleModel reqModel)
        {
            List<SunburstModel> list = new List<SunburstModel>();
            List<string> strings = new List<string> { "NotComplete", "OverComplete", "CompleteAtOnce" };
            try
            {
                var orderList = await _dal.Db.Queryable<ConfirmationEntity, ProductionOrderEntity>(
                        (c, p) => new object[]
                        {
                    JoinType.Inner , c.OrderId == p.ID,
                        })
                        .Where((c, p) => p.SapOrderType == "ZXH1"
                                         && p.ProduceStatus != null
                                         && c.ModifyDate >= reqModel.StartTime.Value.Date
                                         && c.ModifyDate <= reqModel.EndTime.Value.Date)
                        .Select((c, p) => new
                        {
                            p.ID,
                            p.ProductionOrderNo,
                            p.ProduceStatus,
                            p.Reason
                        })
                        .ToListAsync();
                //分组大类
                var groupType = orderList.GroupBy(p => new { p.ProduceStatus }).Select(p => new { values = p.Count(), ProduceStatus = p.Key.ProduceStatus }).ToList();
                //二类
                var reason = orderList.GroupBy(p => new { p.ProduceStatus, p.Reason }).Select(p => new { values = p.Count(), Reason = p.Key.Reason, ProduceStatus = p.Key.ProduceStatus }).ToList();
                foreach (var item2 in strings)
                {
                    if (groupType.Count > 0)
                    {
                        var groupTypeModel = groupType.Where(p => p.ProduceStatus == item2).FirstOrDefault();
                        if (groupTypeModel != null)
                        {
                            var ProduceStatus = "";
                            switch (item2)
                            {
                                case "NotComplete":
                                    ProduceStatus = "工单未完成";
                                    break;
                                case "OverComplete":
                                    ProduceStatus = "工单过量完成";
                                    break;
                                case "CompleteAtOnce":
                                    ProduceStatus = "工单一次性完成";
                                    break;
                                default:
                                    break;
                            }
                            SunburstModel sunburstModel = new SunburstModel
                            {
                                name = ProduceStatus,
                                value = groupTypeModel.values,
                                children = new List<SunburstModel>()
                            };

                            //二类
                            var reasonList = reason.Where(p => p.ProduceStatus == groupTypeModel.ProduceStatus).ToList();
                            foreach (var item1 in reasonList)
                            {
                                var Reason = "";
                                switch (item1.Reason)
                                {
                                    case "Defective products":
                                        Reason = "包装不良品多";
                                        break;
                                    case "Insufficient supply":
                                        Reason = "制造供料不足";
                                        break;
                                    case "Raw material issues":
                                        Reason = "原物料问题";
                                        break;
                                    case "Others":
                                        Reason = "其他不可抗力因素";
                                        break;

                                    case "Continuous production":
                                        Reason = "连续生产";
                                        break;
                                    case "PMC requirement":
                                        Reason = "PMC要求";
                                        break;
                                    case "Overfulfilled":
                                        Reason = "生产做多";

                                        break;
                                    case "工单一次性完成":
                                        Reason = "工单一次性完成";
                                        break;
                                    default:
                                        break;
                                }
                                SunburstModel child = new SunburstModel
                                {
                                    name = Reason,
                                    value = item1.values
                                };
                                sunburstModel.children.Add(child);

                            }
                            list.Add(sunburstModel);
                        }
                        else
                        {
                            var ProduceStatus = "";
                            switch (item2)
                            {
                                case "NotComplete":
                                    ProduceStatus = "工单未完成";
                                    break;
                                case "OverComplete":
                                    ProduceStatus = "工单过量完成";
                                    break;
                                case "CompleteAtOnce":
                                    ProduceStatus = "工单一次性完成";
                                    break;
                                default:
                                    break;
                            }
                            SunburstModel sunburstModel = new SunburstModel
                            {
                                name = ProduceStatus,
                                value = 0,
                                children = new List<SunburstModel>()
                            };
                            list.Add(sunburstModel);
                        }
                        /* foreach (var item in groupType)
                         {
                             var ProduceStatus = "";
                             switch (item.ProduceStatus)
                             {
                                 case "NotComplete":
                                     ProduceStatus = "工单未完成";
                                     break;
                                 case "OverComplete":
                                     ProduceStatus = "工单过量完成";
                                     break;
                                 case "CompleteAtOnce":
                                     ProduceStatus = "工单一次性完成";
                                     break;
                                 default:
                                     break;
                             }
                             SunburstModel sunburstModel = new SunburstModel
                             {
                                 Name = ProduceStatus,
                                 Value = item.values.ToString(),
                                 Children = new List<SunburstModel>()
                             };

                             //二类
                             var reasonList = reason.Where(p => p.ProduceStatus == item.ProduceStatus).ToList();
                             foreach (var item1 in reasonList)
                             {
                                 var Reason = "";
                                 switch (item1.Reason)
                                 {
                                     case "Defective products":
                                         Reason = "包装不良品多";
                                         break;
                                     case "Insufficient supply":
                                         Reason = "制造供料不足";
                                         break;
                                     case "Raw material issues":
                                         Reason = "原物料问题";
                                         break;
                                     case "Others":
                                         Reason = "其他不可抗力因素";
                                         break;

                                     case "Continuous production":
                                         Reason = "连续生产";
                                         break;
                                     case "PMC requirement":
                                         Reason = "PMC要求";
                                         break;
                                     case "Overfulfilled":
                                         Reason = "生产做多";

                                         break;
                                     case "工单一次性完成":
                                         Reason = "工单一次性完成";
                                         break;
                                     default:
                                         break;
                                 }
                                 SunburstModel child = new SunburstModel
                                 {
                                     Name = Reason,
                                     Value = item1.values.ToString()
                                 };
                                 sunburstModel.Children.Add(child);

                             }
                             list.Add(sunburstModel);
                         }*/
                    }
                    else
                    {
                        var ProduceStatus = "";
                        switch (item2)
                        {
                            case "NotComplete":
                                ProduceStatus = "工单未完成";
                                break;
                            case "OverComplete":
                                ProduceStatus = "工单过量完成";
                                break;
                            case "CompleteAtOnce":
                                ProduceStatus = "工单一次性完成";
                                break;
                            default:
                                break;
                        }
                        SunburstModel sunburstModel = new SunburstModel
                        {
                            name = ProduceStatus,
                            value = 1,
                            children = new List<SunburstModel>()
                        };
                        list.Add(sunburstModel);
                    }
                }
            }
            catch (Exception)
            {

            }
            return list;
        }
        #endregion

        #region 生产状态指示灯

        public async Task<List<SIMEquipmentStatusModel>> EquipmentStatusHouur()
        {
            ////查找所有灌装机
            var eqList = await _EquipmentEntity.FindList(p => p.EquipmentName.Contains("灌装机") && p.Deleted == 0 && p.Enabled == 1);
            ////return await GetEquipmentStatusList(eqList);

            List<SIMEquipmentStatusModel> l2Model = new List<SIMEquipmentStatusModel>();
            var ids = eqList?.Select(x => x.ID) ?? new List<string>();
            var performanceEntities = await (_PerformanceEntity.Db.Queryable<PerformanceEntity>()
                   .Where(x => ids.Contains(x.EquipmentId) && x.EndTimeUtc == null)
                   .OrderByDescending(x => x.StartTimeUtc)).ToListAsync();
            foreach (var item in eqList)
            {
                var performanceEntity = performanceEntities.FirstOrDefault(x => x.EquipmentId == item.ID);
                SIMEquipmentStatusModel l2 = new SIMEquipmentStatusModel();
                string Status = "未启动";
                if (performanceEntity != null)
                {
                    switch (performanceEntity.Categroy)
                    {
                        case "生产运行":
                            Status = "运行中";
                            break;
                        case "计划停机":
                            Status = "停机";
                            break;
                        case "非计划停机":
                            Status = "故障";
                            break;
                        default:
                            break;
                    }
                }
                l2.EquipmentStatus = Status;
                if (item.EquipmentName.Contains("#"))
                {
                    //获取产线名截取#号后数据
                    l2.EquipmentName = item.EquipmentName.Substring(item.EquipmentName.IndexOf('#') + 1);
                }
                else
                {
                    l2.EquipmentName = item.EquipmentName;
                }
                var line = await _eq.FindEntity(p => p.ID == item.LineId);
                l2.StatusHour = 0 + "h";
                DateTime todayStart = DateTime.Today; // 当天的00:00:00
                DateTime todayEnd = todayStart.AddDays(1).AddSeconds(-1); // 当天的23:59:59

                if (line != null)
                {
                    var progress = await _ProductionOrderEntity.FindList(p => p.PlanDate >= todayStart && p.PlanDate <= todayEnd && p.LineCode == line.EquipmentCode && p.SegmentCode.Contains("FIL") && p.PoStatus == "3");
                    if (progress.Count > 0)
                    {
                        var progress1 = progress.OrderBy(p => p.PlanEndTime).ToList();
                        if (progress1.Count < 0)
                        {
                            l2.DataState = "progress1无数据";
                        }
                        foreach (var pro in progress1)
                        {
                            if (pro.PlanEndTime != null && pro.EndTime != null)
                            {
                                TimeSpan timeDifference = (TimeSpan)(pro.EndTime - pro.PlanEndTime);
                                l2.DataState = "实际结束时间：" + pro.EndTime + "计划结束时间" + pro.PlanEndTime;
                                // 获取相差的小时数
                                double hoursDifference = timeDifference.TotalHours;
                                l2.StatusHour = hoursDifference + "h";
                                break;
                            }
                            l2.DataState = "实际结束时间：" + pro.EndTime + "计划结束时间" + pro.PlanEndTime;
                        }
                    }
                    else
                    {
                        l2.DataState = "未找到工作中心包含FIL且完工的工单" + line.EquipmentCode + ";" + todayStart + ";" + todayEnd;
                    }
                }
                else
                {
                    l2.DataState = "当前设备的LineID为空,设备id：" + item.ID;
                }
                l2Model.Add(l2);
                /* var Influxdbquantity = 0;
                 string Status = "";
                 //查找设备状态点位
                 var opc = await _InfluxOpcTagEntit.FindEntity(p => p.EquipmentId == item.ID && p.Name == "设备状态");
                 if (opc != null)
                 {
                     try
                     {
                         //获取点位状态
                         Influxdbquantity = (int)Convert.ToDecimal((await _IInfluxDbServices.GetLastInfluxData(DateTime.Now.AddMinutes(-10), DateTime.Now, null, opc.Tag))?.Value);
                     }
                     catch
                     {
                         Influxdbquantity = 0;
                     }


                     #region 截取字符进行状态比对
                     //按设定找点位描述最后一位#号
                     var str = opc.Describe.LastIndexOf("#");
                     //截取#后的数据
                     var str1 = opc.Describe.Substring(str + 1);
                     //按;分割
                     string[] parts = str1.Split(";");
                     for (int j = 0; j < parts.Length; j++)
                     {
                         //按=分割
                         string[] parts2 = parts[j].Split("=");
                         if (Influxdbquantity.ToString() == parts2[1])
                         {
                             Status = parts2[0];
                         }
                         //值存在&符号走
                         if (parts2[1].Contains("&"))
                         {
                             //按&符号截取
                             string[] parts1 = parts2[1].Split("&");
                             for (int h = 0; h < parts1.Length; h++)
                             {
                                 //当获取的点位值和查到的描述一致返回状态描述
                                 if (parts1[h] == Influxdbquantity.ToString())
                                 {
                                     Status = parts2[0];
                                     break;
                                 }
                             }
                         }
                         if (Status != "")
                         {
                             break;
                         }
                     }
                     #endregion
                     //获取设备状态和设备名称
                     l2.EquipmentStatus = Status == "" ? "未启动" : Status;
                     if (item.EquipmentName.Contains("#"))
                     {
                         //获取产线名截取#号后数据
                         l2.EquipmentName = item.EquipmentName.Substring(item.EquipmentName.IndexOf('#') + 1);
                     }
                     else
                     {
                         l2.EquipmentName = item.EquipmentName;
                     }
                 }
                 else
                 {

                     //没找到点位则赋值状态为无排产
                     l2.EquipmentStatus = "未启动";
                     if (item.EquipmentName.Contains("#"))
                     {
                         //获取产线名截取#号后数据
                         l2.EquipmentName = item.EquipmentName.Substring(item.EquipmentName.IndexOf('#') + 1);
                     }
                     else
                     {
                         l2.EquipmentName = item.EquipmentName;
                     }
                 }*/

            }
            return l2Model;
        }


        #endregion
        #endregion
    }
}