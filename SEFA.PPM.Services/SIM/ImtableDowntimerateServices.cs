
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using System;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using System.Linq;
using SEFA.PPM.Model.Models.PTM;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.IRepository.UnitOfWork;
using Dm;
using SEFA.DFM.Model.Models;

namespace SEFA.PPM.Services
{
	public class ImtableDowntimerateServices : BaseServices<ImtableDowntimerateEntity>, IImtableDowntimerateServices
	{
		private readonly IBaseRepository<ImtableDowntimerateEntity> _dal;
		private readonly IUnitOfWork _unitOfWork;
		private readonly IUser _user;


		public ImtableDowntimerateServices(IBaseRepository<ImtableDowntimerateEntity> dal, IUnitOfWork unitOfWork, IUser user)
		{
			this._dal = dal;
			base.BaseDal = dal;
			_unitOfWork = unitOfWork;
			_user = user;
		}

		public async Task<List<ImtableDowntimerateEntity>> GetList(ImtableDowntimerateRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<ImtableDowntimerateEntity>()
							 .ToExpression();
			var data = await _dal.FindList(whereExpression);
			return data;
		}

		public async Task<PageModel<ImtableDowntimerateEntity>> GetPageList(ImtableDowntimerateRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<ImtableDowntimerateEntity>()
							 .ToExpression();
			var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);

			return data;
		}

		public async Task<MessageModel<string>> SaveDowntimerate(DateTime startTime, DateTime endTime, int Status)
		{
			var result = new MessageModel<string>()
			{
				success = false,
				msg = "保存失败！"
			};
			var confirmations = await _dal.Db.Queryable<ConfirmationEntity>().Where(x => x.Status == Status && x.SendTime != null && x.SendTime >= startTime && x.SendTime <= endTime).ToListAsync();
			if (!confirmations.Any())
			{
				result.msg = $"{startTime.ToString("yyyy-MM-dd HH:mm:ss")}~{endTime.ToString("yyyy-MM-dd HH:mm:ss")}时间范围内未找到发送的工时报工";
				return result;
			}
			var categoryNames = new List<string>() { "Production Time", "Planned Stoppages", "Unplanned Stoppages", "生产运行", "计划停机", "非计划停机" };
			var categoryNames1 = new List<string>() { "Unplanned Stoppages", "非计划停机" };
			var addList = new List<ImtableDowntimerateEntity>();
			var updateList = new List<ImtableDowntimerateEntity>();
			var productionOrders = await _dal.Db.Queryable<ProductionOrderEntity>().Where(x => confirmations.Any(x1 => x.ID == x1.OrderId)).ToListAsync();
			var poProducedExecutions = await _dal.Db.Queryable<PoProducedExecutionEntity>().Where(x => confirmations.Any(x1 => x1.OrderId == x.ProductionOrderId) && x.EndTime != null).ToListAsync();
			var downtimes = await _dal.Db.Queryable<DowntimeEntity>().Where(x => x.EndTimeUtc != null && confirmations.Any(x1 => x.OrderId == x1.OrderId)).ToListAsync();
			var imtableDowntimerates = (await _dal.Query()).Where(x => new DateTime((int)x.Year, (int)x.Month, (int)x.Day) >= startTime && new DateTime((int)x.Year, (int)x.Month, (int)x.Day) <= endTime).ToList();
			var downtimeCategroys = await _dal.Db.Queryable<DowntimeCategroyEntity>().Where(x => categoryNames.Contains(x.Description)).ToListAsync();
			var downtimeGroups = await _dal.Db.Queryable<DowntimeGroupEntity>().ToListAsync();
			var downtimeReasons = await _dal.Db.Queryable<DowntimeReasonEntity>().ToListAsync();
			var reasonId = downtimeReasons?.FirstOrDefault(x => x.Description == "生产前准备")?.ID;
			//找到未占用/无排产以外的原因
			var reasonsForGroup = GetReasonsByCategoryAndGroupName(categoryNames, "", downtimeGroups, downtimeReasons, downtimeCategroys);
			//找到非计划停机下设备故障相关的原因
			var reasonsForGroup1 = GetReasonsByCategoryAndGroupName(categoryNames1, "设备故障", downtimeGroups, downtimeReasons, downtimeCategroys);
			//获取所有设备数据
			var allEquipments = _dal.Db.Queryable<EquipmentEntity>().ToList();
			for (DateTime i = startTime.Date; i <= endTime.Date; i = i.AddDays(1))
			{
				var confirmations1 = confirmations.FindAll(x => x.SendTime >= i && x.SendTime < i.AddDays(1));
				if (confirmations1.Any())
				{
					var productionOrders1 = productionOrders.FindAll(x => confirmations1.Any(x1 => x.ID == x1.OrderId));
					if (productionOrders1.Any())
					{
						var groups = productionOrders1.GroupBy(x => new { x.LineCode, x.SegmentCode });
						foreach (var item in groups)
						{
							decimal runningTime = 0;
							decimal totalOccupationTime = 0;
							decimal totalDowntime = 0;
							decimal downtimeAffactProducion = 0;

							// 查找 Line 下的所有 Unit
							var unitsUnderLine = new List<EquipmentEntity>();
							var line = allEquipments.FirstOrDefault(x => x.EquipmentCode == item.Key.LineCode);
							if (line == null)
							{
								continue;
							}
							FindUnitsRecursively(allEquipments, line.ID, unitsUnderLine);

							foreach (var item1 in item)
							{
								//var lastPoProducedExecutions1 = poProducedExecutions.Where(x => x.ProductionOrderId == item1.ID).OrderByDescending(x => x.EndTime).FirstOrDefault();
								//var orderEndTime = lastPoProducedExecutions1?.EndTime;
								var orderStartTime = item1.StartTime;
								var orderEndTime = item1.EndTime;
								if (orderEndTime != null)
								{
									if (string.IsNullOrEmpty(reasonId))
									{
										//取第一个生产前准备事件的结束时间作为QA时间
										var qaTime = downtimes.FindAll(x => x.OrderId == item1.ID && x.ReasonId == reasonId).OrderBy(x => x.EndTimeUtc).FirstOrDefault()?.EndTimeUtc;
										if (qaTime != null)
										{
											// 计算每个 downtime 的时间差并汇总
											runningTime += downtimes.Where(x => reasonsForGroup.Any(x2 => x.ReasonId == x2.ID) && x.OrderId == item1.ID && x.StartTimeUtc >= qaTime && x.EndTimeUtc <= orderEndTime).Sum(x => (decimal)(x.EndTimeUtc.Value - x.StartTimeUtc.Value).TotalSeconds);
											if (reasonsForGroup.Any())
											{
												downtimeAffactProducion += downtimes.Where(x => unitsUnderLine.Any(x2 => x2.ID == x.EquipmentId) && reasonsForGroup1.Any(x2 => x.ReasonId == x2.ID) && x.OrderId == item1.ID && x.StartTimeUtc >= qaTime && x.EndTimeUtc <= orderEndTime).Sum(x => (decimal)(x.EndTimeUtc.Value - x.StartTimeUtc.Value).TotalSeconds);
											}
										}
									}
									if (orderStartTime != null)
									{
										// 计算每个 downtime 的时间差并汇总
										totalOccupationTime += downtimes.Where(x => reasonsForGroup.Any(x2 => x.ReasonId == x2.ID) && x.OrderId == item1.ID && x.StartTimeUtc >= orderStartTime && x.EndTimeUtc <= orderEndTime).Sum(x => (decimal)(x.EndTimeUtc.Value - x.StartTimeUtc.Value).TotalSeconds);
										if (reasonsForGroup.Any())
										{
											totalDowntime += downtimes.Where(x => unitsUnderLine.Any(x2 => x2.ID == x.EquipmentId) && reasonsForGroup1.Any(x2 => x.ReasonId == x2.ID) && x.OrderId == item1.ID && x.StartTimeUtc >= orderStartTime && x.EndTimeUtc <= orderEndTime).Sum(x => (decimal)(x.EndTimeUtc.Value - x.StartTimeUtc.Value).TotalSeconds);
										}
									}
								}
							}
							var imtableDowntimerate1 = imtableDowntimerates.Find(x => x.Year == i.Year && x.Month == i.Month && x.Day == i.Day && x.LineId == line.ID && x.WorkCenter == item.Key.SegmentCode);
							if (imtableDowntimerate1 == null)
							{
								imtableDowntimerate1 = new ImtableDowntimerateEntity();
								imtableDowntimerate1.Year = i.Year;
								imtableDowntimerate1.Month = i.Month;
								imtableDowntimerate1.Day = i.Day;
								imtableDowntimerate1.LineId = line.ID;
								imtableDowntimerate1.WorkCenter = item.Key.SegmentCode;
								imtableDowntimerate1.CreateCustomGuid(_user.Name);
								addList.Add(imtableDowntimerate1);
							}
							else
							{
								imtableDowntimerate1.Modify(imtableDowntimerate1.ID, _user.Name);
								updateList.Add(imtableDowntimerate1);
							}

							imtableDowntimerate1.RunningTime = Math.Round(runningTime / 3600m, 2);
							imtableDowntimerate1.TotalOccupationTime = Math.Round(totalOccupationTime / 3600m, 2);
							imtableDowntimerate1.TotalDowntime = Math.Round(totalDowntime / 3600m, 2);  
							imtableDowntimerate1.DowntimeAffactProducion = Math.Round(downtimeAffactProducion / 3600m, 2);

							imtableDowntimerate1.MicroStopageNum = 0;
							imtableDowntimerate1.MicroStopageTime = 0;
							imtableDowntimerate1.EquipmentDowntime = 0;
						}
					}
				}
			}
			_unitOfWork.BeginTran();
			try
			{
				if (addList.Count > 1000)
				{
					await _dal.AddBigData(addList);
				}
				else if (addList.Count > 0)
				{
					await _dal.Add(addList);
				}

				if (updateList.Count > 1000)
				{
					await _dal.StorageBigData(updateList);
				}
				else if (updateList.Count > 0)
				{
					await _dal.Update(updateList);
				}
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.msg = $"提交数据库时出现错误：{ex.Message}";
				return result;
			}
			result.success = true;
			result.msg = "保存成功！";
			return result;
		}

		public List<DowntimeReasonEntity> GetReasonsByCategoryAndGroupName(List<string> categoryNames, string groupName, List<DowntimeGroupEntity> groups, List<DowntimeReasonEntity> reasons, List<DowntimeCategroyEntity> categories)
		{
			var categoryIds = categories
				.Where(c => categoryNames.Contains(c.Description))
				.Select(c => c.ID)
				.ToList();

			// 找到所有顶级组
			var topLevelGroups = groups
				.Where(g => string.IsNullOrEmpty(g.ParentGroupId) && categoryIds.Contains(g.CategoryId))
				.ToList();

			List<DowntimeGroupEntity> matchingGroups;

			if (string.IsNullOrEmpty(groupName))
			{
				// 如果 groupName 为空，仅基于 categoryNames 过滤
				matchingGroups = topLevelGroups;
			}
			else
			{
				// 找到所有匹配的组（可能不是根节点）
				matchingGroups = groups
					.Where(g => topLevelGroups.Any(tlg => IsInHierarchy(tlg.ID, g, groups)) && g.Description == groupName)
					.ToList();
			}

			var allGroups = new List<DowntimeGroupEntity>();
			foreach (var group in matchingGroups)
			{
				FindAllSubGroups(group.ID, groups, allGroups);
				allGroups.Add(group);
			}

			var allReasons = reasons
				.Where(r => allGroups.Any(g => g.ID == r.GroupId))
				.ToList();

			return allReasons;
		}

		private bool IsInHierarchy(string topGroupId, DowntimeGroupEntity group, List<DowntimeGroupEntity> groups)
		{
			if (group.ID == topGroupId)
			{
				return true;
			}

			var parentGroup = groups.FirstOrDefault(g => g.ID == group.ParentGroupId);
			if (parentGroup == null)
			{
				return false;
			}

			return IsInHierarchy(topGroupId, parentGroup, groups);
		}

		private void FindAllSubGroups(string groupId, List<DowntimeGroupEntity> groups, List<DowntimeGroupEntity> result)
		{
			var subGroups = groups.Where(g => g.ParentGroupId == groupId).ToList();
			foreach (var subGroup in subGroups)
			{
				result.Add(subGroup);
				FindAllSubGroups(subGroup.ID, groups, result);
			}
		}

		/// <summary>
		/// 根据id获取下级所有Unit
		/// </summary>
		/// <param name="allEquipments"></param>
		/// <param name="parentId"></param>
		/// <param name="units"></param>
		private static void FindUnitsRecursively(List<EquipmentEntity> allEquipments, string parentId, List<EquipmentEntity> units)
		{
			var children = allEquipments.Where(e => e.ParentId == parentId).ToList();
			foreach (var child in children)
			{
				if (child.Level == "Unit")
				{
					units.Add(child);
				}
				else
				{
					FindUnitsRecursively(allEquipments, child.ID, units);
				}
			}
		}

		public async Task<bool> SaveForm(ImtableDowntimerateEntity entity)
		{
			if (string.IsNullOrEmpty(entity.ID))
			{
				return await this.Add(entity) > 0;
			}
			else
			{
				return await this.Update(entity);
			}
		}
	}
}