using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base.Common.Common;
using SEFA.DFM.Model.ViewModels;
using System;
using SEFA.DFM.Model.Models;
using SEFA.Base.Common.HttpContextUser;
using System.Linq;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.ViewModels.SIM.View;
using SEFA.PPM.Model.ViewModels.MKM.View;
using static SEFA.PPM.Services.KpitgtServices;

namespace SEFA.PPM.Services
{
    public class ProdtgtSaucetypeServices : BaseServices<ProdtgtSaucetypeEntity>, IProdtgtSaucetypeServices
    {
        private readonly IBaseRepository<ProdtgtSaucetypeEntity> _dal;
        private readonly IKpitgtServices _kpitgtServices;
        private readonly IBaseRepository<EquipmentEntity> _eq;
        private readonly IUser _user;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IBaseRepository<DFM.Model.Models.UnitmanageEntity> _unit;
        private readonly IBaseRepository<ProdtgtSaucetypeViewEntity> _ProdtgtSaucetypeViewEntity;
        public ProdtgtSaucetypeServices(IBaseRepository<ProdtgtSaucetypeEntity> dal, IKpitgtServices kpitgtServices, IBaseRepository<EquipmentEntity> eq, IUser user, IUnitOfWork unitOfWork, IBaseRepository<DFM.Model.Models.UnitmanageEntity> unit, IBaseRepository<ProdtgtSaucetypeViewEntity> prodtgtSaucetypeViewEntity)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _kpitgtServices = kpitgtServices;
            _eq = eq;
            _user = user;
            _unitOfWork = unitOfWork;
            _unit = unit;
            _ProdtgtSaucetypeViewEntity = prodtgtSaucetypeViewEntity;
        }

        public async Task<List<ProdtgtSaucetypeModel>> GetList(ProdtgtSaucetypeRequestModel reqModel)
        {
            List<ProdtgtSaucetypeModel> prodtgtSaucetypeModels = new List<ProdtgtSaucetypeModel>();
            var whereExpression = Expressionable.Create<ProdtgtSaucetypeViewEntity>()
                             .AndIF(reqModel.Year != null, p => p.Year == reqModel.Year)
                             .AndIF(reqModel.Month != null, p => p.Month == reqModel.Month)
                             .AndIF(!string.IsNullOrEmpty(reqModel.SauceType), p => p.SauceType.Equals(reqModel.SauceType))
                             .ToExpression();
            var data = await _ProdtgtSaucetypeViewEntity.FindList(whereExpression);
            var datas = data.GroupBy(p => new { type = p.SauceType, year = p.Year, unitName = p.UnitName })
                 .Select(p => new { p.Key.type, p.Key.year, p.Key.unitName }).ToList();
            for (int i = 0; i < datas.Count; i++)
            {
                ProdtgtSaucetypeModel prodtgt = new ProdtgtSaucetypeModel();
                var str = data.Where(p => p.SauceType == datas[i].type
                && p.Year == datas[i].year
                && p.UnitName == datas[i].unitName).ToList();
                List<ProdtgtModel> prodtgts = new List<ProdtgtModel>();
                for (int k = 1; k <= 12; k++)
                {
                    ProdtgtModel prodtgtModel = new ProdtgtModel();
                    var model = str.Where(p => p.Month == k).FirstOrDefault();
                    var model1 = str.Where(p => p.SauceType != null).FirstOrDefault();
                    if (model != null)
                    {
                        prodtgtModel.Type = model.SauceType;
                        prodtgtModel.Year = model.Year;
                        prodtgtModel.UnitName = model.UnitName;
                        prodtgtModel.Month = k;
                        prodtgtModel.m = model.Tgt.ToString("0.###");
                    }
                    else
                    {
                        prodtgtModel.Type = model1.SauceType;
                        prodtgtModel.Year = model1.Year;
                        prodtgtModel.UnitName = model1.UnitName;
                        prodtgtModel.Month = k;
                        prodtgtModel.m = "";
                    }
                    prodtgts.Add(prodtgtModel);
                }
                var k1 = prodtgts.Where(p => p.Month == 1).FirstOrDefault();
                var k2 = prodtgts.Where(p => p.Month == 2).FirstOrDefault();
                var k3 = prodtgts.Where(p => p.Month == 3).FirstOrDefault();
                var k4 = prodtgts.Where(p => p.Month == 4).FirstOrDefault();
                var k5 = prodtgts.Where(p => p.Month == 5).FirstOrDefault();
                var k6 = prodtgts.Where(p => p.Month == 6).FirstOrDefault();
                var k7 = prodtgts.Where(p => p.Month == 7).FirstOrDefault();
                var k8 = prodtgts.Where(p => p.Month == 8).FirstOrDefault();
                var k9 = prodtgts.Where(p => p.Month == 9).FirstOrDefault();
                var k10 = prodtgts.Where(p => p.Month == 10).FirstOrDefault();
                var k11 = prodtgts.Where(p => p.Month == 11).FirstOrDefault();
                var k12 = prodtgts.Where(p => p.Month == 12).FirstOrDefault();

                prodtgt.Type = k1.Type;
                prodtgt.Year = k1.Year;
                prodtgt.UntName = k1.UnitName;
                prodtgt.m1 = k1.m;
                prodtgt.m2 = k2.m;
                prodtgt.m3 = k3.m;
                prodtgt.m4 = k4.m;
                prodtgt.m5 = k5.m;
                prodtgt.m6 = k6.m;
                prodtgt.m7 = k7.m;
                prodtgt.m8 = k8.m;
                prodtgt.m9 = k9.m;
                prodtgt.m10 = k10.m;
                prodtgt.m11 = k11.m;
                prodtgt.m12 = k12.m;
                prodtgtSaucetypeModels.Add(prodtgt);
            }
            return prodtgtSaucetypeModels;
        }
        public class ProdtgtModel
        {
            public string Type { get; set; }
            public int Year { get; set; }
            public string UnitName { get; set; }
            public int Month { get; set; }
            public string m { get; set; }
        }
        public async Task<PageModel<ProdtgtSaucetypeViewEntity>> GetPageList(ProdtgtSaucetypeViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<ProdtgtSaucetypeViewEntity>()
                                             .AndIF(!string.IsNullOrEmpty(reqModel.Year), p => p.Year ==Convert.ToInt32( reqModel.Year))
                                             .AndIF(!string.IsNullOrEmpty(reqModel.Month), p => p.Month == Convert.ToInt32(reqModel.Month))
                                             .AndIF(!string.IsNullOrEmpty(reqModel.SauceType), p => p.SauceType.Equals(reqModel.SauceType))
                                             .ToExpression();
            var data = await _ProdtgtSaucetypeViewEntity.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return data;
        }
        public async Task<List<string>> GetSauceType()
        {
            List<string> res = new List<string>();
            var whereExpression = Expressionable.Create<ProdtgtSaucetypeEntity>()
                     .ToExpression();
            var data = await _dal.FindList(whereExpression);
            res = data.GroupBy(p => p.SauceType).Select(p => p.Key).ToList();
            return res;
        }
        public async Task<bool> SaveForm(ProdtgtSaucetypeEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
        #region 丢弃


        /*public async Task<ResultString> ImportData([FromForm] FileImportDto input)
        {

            ResultString result = new ResultString();
            try
            {
                _unitOfWork.BeginTran();
                var stream = input.File.OpenReadStream();
                // 检查文件是否存在
                if (stream == null)
                {
                    result.AddError("未找到文件,请重新上传");
                    return result;
                }
                //获取表格数据
                var fileData = await _kpitgtServices.ReadExcel(stream);
                if (fileData.Rows.Count < 1)
                {
                    result.AddError("表格中没有效数据");
                    return result;
                }
                List<ProdtgtSaucetypeEntity> excelDataList = new List<ProdtgtSaucetypeEntity>();
                List<ProdtgtSaucetypeEntity> upDataList = new List<ProdtgtSaucetypeEntity>();
                List<ProdtgtSaucetypeEntity> insertDataList = new List<ProdtgtSaucetypeEntity>();
                var wheredtgExpression = Expressionable.Create<ProdtgtSaucetypeEntity>()
                                     .ToExpression();
                var dtgDataList = await _dal.FindList(wheredtgExpression);
                var whereUnitExpression = Expressionable.Create<DFM.Model.Models.UnitmanageEntity>()
                                           .ToExpression();
                var unitDataList = await _unit.FindList(whereUnitExpression);
                var whereExpression = Expressionable.Create<EquipmentEntity>()
                      .ToExpression();
                var eqDataList = await _eq.FindList(whereExpression);
                string year = string.Empty;
                string unit = string.Empty;
                for (int i = 0; i < fileData.Rows.Count; i++)
                {
                    List<string> monthList = new List<string>();
                    if (i == 0)
                    {
                        //获取年份
                        year = fileData.Rows[i]["Column2"] == null ? "" : fileData.Rows[i]["Column2"].ToString();
                        if (string.IsNullOrEmpty(year))
                        {
                            result.AddError("表格中年份不能为空");
                            return result;
                        }
                        unit = fileData.Rows[i]["Column4"] == null ? "" : fileData.Rows[i]["Column4"].ToString();
                        if (string.IsNullOrEmpty(year))
                        {
                            result.AddError("表格中单位不能为空");
                            return result;
                        }
                    }
                    else if (i == 1)
                    {
                        continue;
                    }
                    else
                    {
                        var sauType = fileData.Rows[i]["Column1"] == null ? "" : fileData.Rows[i]["Column1"].ToString();
                        //判断空
                        if (string.IsNullOrEmpty(sauType))
                        {
                            result.AddError(string.Format(@"第'{0}'行，酱料类别列为空，导入失败", i + 1));
                            return result;
                        }
                        var January = fileData.Rows[i]["Column2"] == null ? "" : fileData.Rows[i]["Column2"].ToString();
                        monthList.Add(January);
                        var February = fileData.Rows[i]["Column3"] == null ? "" : fileData.Rows[i]["Column3"].ToString();
                        monthList.Add(February);
                        var March = fileData.Rows[i]["Column4"] == null ? "" : fileData.Rows[i]["Column4"].ToString();
                        monthList.Add(March);
                        var April = fileData.Rows[i]["Column5"] == null ? "" : fileData.Rows[i]["Column5"].ToString();
                        monthList.Add(April);
                        var May = fileData.Rows[i]["Column6"] == null ? "" : fileData.Rows[i]["Column6"].ToString();
                        monthList.Add(May);
                        var June = fileData.Rows[i]["Column7"] == null ? "" : fileData.Rows[i]["Column7"].ToString();
                        monthList.Add(June);
                        var July = fileData.Rows[i]["Column8"] == null ? "" : fileData.Rows[i]["Column8"].ToString();
                        monthList.Add(July);
                        var August = fileData.Rows[i]["Column9"] == null ? "" : fileData.Rows[i]["Column9"].ToString();
                        monthList.Add(August);
                        var September = fileData.Rows[i]["Column10"] == null ? "" : fileData.Rows[i]["Column10"].ToString();
                        monthList.Add(September);
                        var October = fileData.Rows[i]["Column11"] == null ? "" : fileData.Rows[i]["Column11"].ToString();
                        monthList.Add(October);
                        var November = fileData.Rows[i]["Column12"] == null ? "" : fileData.Rows[i]["Column12"].ToString();
                        monthList.Add(November);
                        var December = fileData.Rows[i]["Column13"] == null ? "" : fileData.Rows[i]["Column13"].ToString();
                        monthList.Add(December);
                        for (int j = 0; j < monthList.Count; j++)
                        {
                            if (!string.IsNullOrEmpty(monthList[j]))
                            {
                                ProdtgtSaucetypeEntity entity = new ProdtgtSaucetypeEntity();
                                entity.CreateCustomGuid(_user.Name);
                                entity.SauceType = sauType;
                                entity.Year = Convert.ToInt32(year);
                                entity.Month = j + 1;
                                entity.Tgt = Convert.ToDecimal(monthList[j]);
                                entity.Unit = unit;
                                excelDataList.Add(entity);
                            }
                        }

                    }

                }
                //检查是否存在相同的数据如果有则更新，反之则插入
                foreach (var item in excelDataList)
                {
                    //检查输入的单位是否能在维护的表中找到，没有则新增
                    var unitData = unitDataList.Where(p => p.Name.Equals(item.Unit)).FirstOrDefault();
                    if (unitData != null)
                    {
                        item.Unit = unitData.ID;
                    }
                    else
                    {
                        DFM.Model.Models.UnitmanageEntity unitmanageEntity = new DFM.Model.Models.UnitmanageEntity();
                        unitmanageEntity.Name = item.Unit;
                        unitmanageEntity.Enable = 1;
                        unitmanageEntity.Deleted = 0;
                        var unitAdd = _unit.Add(unitmanageEntity);
                        item.Unit = unitmanageEntity.ID;
                    }
                    var oldData = dtgDataList.Where(p => p.SauceType.Equals(item.SauceType) &&
                                                         p.Year == item.Year &&
                                                         p.Month == item.Month && p.Unit.Equals(item.Unit)).FirstOrDefault();
                    if (oldData != null)
                    {
                        oldData.Tgt = item.Tgt;
                        upDataList.Add(oldData);
                    }
                    else
                    {
                        insertDataList.Add(item);
                    }
                }
                if (insertDataList != null && insertDataList.Count > 0)
                {
                    if (await _dal.Add(insertDataList) <= 0)
                    {
                        _unitOfWork.RollbackTran();
                        result.AddError("插入失败");
                        return result;
                    }
                }
                if (upDataList != null && upDataList.Count > 0)
                {
                    if (!await _dal.Update(upDataList))
                    {
                        _unitOfWork.RollbackTran();
                        result.AddError("更新失败");
                        return result;
                    }
                }
                _unitOfWork.CommitTran();
                result.Succeed = true;
                result.Data = string.Format("导入成功，共插入{0}条数据，更新{1}条数据", insertDataList.Count, upDataList.Count);
                return result;
            }
            catch (Exception e)
            {
                _unitOfWork.RollbackTran();
                result.AddError(e.StackTrace.ToString());
                return result;
            }
        }*/
        #endregion

        public async Task<MessageModel<string>> ImportData(IEnumerable<dynamic> data)
        {
            MessageModel<string> result = new MessageModel<string>()
            {
                success = false,
                msg = "导入失败!",
                response = "操作失败！"
            };
            try
            {
                var ProdtgtSaucetypeDataList = await _dal.Query();
                var unitDataList = await _unit.Query();

                var upProdtgtEntityList = new List<ProdtgtSaucetypeEntity>();
                var addProdtgtEntityList = new List<ProdtgtSaucetypeEntity>();
                int rowIndex = 0;
                foreach (IDictionary<string, object> row in data)
                {
                    var Col1 = row["酱料类别"]?.ToString();
                    var Col2 = row["年份"]?.ToString();
                    var Col3 = row["单位"]?.ToString();
                    #region 判断空
                    //判断空
                    if (string.IsNullOrEmpty(Col1))
                    {
                        result.response = string.Format(string.Format(@"第'{0}'行，酱料类别列为空，导入失败", rowIndex + 1));
                        return result;
                    }
                    if (string.IsNullOrEmpty(Col2))
                    {
                        result.response = (string.Format(@"第'{0}'行，年份列为空，导入失败", rowIndex + 1));
                        return result;
                    }
                    if (string.IsNullOrEmpty(Col3))
                    {
                        result.response = (string.Format(@"第'{0}'行，单位列为空，导入失败", rowIndex + 1));
                        return result;
                    }

                    #endregion
                    //检查输入的单位是否能在维护的表中找到，没有则新增
                    string Unit;
                    var unitData = unitDataList.Where(p => p.Name == Col3.Trim().ToUpper()).FirstOrDefault();
                    if (unitData != null)
                    {
                        Unit = unitData.ID;
                    }
                    else
                    {
                        DFM.Model.Models.UnitmanageEntity unitmanageEntity = new DFM.Model.Models.UnitmanageEntity();
                        unitmanageEntity.Name = Col3.Trim();
                        unitmanageEntity.Enable = 1;
                        unitmanageEntity.Deleted = 0;
                        var unitAdd = _unit.Add(unitmanageEntity);
                        Unit = unitmanageEntity.ID;
                    }
                    for (int i = 1; i <= 12; i++)
                    {
                        var Col = row[i + "月"]?.ToString();
                        if (Col != "" && Col != null)
                        {
                            var oldData = ProdtgtSaucetypeDataList.Where(p => p.SauceType == Col1 &&
                               p.Year == Convert.ToInt32(Col2) &&
                               p.Month == Convert.ToInt32(i) &&
                               p.Unit == Unit).FirstOrDefault();

                            if (oldData == null)
                            {
                                oldData = new ProdtgtSaucetypeEntity();
                                oldData.CreateCustomGuid(_user.Name);
                                addProdtgtEntityList.Add(oldData);
                            }
                            else
                            {
                                oldData.Modify(oldData.ID, _user.Name);
                                upProdtgtEntityList.Add(oldData);
                            }

                            oldData.SauceType = Col1;
                            oldData.Year = Convert.ToInt32(Col2);
                            oldData.Unit = Unit;
                            oldData.Month = Convert.ToInt32(i);
                            oldData.Tgt = Convert.ToDecimal(Col);
                        }
                    }
                    rowIndex++;
                }
                int updateCount = 0;
                int addCount = 0;
                _unitOfWork.BeginTran();
                if (upProdtgtEntityList.Count > 0)
                {
                    await _dal.Update(upProdtgtEntityList);
                    updateCount = upProdtgtEntityList.Count;
                }
                if (addProdtgtEntityList.Count > 0)
                {
                    addCount = await _dal.Add(addProdtgtEntityList);
                }
                _unitOfWork.CommitTran();
                result.msg = "导入成功";
                result.response = $"操作成功！新增数据{addCount}条，更新数据{updateCount}条";
            }
            catch (Exception e)
            {
                result.response = e.StackTrace.ToString();
                return result;
            }
            result.success = true;
            return result;
        }
        public class MothModel
        {
            public string m { get; set; }
            public string v { get; set; }
        }
    }
}