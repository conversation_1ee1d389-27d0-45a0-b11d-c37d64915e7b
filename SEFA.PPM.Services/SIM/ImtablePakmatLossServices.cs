
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using SEFA.PPM.Model.ViewModels.MKM.View;
using System;
using SEFA.DFM.Model.Models;
using Microsoft.IdentityModel.Tokens;
using System.Reflection;
using Microsoft.IdentityModel.Protocols.WsFed;
using SEFA.Base.Model.Models;
using System.Data;

namespace SEFA.PPM.Services
{
    public class ImtablePakmatLossServices : BaseServices<ImtablePakmatLossEntity>, IImtablePakmatLossServices
    {
        private readonly IBaseRepository<ImtablePakmatLossEntity> _dal;
        private readonly IBaseRepository<LosstgtEntity> _LosstgtEntity;
        private readonly IBaseRepository<EquipmentEntity> _EquipmentEntity;
        private readonly IBaseRepository<ImtableDowntimerateEntity> _ImtableDowntimerateEntity;
        private readonly IBaseRepository<DowntimetgtEntity> _DowntimetgtEntity;
        public ImtablePakmatLossServices(IBaseRepository<ImtablePakmatLossEntity> dal, IBaseRepository<LosstgtEntity> losstgtEntity, IBaseRepository<EquipmentEntity> equipmentEntity, IBaseRepository<ImtableDowntimerateEntity> imtableDowntimerateEntity, IBaseRepository<DowntimetgtEntity> DowntimetgtEntity)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _LosstgtEntity = losstgtEntity;
            _EquipmentEntity = equipmentEntity;
            _ImtableDowntimerateEntity = imtableDowntimerateEntity;
            _DowntimetgtEntity = DowntimetgtEntity;
        }

        public async Task<List<ImtablePakmatLossEntity>> GetList(ImtablePakmatLossRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<ImtablePakmatLossEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<ImtablePakmatLossEntity>> GetPageList(ImtablePakmatLossRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<ImtablePakmatLossEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

        public async Task<bool> SaveForm(ImtablePakmatLossEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }

        #region 包材损耗率-按分类汇总

        public class PackagingModel
        {
            public decimal? BOMQty { get; set; }
            public decimal? ActualQty { get; set; }
            public DateTime Frequence { get; set; }
            public string MaterialCate { get; set; }
            public string MaterialSubCate { get; set; }
        }

        public class PackagingLineModel
        {
            public decimal? BOMQty { get; set; }
            public decimal? ActualQty { get; set; }
            public DateTime Frequence { get; set; }
            public string LineName { get; set; }
        }
        public static string GetPackagingRateSql(DateTime startTime, DateTime endTime, string WorkCenter, string LineName, string FormulaType, string MaterialCode, string MaterialGroupName, string MaterialCate, string MaterialSubCate, int GroupByFrequence, int GroupByFormulaGroup, int GroupByMaterial,int GroupByMaterialCate,int GroupByMaterialSubCate, int GroupByLineName)
        {
            return string.Format("exec [dbo].[sp_Report_GetPackMaterialProductionLoss] '{0}','{1}','{2}','{3}','{4}','{5}','{6}','{7}','{8}','{9}','{10}','{11}','{12}','{13}','{14}'", startTime, endTime, WorkCenter,  LineName,  FormulaType,  MaterialCode,  MaterialGroupName,  MaterialCate,  MaterialSubCate,  GroupByFrequence,  GroupByFormulaGroup,  GroupByMaterial,  GroupByMaterialCate, GroupByMaterialSubCate, GroupByLineName);
        }

        public static string GetPackagingLineSql(DateTime startTime, DateTime endTime, string WorkCenter, string LineName, string FormulaType, string MaterialCode, string MaterialGroupName, string MaterialCate, string MaterialSubCate, int GroupByFrequence, int GroupByFormulaGroup, int GroupByMaterial, int GroupByMaterialCate, int GroupByMaterialSubCate, int GroupByLineName)
        {
            return string.Format("exec [dbo].[sp_Report_GetPackMaterialProductionLoss] '{0}','{1}','{2}','{3}','{4}','{5}','{6}','{7}','{8}','{9}','{10}','{11}','{12}','{13}','{14}'", startTime, endTime, WorkCenter, LineName, FormulaType, MaterialCode, MaterialGroupName, MaterialCate, MaterialSubCate, GroupByFrequence, GroupByFormulaGroup, GroupByMaterial, GroupByMaterialCate, GroupByMaterialSubCate, GroupByLineName);
        }
        /// <summary>
        /// NEW 包材损耗率-按分类汇总
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<PageModel<PackLossModel>> PackagingRate(ImtablePakmatLossRequestModel reqModel)
        {
            PageModel<PackLossModel> result = new PageModel<PackLossModel>();
            RefAsync<int> dataCount = 0;
            List<PackLossModel> models = new List<PackLossModel>();
            DateTime[] result1 = new DateTime[2];
            var StartTime =result1[0] = new DateTime(reqModel.Year,1, 1); // 年份开始时间：1月1日
            var EndTime = result1[1] = result1[0].AddMonths(12).AddSeconds(-1); 
            //二级、三级
            var sql = GetPackagingRateSql(StartTime,EndTime, reqModel.WorkCenter, reqModel.LineName, reqModel.FormulaType, reqModel.MaterialCode, reqModel.MaterialGroupName, reqModel.MaterialCate, reqModel.MaterialSubCate,3, 0, 0, 1, 1,0);
            var Model = await Task.Run(() =>
              _dal.Db.Ado.GetDataTable(sql)
            );
            List< PackagingModel > packagingModels = new List< PackagingModel >();
            foreach (DataRow item in Model.Rows)
            {
                PackagingModel packaging  = new PackagingModel();
                packaging.BOMQty =Convert.ToDecimal( item["BOMQty"]);
                packaging.ActualQty = Convert.ToDecimal(item["ActualQty"]);
                packaging.Frequence = Convert.ToDateTime(item["Frequence"]);
                packaging.MaterialCate = item["MaterialCate"].ToString();
                packaging.MaterialSubCate = item["MaterialSubCate"].ToString();
                packagingModels.Add(packaging);
            }
            //年度目标-包材损耗-小分类
            var whereExpression2 = Expressionable.Create<LosstgtEntity>()
                .And(p => p.Year == reqModel.Year)
               .And(p => p.LossType == "包材损耗-小分类")//待后续确认存入是什么数据再进行修改
                             .ToExpression();
            var data2 = await _LosstgtEntity.FindList(whereExpression2);
            var list = data2.GroupBy(p => new { p.Item }).Select(p => new { type = p.Key.Item, tgt = p.Sum(t => t.Tgt) }).ToList();
            foreach (var item in packagingModels)
            {
                PackLossModel obj1 = new PackLossModel { };
                models.Add(obj1);

                decimal BOM_Sum = 0;
                decimal loss_Sum = 0;
                decimal LossRate_Sum = 0;
                decimal tgt = 0;
                //小分类年度数据
                var lists1 = list.Where(p => p.type == item.MaterialSubCate).FirstOrDefault();
                if (lists1 != null)
                {
                    tgt = lists1.tgt;
                }
                obj1.BigGroup = item.MaterialCate;
                obj1.SmallGroup = item.MaterialSubCate;
                obj1.AnnualTgt = tgt;
                Type classType = typeof(MaterialLossModel);
                for (int j = 1; j <= 12; j++)
                {
                    PropertyInfo propertyInfo = classType.GetProperty("m" + j);
                    PropertyInfo propertyInfos = classType.GetProperty("s" + j);
                    PropertyInfo propertyInfol = classType.GetProperty("l" + j);
                    decimal countSAP = 0;
                    decimal bomLoss = 0;
                    //损耗率
                    decimal lassRate = 0.0m;
                    decimal stdloss = 0.0m;
                    decimal loss = 0.0m;
                    //小分类主数据
                    var lists = packagingModels.Where(p => p.Frequence.Month == j && p.MaterialCate == item.MaterialCate && p.MaterialSubCate == item.MaterialSubCate).FirstOrDefault();

                    if (lists != null)
                    {
                        if (lists.ActualQty!=null)
                        {
                            //实际
                            countSAP = Convert.ToDecimal(lists.ActualQty);
                        }
                        if (lists.BOMQty!=null)
                        {
                            bomLoss =Convert.ToDecimal( lists.BOMQty);
                        }
                        loss = countSAP - bomLoss;
                        stdloss = bomLoss;
                    }

                    if (stdloss > 0)
                    {
                        lassRate = Math.Round(loss / stdloss, 2);
                    }
                    switch (j)
                    {
                        case 1:
                            obj1.s1 = stdloss;
                            obj1.m1 = loss;
                            obj1.l1 = lassRate;
                            break;
                        case 2:
                            obj1.s2 = stdloss;
                            obj1.m2 = loss;
                            obj1.l2 = lassRate;
                            break;
                        case 3:
                            obj1.s3 = stdloss;
                            obj1.m3 = loss;
                            obj1.l3 = lassRate;
                            break;
                        case 4:
                            obj1.s4 = stdloss;
                            obj1.m4 = loss;
                            obj1.l4 = lassRate;
                            break;
                        case 5:
                            obj1.s5 = stdloss;
                            obj1.m5 = loss;
                            obj1.l5 = lassRate;
                            break;
                        case 6:
                            obj1.s6 = stdloss;
                            obj1.m6 = loss;
                            obj1.l6 = lassRate;
                            break;
                        case 7:
                            obj1.s7 = stdloss;
                            obj1.m7 = loss;
                            obj1.l7 = lassRate;
                            break;
                        case 8:
                            obj1.s8 = stdloss;
                            obj1.m8 = loss;
                            obj1.l8 = lassRate;
                            break;
                        case 9:
                            obj1.s9 = stdloss;
                            obj1.m9 = loss;
                            obj1.l9 = lassRate;
                            break;
                        case 10:
                            obj1.s10 = stdloss;
                            obj1.m10 = loss;
                            obj1.l10 = lassRate;
                            break;
                        case 11:
                            obj1.s11 = stdloss;
                            obj1.m11 = loss;
                            obj1.l11 = lassRate;
                            break;
                        case 12:
                            obj1.s12 = stdloss;
                            obj1.m12 = loss;
                            obj1.l12 = lassRate;
                            break;
                    }

                    //propertyInfo.SetValue(obj1.s1, stdloss);
                    //propertyInfos.SetValue(obj1, loss);
                    //propertyInfol.SetValue(obj1, lassRate);
                    BOM_Sum += stdloss;
                    loss_Sum += loss;
                    LossRate_Sum += lassRate;
                }
                obj1.m13 = BOM_Sum;
                obj1.s13 = loss_Sum;
                obj1.l13 = LossRate_Sum;
            }
            int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引   
            var dataList = models.OrderBy(p => p.BigGroup).Skip(startIndex).Take(reqModel.pageSize).ToList();
            result.dataCount = models.Count;
            result.data = dataList;
            return result;
        }

        /// <summary>
        ///NEW  包材损耗率-按产线汇总
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<PageModel<PackLossLineModel>> PackagingLine(ImtablePakmatLossRequestModel reqModel)
        {
            PageModel<PackLossLineModel> result = new PageModel<PackLossLineModel>();
            RefAsync<int> dataCount = 0;
            DateTime[] result1 = new DateTime[2];
            var StartTime = result1[0] = new DateTime(reqModel.Year, 1, 1); // 年份开始时间：1月1日
            var EndTime = result1[1] = result1[0].AddMonths(12).AddSeconds(-1);
            var sql = GetPackagingLineSql(StartTime, EndTime, reqModel.WorkCenter, reqModel.LineName, reqModel.FormulaType, reqModel.MaterialCode, reqModel.MaterialGroupName, reqModel.MaterialCate, reqModel.MaterialSubCate, 3, 0, 0, 0, 0,1);
            var Model = await Task.Run(() =>
              _dal.Db.Ado.GetDataTable(sql)
            );
            List<PackagingLineModel> lineModels = new List<PackagingLineModel>();
            foreach (DataRow item in Model.Rows)
            {
                PackagingLineModel lineModel = new PackagingLineModel();
                lineModel.BOMQty = Convert.ToDecimal(item["BOMQty"]);
                lineModel.ActualQty = Convert.ToDecimal(item["ActualQty"]);
                lineModel.Frequence = Convert.ToDateTime(item["Frequence"]);
                lineModel.LineName = item["LINENAME"].ToString();
                lineModels.Add(lineModel);
            }
            //包装车间下产线
            var LineModel = await _EquipmentEntity.FindList(p => p.ParentId == "02405071-5072-0299-163e-0370f6000000" && p.Deleted == 0);
            var lineInfo= LineModel.OrderBy(p => p.EquipmentName).ToList();

            //年度目标-包材损耗-生产线
            var whereExpression3 = Expressionable.Create<LosstgtEntity>()
               .And(p => p.Year == reqModel.Year)
              .And(p => p.LossType == "包材损耗-生产线")//待后续确认存入是什么数据再进行修改
                            .ToExpression();
            var data3 = await _LosstgtEntity.FindList(whereExpression3);
            var list1 = data3.GroupBy(p => new { p.ModelRef }).Select(p => new { line = p.Key.ModelRef, tgt = p.Sum(t => t.Tgt) }).ToList();

            List<PackLossLineModel> models = new List<PackLossLineModel>();


            foreach (var item in lineInfo)
            {
                PackLossLineModel obj1 = new PackLossLineModel { };
                models.Add(obj1);
                decimal BOM_Sum = 0;
                decimal loss_Sum = 0;
                decimal LossRate_Sum = 0;
                decimal tgt = 0;
                //小分类年度数据
                var lists1 = list1.Where(p => p.line == item.ID).FirstOrDefault();
                obj1.LineName = item.EquipmentName;
                //model.LossModel.Add(item.EquipmentName);//产线
                if (lists1 != null)
                {
                    tgt = lists1.tgt;
                }
                obj1.AnnualTgt = tgt;//年目标
                Type classType = typeof(PackLossLineModel);
                for (int j = 1; j <= 12; j++)
                {
                    PropertyInfo propertyInfo = classType.GetProperty("m" + j);
                    PropertyInfo propertyInfos = classType.GetProperty("s" + j);
                    PropertyInfo propertyInfol = classType.GetProperty("l" + j);
                    //损耗率
                    decimal lassRate = 0;
                    decimal loss = 0;
                    decimal stdloss = 0;
                    //产线数据
                    var lists = lineModels.Where(p => p.Frequence.Month == j && p.LineName == item.EquipmentName).FirstOrDefault();
                    if (lists != null)
                    {
                        loss = Convert.ToDecimal(lists.ActualQty) -Convert.ToDecimal( lists.BOMQty);
                        stdloss = Convert.ToDecimal(lists.BOMQty);
                        lassRate = Math.Round(loss / stdloss, 2);
                    }
                    propertyInfo.SetValue(obj1, stdloss);
                    propertyInfos.SetValue(obj1, loss);
                    propertyInfol.SetValue(obj1, lassRate);
                    /* model.LossModel.Add(stdloss.ToString());//标准值
                     model.LossModel.Add(loss.ToString());//损耗
                     model.LossModel.Add(lassRate.ToString());//损耗率*/
                    BOM_Sum += stdloss;
                    loss_Sum += loss;
                    LossRate_Sum += lassRate;
                }

                obj1.m13 = BOM_Sum;
                obj1.s13 = loss_Sum;
                obj1.l13 = LossRate_Sum;
                /* model.LossModel.Add(BOM_Sum.ToString());
                 model.LossModel.Add(loss_Sum.ToString());
                 model.LossModel.Add(LossRate_Sum.ToString());
                 models.Add(model);*/
            }
            #region L12
            decimal months = 0;
            decimal month1 = 0;
            decimal month2 = 0;
            decimal month3 = 0;
            decimal month4 = 0;
            decimal month5 = 0;
            decimal month6 = 0;
            decimal month7 = 0;
            decimal month8 = 0;
            decimal month9 = 0;
            decimal month10 = 0;
            decimal month11 = 0;
            decimal month12 = 0;
            decimal month13 = 0;
            decimal month14 = 0;
            decimal month15 = 0;
            decimal month16 = 0;
            decimal month17 = 0;
            decimal month18 = 0;
            decimal month19 = 0;
            decimal month20 = 0;
            decimal month21 = 0;
            decimal month22 = 0;
            decimal month23 = 0;
            decimal month24 = 0;
            decimal month25 = 0;
            decimal month26 = 0;
            decimal month27 = 0;
            decimal month28 = 0;
            decimal month29 = 0;
            decimal month30 = 0;
            decimal month31 = 0;
            decimal month32 = 0;
            decimal month33 = 0;
            decimal month34 = 0;
            decimal month35 = 0;
            decimal month36 = 0;
            //年累计
            decimal month37 = 0;
            decimal month38 = 0;
            decimal month39 = 0;

            #endregion

            #region L14
            decimal months_1 = 0;
            decimal months_2 = 0;
            decimal months_3 = 0;
            decimal months_4 = 0;
            decimal months_5 = 0;
            decimal months_6 = 0;
            decimal months_7 = 0;
            decimal months_8 = 0;
            decimal months_9 = 0;
            decimal months_10 = 0;
            decimal months_11 = 0;
            decimal months_12 = 0;
            decimal months_13 = 0;
            decimal months_14 = 0;
            decimal months_15 = 0;
            decimal months_16 = 0;
            decimal months_17 = 0;
            decimal months_18 = 0;
            decimal months_19 = 0;
            decimal months_20 = 0;
            decimal months_21 = 0;
            decimal months_22 = 0;
            decimal months_23 = 0;
            decimal months_24 = 0;
            decimal months_25 = 0;
            decimal months_26 = 0;
            decimal months_27 = 0;
            decimal months_28 = 0;
            decimal months_29 = 0;
            decimal months_30 = 0;
            decimal months_31 = 0;
            decimal months_32 = 0;
            decimal months_33 = 0;
            decimal months_34 = 0;
            decimal months_35 = 0;
            decimal months_36 = 0;
            decimal months_37 = 0;
            //年累计
            decimal months_38 = 0;
            decimal months_39 = 0;
            decimal months_40 = 0;

            #endregion

            #region L19
            decimal monthss_1 = 0;
            decimal monthss_2 = 0;
            decimal monthss_3 = 0;
            decimal monthss_4 = 0;
            decimal monthss_5 = 0;
            decimal monthss_6 = 0;
            decimal monthss_7 = 0;
            decimal monthss_8 = 0;
            decimal monthss_9 = 0;
            decimal monthss_10 = 0;
            decimal monthss_11 = 0;
            decimal monthss_12 = 0;
            decimal monthss_13 = 0;
            decimal monthss_14 = 0;
            decimal monthss_15 = 0;
            decimal monthss_16 = 0;
            decimal monthss_17 = 0;
            decimal monthss_18 = 0;
            decimal monthss_19 = 0;
            decimal monthss_20 = 0;
            decimal monthss_21 = 0;
            decimal monthss_22 = 0;
            decimal monthss_23 = 0;
            decimal monthss_24 = 0;
            decimal monthss_25 = 0;
            decimal monthss_26 = 0;
            decimal monthss_27 = 0;
            decimal monthss_28 = 0;
            decimal monthss_29 = 0;
            decimal monthss_30 = 0;
            decimal monthss_31 = 0;
            decimal monthss_32 = 0;
            decimal monthss_33 = 0;
            decimal monthss_34 = 0;
            decimal monthss_35 = 0;
            decimal monthss_36 = 0;
            decimal monthss_37 = 0;
            //年累计
            decimal monthss_38 = 0;
            decimal monthss_39 = 0;
            decimal monthss_40 = 0;

            #endregion
            for (int i = 0; i < models.Count; i++)
            {
                var line = models[i].LineName;
                if (line.Contains("L12"))
                {
                    months += Convert.ToDecimal(models[i].AnnualTgt);
                    month1 += Convert.ToDecimal(models[i].m1);
                    month2 += Convert.ToDecimal(models[i].s1);
                    month3 += Convert.ToDecimal(models[i].l1);
                    month4 += Convert.ToDecimal(models[i].m2);
                    month5 += Convert.ToDecimal(models[i].s2);
                    month6 += Convert.ToDecimal(models[i].l2);
                    month7 += Convert.ToDecimal(models[i].m3);
                    month8 += Convert.ToDecimal(models[i].s3);
                    month9 += Convert.ToDecimal(models[i].l3);
                    month10 += Convert.ToDecimal(models[i].m4);
                    month11 += Convert.ToDecimal(models[i].s4);
                    month12 += Convert.ToDecimal(models[i].l4);
                    month13 += Convert.ToDecimal(models[i].m5);
                    month14 += Convert.ToDecimal(models[i].s5);
                    month15 += Convert.ToDecimal(models[i].l5);
                    month16 += Convert.ToDecimal(models[i].m6);
                    month17 += Convert.ToDecimal(models[i].s6);
                    month18 += Convert.ToDecimal(models[i].l6);
                    month19 += Convert.ToDecimal(models[i].m7);
                    month20 += Convert.ToDecimal(models[i].s7);
                    month21 += Convert.ToDecimal(models[i].l7);
                    month22 += Convert.ToDecimal(models[i].m8);
                    month23 += Convert.ToDecimal(models[i].s8);
                    month24 += Convert.ToDecimal(models[i].l8);
                    month25 += Convert.ToDecimal(models[i].m9);
                    month26 += Convert.ToDecimal(models[i].s9);
                    month27 += Convert.ToDecimal(models[i].l9);
                    month28 += Convert.ToDecimal(models[i].m10);
                    month29 += Convert.ToDecimal(models[i].s10);
                    month30 += Convert.ToDecimal(models[i].l10);
                    month31 += Convert.ToDecimal(models[i].m11);
                    month32 += Convert.ToDecimal(models[i].s11);
                    month33 += Convert.ToDecimal(models[i].l11);
                    month34 += Convert.ToDecimal(models[i].m12);
                    month35 += Convert.ToDecimal(models[i].s12);
                    month36 += Convert.ToDecimal(models[i].l12);
                    month37 += Convert.ToDecimal(models[i].m13);
                    month38 += Convert.ToDecimal(models[i].s13);
                    month39 += Convert.ToDecimal(models[i].l13);
                }

                if (line.Contains("L14"))
                {
                    months_1 += Convert.ToDecimal(models[i].AnnualTgt);
                    months_2 += Convert.ToDecimal(models[i].m1);
                    months_3 += Convert.ToDecimal(models[i].s1);
                    months_4 += Convert.ToDecimal(models[i].l1);
                    months_5 += Convert.ToDecimal(models[i].m2);
                    months_6 += Convert.ToDecimal(models[i].s2);
                    months_7 += Convert.ToDecimal(models[i].l2);
                    months_8 += Convert.ToDecimal(models[i].m3);
                    months_9 += Convert.ToDecimal(models[i].s3);
                    months_10 += Convert.ToDecimal(models[i].l3);
                    months_11 += Convert.ToDecimal(models[i].m4);
                    months_12 += Convert.ToDecimal(models[i].s4);
                    months_13 += Convert.ToDecimal(models[i].l4);
                    months_14 += Convert.ToDecimal(models[i].m5);
                    months_15 += Convert.ToDecimal(models[i].s5);
                    months_16 += Convert.ToDecimal(models[i].l5);
                    months_17 += Convert.ToDecimal(models[i].m6);
                    months_18 += Convert.ToDecimal(models[i].s6);
                    months_19 += Convert.ToDecimal(models[i].l6);
                    months_20 += Convert.ToDecimal(models[i].m7);
                    months_21 += Convert.ToDecimal(models[i].s7);
                    months_22 += Convert.ToDecimal(models[i].l7);
                    months_23 += Convert.ToDecimal(models[i].m8);
                    months_24 += Convert.ToDecimal(models[i].s8);
                    months_25 += Convert.ToDecimal(models[i].l8);
                    months_26 += Convert.ToDecimal(models[i].m9);
                    months_27 += Convert.ToDecimal(models[i].s9);
                    months_28 += Convert.ToDecimal(models[i].l9);
                    months_29 += Convert.ToDecimal(models[i].m10);
                    months_30 += Convert.ToDecimal(models[i].s10);
                    months_31 += Convert.ToDecimal(models[i].l10);
                    months_32 += Convert.ToDecimal(models[i].m11);
                    months_33 += Convert.ToDecimal(models[i].s11);
                    months_34 += Convert.ToDecimal(models[i].l11);
                    months_35 += Convert.ToDecimal(models[i].m12);
                    months_36 += Convert.ToDecimal(models[i].s12);
                    months_37 += Convert.ToDecimal(models[i].l12);
                    months_38 += Convert.ToDecimal(models[i].m13);
                    months_39 += Convert.ToDecimal(models[i].s13);
                    months_40 += Convert.ToDecimal(models[i].l13);
                }

                if (line.Contains("L19"))
                {

                    monthss_1 += Convert.ToDecimal(models[i].AnnualTgt);
                    monthss_2 += Convert.ToDecimal(models[i].m1);
                    monthss_3 += Convert.ToDecimal(models[i].s1);
                    monthss_4 += Convert.ToDecimal(models[i].l1);
                    monthss_5 += Convert.ToDecimal(models[i].m2);
                    monthss_6 += Convert.ToDecimal(models[i].s2);
                    monthss_7 += Convert.ToDecimal(models[i].l2);
                    monthss_8 += Convert.ToDecimal(models[i].m3);
                    monthss_9 += Convert.ToDecimal(models[i].s3);
                    monthss_10 += Convert.ToDecimal(models[i].l3);
                    monthss_11 += Convert.ToDecimal(models[i].m4);
                    monthss_12 += Convert.ToDecimal(models[i].s4);
                    monthss_13 += Convert.ToDecimal(models[i].l4);
                    monthss_14 += Convert.ToDecimal(models[i].m5);
                    monthss_15 += Convert.ToDecimal(models[i].s5);
                    monthss_16 += Convert.ToDecimal(models[i].l5);
                    monthss_17 += Convert.ToDecimal(models[i].m6);
                    monthss_18 += Convert.ToDecimal(models[i].s6);
                    monthss_19 += Convert.ToDecimal(models[i].l6);
                    monthss_20 += Convert.ToDecimal(models[i].m7);
                    monthss_21 += Convert.ToDecimal(models[i].s7);
                    monthss_22 += Convert.ToDecimal(models[i].l7);
                    monthss_23 += Convert.ToDecimal(models[i].m8);
                    monthss_24 += Convert.ToDecimal(models[i].s8);
                    monthss_25 += Convert.ToDecimal(models[i].l8);
                    monthss_26 += Convert.ToDecimal(models[i].m9);
                    monthss_27 += Convert.ToDecimal(models[i].s9);
                    monthss_28 += Convert.ToDecimal(models[i].l9);
                    monthss_29 += Convert.ToDecimal(models[i].m10);
                    monthss_30 += Convert.ToDecimal(models[i].s10);
                    monthss_31 += Convert.ToDecimal(models[i].l10);
                    monthss_32 += Convert.ToDecimal(models[i].m11);
                    monthss_33 += Convert.ToDecimal(models[i].s11);
                    monthss_34 += Convert.ToDecimal(models[i].l11);
                    monthss_35 += Convert.ToDecimal(models[i].m12);
                    monthss_36 += Convert.ToDecimal(models[i].s12);
                    monthss_37 += Convert.ToDecimal(models[i].l12);
                    monthss_38 += Convert.ToDecimal(models[i].m13);
                    monthss_39 += Convert.ToDecimal(models[i].s13);
                    monthss_40 += Convert.ToDecimal(models[i].l13);

                }
            }
            PackLossLineModel L12 = new PackLossLineModel { };
            PackLossLineModel L14 = new PackLossLineModel { };
            PackLossLineModel L19 = new PackLossLineModel { };
            models.Add(L12);
            models.Add(L14);
            models.Add(L19);
            #region 组合L12合并
            L12.LineName = "L12总计";
            L12.AnnualTgt = months;
            L12.m1 = month1;
            L12.s1 = month2;
            L12.l1 = month3;
            L12.m2 = month4;
            L12.s2 = month5;
            L12.l2 = month6;
            L12.m3 = month7;
            L12.s3 = month8;
            L12.l3 = month9;
            L12.m4 = month10;
            L12.s4 = month11;
            L12.l4 = month12;
            L12.m5 = month13;
            L12.s5 = month14;
            L12.l5 = month15;
            L12.m6 = month16;
            L12.s6 = month17;
            L12.l6 = month18;
            L12.m7 = month19;
            L12.s7 = month20;
            L12.l7 = month21;
            L12.m8 = month22;
            L12.s8 = month23;
            L12.l8 = month24;
            L12.m9 = month25;
            L12.s9 = month26;
            L12.l9 = month27;
            L12.m10 = month28;
            L12.s10 = month29;
            L12.l10 = month30;
            L12.m11 = month31;
            L12.s11 = month32;
            L12.l11 = month33;
            L12.m12 = month34;
            L12.s12 = month35;
            L12.l12 = month36;
            L12.m13 = month37;
            L12.s13 = month38;
            L12.l13 = month39;
            #endregion
            #region 组合L14合并

            L14.LineName = "L14总计";
            L14.AnnualTgt = months_1;
            L14.m1 = months_2;
            L14.s1 = months_3;
            L14.l1 = months_4;
            L14.m2 = months_5;
            L14.s2 = months_6;
            L14.l2 = months_7;
            L14.m3 = months_8;
            L14.s3 = months_9;
            L14.l3 = months_10;
            L14.m4 = months_11;
            L14.s4 = months_12;
            L14.l4 = months_13;
            L14.m5 = months_14;
            L14.s5 = months_15;
            L14.l5 = months_16;
            L14.m6 = months_17;
            L14.s6 = months_18;
            L14.l6 = months_19;
            L14.m7 = months_20;
            L14.s7 = months_21;
            L14.l7 = months_22;
            L14.m8 = months_23;
            L14.s8 = months_24;
            L14.l8 = months_25;
            L14.m9 = months_26;
            L14.s9 = months_27;
            L14.l9 = months_28;
            L14.m10 = months_29;
            L14.s10 = months_30;
            L14.l10 = months_31;
            L14.m11 = months_32;
            L14.s11 = months_33;
            L14.l11 = months_34;
            L14.m12 = months_35;
            L14.s12 = months_36;
            L14.l12 = months_37;
            L14.m13 = months_38;
            L14.s13 = months_39;
            L14.l13 = months_40;
            #endregion
            #region 组合L19合并
            L19.LineName = "L19总计";
            L19.AnnualTgt = monthss_1;
            L19.m1 = monthss_2;
            L19.s1 = monthss_3;
            L19.l1 = monthss_4;
            L19.m2 = monthss_5;
            L19.s2 = monthss_6;
            L19.l2 = monthss_7;
            L19.m3 = monthss_8;
            L19.s3 = monthss_9;
            L19.l3 = monthss_10;
            L19.m4 = monthss_11;
            L19.s4 = monthss_12;
            L19.l4 = monthss_13;
            L19.m5 = monthss_14;
            L19.s5 = monthss_15;
            L19.l5 = monthss_16;
            L19.m6 = monthss_17;
            L19.s6 = monthss_18;
            L19.l6 = monthss_19;
            L19.m7 = monthss_20;
            L19.s7 = monthss_21;
            L19.l7 = monthss_22;
            L19.m8 = monthss_23;
            L19.s8 = monthss_24;
            L19.l8 = monthss_25;
            L19.m9 = monthss_26;
            L19.s9 = monthss_27;
            L19.l9 = monthss_28;
            L19.m10 = monthss_29;
            L19.s10 = monthss_30;
            L19.l10 = monthss_31;
            L19.m11 = monthss_32;
            L19.s11 = monthss_33;
            L19.l11 = monthss_34;
            L19.m12 = monthss_35;
            L19.s12 = monthss_36;
            L19.l12 = monthss_37;
            L19.m13 = monthss_38;
            L19.s13 = monthss_39;
            L19.l13 = monthss_40;
            #endregion
            int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引   
            var dataList = models.OrderBy(p => p.LineName).Skip(startIndex).Take(reqModel.pageSize).ToList();
            result.dataCount = models.Count;
            result.data = dataList;
            return result;

        }

        /*
/// <summary>
/// 包材损耗率-按分类汇总
/// </summary>
/// <param name="reqModel"></param>
/// <returns></returns>
public async Task<PageModel<PackLossModel>> PackagingRate(ImtablePakmatLossRequestModel reqModel)
{
    PageModel<PackLossModel> result = new PageModel<PackLossModel>();
    RefAsync<int> dataCount = 0;
    var whereExpression = Expressionable.Create<ImtablePakmatLossEntity>()
        .And(p => p.Year == reqModel.Year)
                     .ToExpression();
    var data = await _dal.FindList(whereExpression);
    //根据类别进行分类
    var datas = data.GroupBy(p => new { p.Month, p.SecondPakgroup, p.Pakgroup }).Select(p => new { month = p.Key.Month, group = p.Key.SecondPakgroup, paskGroup = p.Key.Pakgroup, stdloss = p.Sum(t => t.StdLoss), countSAP = p.Sum(t => t.ConsumedActual), bomLoss = p.Sum(t => t.BomWithoutLoss) }).ToList();

    //年度目标-包材损耗-小分类
    var whereExpression2 = Expressionable.Create<LosstgtEntity>()
        .And(p => p.Year == reqModel.Year)
       .And(p => p.LossType == "包材损耗-小分类")//待后续确认存入是什么数据再进行修改
                     .ToExpression();
    var data2 = await _LosstgtEntity.FindList(whereExpression2);
    var list = data2.GroupBy(p => new { p.Item }).Select(p => new { type = p.Key.Item, tgt = p.Sum(t => t.Tgt) }).ToList();

    List<PackLossModel> models = new List<PackLossModel>();

    foreach (var item in datas)
    {
        PackLossModel obj1 = new PackLossModel { };
        models.Add(obj1);

        decimal BOM_Sum = 0;
        decimal loss_Sum = 0;
        decimal LossRate_Sum = 0;
        decimal tgt = 0;
        //小分类年度数据
        var lists1 = list.Where(p => p.type == item.paskGroup).FirstOrDefault();
        if (lists1 != null)
        {
            tgt = lists1.tgt;
        }
        obj1.BigGroup = item.group;
        obj1.SmallGroup = item.paskGroup;
        obj1.AnnualTgt = tgt;
        Type classType = typeof(MaterialLossModel);
        for (int j = 1; j <= 12; j++)
        {
            PropertyInfo propertyInfo = classType.GetProperty("m" + j);
            PropertyInfo propertyInfos = classType.GetProperty("s" + j);
            PropertyInfo propertyInfol = classType.GetProperty("l" + j);
            decimal countSAP = 0;
            decimal bomLoss = 0;
            //损耗率
            decimal lassRate = 0.0m;
            decimal stdloss = 0.0m;
            decimal loss = 0.0m;
            //小分类主数据
            var lists = datas.Where(p => p.month == j && p.group == item.group && p.paskGroup == item.paskGroup).FirstOrDefault();

            if (lists != null)
            {
                if (true)
                {
                    countSAP = lists.countSAP;
                }
                if (true)
                {
                    bomLoss = lists.bomLoss;
                }
                loss = countSAP - bomLoss;
                stdloss = lists.stdloss;
            }

            if (stdloss > 0)
            {
                lassRate = Math.Round(loss / stdloss, 2);
            }
            switch (j)
            {
                case 1:
                    obj1.s1 = stdloss;
                    obj1.m1 = loss;
                    obj1.l1 = lassRate;
                    break;
                case 2:
                    obj1.s2 = stdloss;
                    obj1.m2 = loss;
                    obj1.l2 = lassRate;
                    break;
                case 3:
                    obj1.s3 = stdloss;
                    obj1.m3 = loss;
                    obj1.l3 = lassRate;
                    break;
                case 4:
                    obj1.s4 = stdloss;
                    obj1.m4 = loss;
                    obj1.l4 = lassRate;
                    break;
                case 5:
                    obj1.s5 = stdloss;
                    obj1.m5 = loss;
                    obj1.l5 = lassRate;
                    break;
                case 6:
                    obj1.s6 = stdloss;
                    obj1.m6 = loss;
                    obj1.l6 = lassRate;
                    break;
                case 7:
                    obj1.s7 = stdloss;
                    obj1.m7 = loss;
                    obj1.l7 = lassRate;
                    break;
                case 8:
                    obj1.s8 = stdloss;
                    obj1.m8 = loss;
                    obj1.l8 = lassRate;
                    break;
                case 9:
                    obj1.s9 = stdloss;
                    obj1.m9 = loss;
                    obj1.l9 = lassRate;
                    break;
                case 10:
                    obj1.s10 = stdloss;
                    obj1.m10 = loss;
                    obj1.l10 = lassRate;
                    break;
                case 11:
                    obj1.s11 = stdloss;
                    obj1.m11 = loss;
                    obj1.l11 = lassRate;
                    break;
                case 12:
                    obj1.s12 = stdloss;
                    obj1.m12 = loss;
                    obj1.l12 = lassRate;
                    break;
            }

            //propertyInfo.SetValue(obj1.s1, stdloss);
            //propertyInfos.SetValue(obj1, loss);
            //propertyInfol.SetValue(obj1, lassRate);
            BOM_Sum += stdloss;
            loss_Sum += loss;
            LossRate_Sum += lassRate;
        }
        obj1.m13 = BOM_Sum;
        obj1.s13 = loss_Sum;
        obj1.l13 = LossRate_Sum;
    }
    int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引   
    var dataList = models.OrderBy(p => p.BigGroup).Skip(startIndex).Take(reqModel.pageSize).ToList();
    result.dataCount = dataList.Count;
    result.data = dataList;
    return result;
    //return models;
}
*/
        #endregion
        /**/
        /// <summary>
        /// 包材损耗率-按产线汇总
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<PageModel<PackLossLineModel>> PackagingLine1(ImtablePakmatLossRequestModel reqModel)
        {
            PageModel<PackLossLineModel> result = new PageModel<PackLossLineModel>();
            var whereExpression = Expressionable.Create<ImtablePakmatLossEntity>()
                .And(p => p.Year == reqModel.Year)
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            //根据产线进行分类
            var datas = data.GroupBy(p => new { p.Month, p.LineId }).Select(p => new { month = p.Key.Month, lineName = p.Key.LineId, stdloss = p.Sum(t => t.StdLoss), countSAP = p.Sum(t => t.ConsumedActual), bomLoss = p.Sum(t => t.BomWithoutLoss) }).ToList();

            //包装车间下产线
            var LineModel = await _EquipmentEntity.FindList(p => p.ParentId == "02405071-5072-0299-163e-0370f6000000" && p.Deleted == 0);
            LineModel.OrderBy(p => p.EquipmentName).Select(p => p.EquipmentName).ToList();

            //年度目标-包材损耗-生产线
            var whereExpression3 = Expressionable.Create<LosstgtEntity>()
               .And(p => p.Year == reqModel.Year)
              .And(p => p.LossType == "包材损耗-生产线")//待后续确认存入是什么数据再进行修改
                            .ToExpression();
            var data3 = await _LosstgtEntity.FindList(whereExpression3);
            var list1 = data3.GroupBy(p => new { p.ModelRef }).Select(p => new { line = p.Key.ModelRef, tgt = p.Sum(t => t.Tgt) }).ToList();

            List<PackLossLineModel> models = new List<PackLossLineModel>();
            
           
            foreach (var item in LineModel)
            {
                PackLossLineModel obj1 = new PackLossLineModel { };
                models.Add(obj1);
                // MaterialLossModel model = new MaterialLossModel();
                //model.LossModel = new List<string>();
                decimal BOM_Sum = 0;
                decimal loss_Sum = 0;
                decimal LossRate_Sum = 0;
                decimal tgt = 0;
                //小分类年度数据
                var lists1 = list1.Where(p => p.line == item.ID).FirstOrDefault();
                obj1.LineName = item.EquipmentName;
                //model.LossModel.Add(item.EquipmentName);//产线
                if (lists1 != null)
                {
                    tgt = lists1.tgt;
                }
                obj1.AnnualTgt = tgt;//年目标
                //model.LossModel.Add(tgt.ToString());
                Type classType = typeof(PackLossLineModel);
                for (int j = 1; j <= 12; j++)
                {
                    PropertyInfo propertyInfo = classType.GetProperty("m" + j);
                    PropertyInfo propertyInfos = classType.GetProperty("s" + j);
                    PropertyInfo propertyInfol = classType.GetProperty("l" + j);
                    //损耗率
                    decimal lassRate = 0;
                    decimal loss = 0;
                    decimal stdloss = 0;
                    //产线数据
                    var lists = datas.Where(p => p.month == j && p.lineName == item.ID).FirstOrDefault();
                    if (lists != null)
                    {
                        loss = lists.countSAP - lists.bomLoss;
                        stdloss = lists.stdloss;
                        lassRate = Math.Round(loss / lists.stdloss, 2);
                    }
                    propertyInfo.SetValue(obj1, stdloss);
                    propertyInfos.SetValue(obj1, loss);
                    propertyInfol.SetValue(obj1, lassRate);
                   /* model.LossModel.Add(stdloss.ToString());//标准值
                    model.LossModel.Add(loss.ToString());//损耗
                    model.LossModel.Add(lassRate.ToString());//损耗率*/
                    BOM_Sum += stdloss;
                    loss_Sum += loss;
                    LossRate_Sum += lassRate;
                }

                obj1.m13 = BOM_Sum;
                obj1.s13 = loss_Sum;
                obj1.l13 = LossRate_Sum;
               /* model.LossModel.Add(BOM_Sum.ToString());
                model.LossModel.Add(loss_Sum.ToString());
                model.LossModel.Add(LossRate_Sum.ToString());
                models.Add(model);*/
            }
            #region L12
            decimal months = 0;
            decimal month1 = 0;
            decimal month2 = 0;
            decimal month3 = 0;
            decimal month4 = 0;
            decimal month5 = 0;
            decimal month6 = 0;
            decimal month7 = 0;
            decimal month8 = 0;
            decimal month9 = 0;
            decimal month10 = 0;
            decimal month11 = 0;
            decimal month12 = 0;
            decimal month13 = 0;
            decimal month14 = 0;
            decimal month15 = 0;
            decimal month16 = 0;
            decimal month17 = 0;
            decimal month18 = 0;
            decimal month19 = 0;
            decimal month20 = 0;
            decimal month21 = 0;
            decimal month22 = 0;
            decimal month23 = 0;
            decimal month24 = 0;
            decimal month25 = 0;
            decimal month26 = 0;
            decimal month27 = 0;
            decimal month28 = 0;
            decimal month29 = 0;
            decimal month30 = 0;
            decimal month31 = 0;
            decimal month32 = 0;
            decimal month33 = 0;
            decimal month34 = 0;
            decimal month35 = 0;
            decimal month36 = 0;
            //年累计
            decimal month37 = 0;
            decimal month38 = 0;
            decimal month39 = 0;

            #endregion

            #region L14
            decimal months_1 = 0;
            decimal months_2 = 0;
            decimal months_3 = 0;
            decimal months_4 = 0;
            decimal months_5 = 0;
            decimal months_6 = 0;
            decimal months_7 = 0;
            decimal months_8 = 0;
            decimal months_9 = 0;
            decimal months_10 = 0;
            decimal months_11 = 0;
            decimal months_12 = 0;
            decimal months_13 = 0;
            decimal months_14 = 0;
            decimal months_15 = 0;
            decimal months_16 = 0;
            decimal months_17 = 0;
            decimal months_18 = 0;
            decimal months_19 = 0;
            decimal months_20 = 0;
            decimal months_21 = 0;
            decimal months_22 = 0;
            decimal months_23 = 0;
            decimal months_24 = 0;
            decimal months_25 = 0;
            decimal months_26 = 0;
            decimal months_27 = 0;
            decimal months_28 = 0;
            decimal months_29 = 0;
            decimal months_30 = 0;
            decimal months_31 = 0;
            decimal months_32 = 0;
            decimal months_33 = 0;
            decimal months_34 = 0;
            decimal months_35 = 0;
            decimal months_36 = 0;
            decimal months_37 = 0;
            //年累计
            decimal months_38 = 0;
            decimal months_39 = 0;
            decimal months_40 = 0;

            #endregion

            #region L19
            decimal monthss_1 = 0;
            decimal monthss_2 = 0;
            decimal monthss_3 = 0;
            decimal monthss_4 = 0;
            decimal monthss_5 = 0;
            decimal monthss_6 = 0;
            decimal monthss_7 = 0;
            decimal monthss_8 = 0;
            decimal monthss_9 = 0;
            decimal monthss_10 = 0;
            decimal monthss_11 = 0;
            decimal monthss_12 = 0;
            decimal monthss_13 = 0;
            decimal monthss_14 = 0;
            decimal monthss_15 = 0;
            decimal monthss_16 = 0;
            decimal monthss_17 = 0;
            decimal monthss_18 = 0;
            decimal monthss_19 = 0;
            decimal monthss_20 = 0;
            decimal monthss_21 = 0;
            decimal monthss_22 = 0;
            decimal monthss_23 = 0;
            decimal monthss_24 = 0;
            decimal monthss_25 = 0;
            decimal monthss_26 = 0;
            decimal monthss_27 = 0;
            decimal monthss_28 = 0;
            decimal monthss_29 = 0;
            decimal monthss_30 = 0;
            decimal monthss_31 = 0;
            decimal monthss_32 = 0;
            decimal monthss_33 = 0;
            decimal monthss_34 = 0;
            decimal monthss_35 = 0;
            decimal monthss_36 = 0;
            decimal monthss_37 = 0;
            //年累计
            decimal monthss_38 = 0;
            decimal monthss_39 = 0;
            decimal monthss_40 = 0;

            #endregion
            for (int i = 0; i < models.Count; i++)
            {
                var line = models[i].LineName;
                if (line.Contains("L12"))
                {
                    months += Convert.ToDecimal(models[i].AnnualTgt);
                    month1 += Convert.ToDecimal(models[i].m1);
                    month2 += Convert.ToDecimal(models[i].s1);
                    month3 += Convert.ToDecimal(models[i].l1);
                    month4 += Convert.ToDecimal(models[i].m2);
                    month5 += Convert.ToDecimal(models[i].s2);
                    month6 += Convert.ToDecimal(models[i].l2);
                    month7 += Convert.ToDecimal(models[i].m3);
                    month8 += Convert.ToDecimal(models[i].s3);
                    month9 += Convert.ToDecimal(models[i].l3);
                    month10 += Convert.ToDecimal(models[i].m4);
                    month11 += Convert.ToDecimal(models[i].s4);
                    month12 += Convert.ToDecimal(models[i].l4);
                    month13 += Convert.ToDecimal(models[i].m5);
                    month14 += Convert.ToDecimal(models[i].s5);
                    month15 += Convert.ToDecimal(models[i].l5);
                    month16 += Convert.ToDecimal(models[i].m6);
                    month17 += Convert.ToDecimal(models[i].s6);
                    month18 += Convert.ToDecimal(models[i].l6);
                    month19 += Convert.ToDecimal(models[i].m7);
                    month20 += Convert.ToDecimal(models[i].s7);
                    month21 += Convert.ToDecimal(models[i].l7);
                    month22 += Convert.ToDecimal(models[i].m8);
                    month23 += Convert.ToDecimal(models[i].s8);
                    month24 += Convert.ToDecimal(models[i].l8);
                    month25 += Convert.ToDecimal(models[i].m9);
                    month26 += Convert.ToDecimal(models[i].s9);
                    month27 += Convert.ToDecimal(models[i].l9);
                    month28 += Convert.ToDecimal(models[i].m10);
                    month29 += Convert.ToDecimal(models[i].s10);
                    month30 += Convert.ToDecimal(models[i].l10);
                    month31 += Convert.ToDecimal(models[i].m11);
                    month32 += Convert.ToDecimal(models[i].s11);
                    month33 += Convert.ToDecimal(models[i].l11);
                    month34 += Convert.ToDecimal(models[i].m12);
                    month35 += Convert.ToDecimal(models[i].s12);
                    month36 += Convert.ToDecimal(models[i].l12);
                    month37 += Convert.ToDecimal(models[i].m13);
                    month38 += Convert.ToDecimal(models[i].s13);
                    month39 += Convert.ToDecimal(models[i].l13);
                }

                if (line.Contains("L14"))
                {
                    months_1 += Convert.ToDecimal(models[i].AnnualTgt);
                    months_2 += Convert.ToDecimal(models[i].m1);
                    months_3 += Convert.ToDecimal(models[i].s1);
                    months_4 += Convert.ToDecimal(models[i].l1);
                    months_5 += Convert.ToDecimal(models[i].m2);
                    months_6 += Convert.ToDecimal(models[i].s2);
                    months_7 += Convert.ToDecimal(models[i].l2);
                    months_8 += Convert.ToDecimal(models[i].m3);
                    months_9 += Convert.ToDecimal(models[i].s3);
                    months_10 += Convert.ToDecimal(models[i].l3);
                    months_11 += Convert.ToDecimal(models[i].m4);
                    months_12 += Convert.ToDecimal(models[i].s4);
                    months_13 += Convert.ToDecimal(models[i].l4);
                    months_14 += Convert.ToDecimal(models[i].m5);
                    months_15 += Convert.ToDecimal(models[i].s5);
                    months_16 += Convert.ToDecimal(models[i].l5);
                    months_17 += Convert.ToDecimal(models[i].m6);
                    months_18 += Convert.ToDecimal(models[i].s6);
                    months_19 += Convert.ToDecimal(models[i].l6);
                    months_20 += Convert.ToDecimal(models[i].m7);
                    months_21 += Convert.ToDecimal(models[i].s7);
                    months_22 += Convert.ToDecimal(models[i].l7);
                    months_23 += Convert.ToDecimal(models[i].m8);
                    months_24 += Convert.ToDecimal(models[i].s8);
                    months_25 += Convert.ToDecimal(models[i].l8);
                    months_26 += Convert.ToDecimal(models[i].m9);
                    months_27 += Convert.ToDecimal(models[i].s9);
                    months_28 += Convert.ToDecimal(models[i].l9);
                    months_29 += Convert.ToDecimal(models[i].m10);
                    months_30 += Convert.ToDecimal(models[i].s10);
                    months_31 += Convert.ToDecimal(models[i].l10);
                    months_32 += Convert.ToDecimal(models[i].m11);
                    months_33 += Convert.ToDecimal(models[i].s11);
                    months_34 += Convert.ToDecimal(models[i].l11);
                    months_35 += Convert.ToDecimal(models[i].m12);
                    months_36 += Convert.ToDecimal(models[i].s12);
                    months_37 += Convert.ToDecimal(models[i].l12);
                    months_38 += Convert.ToDecimal(models[i].m13);
                    months_39 += Convert.ToDecimal(models[i].s13);
                    months_40 += Convert.ToDecimal(models[i].l13);

/*                    months_1 += Convert.ToDecimal(models[i].LossModel[1]);
                    months_2 += Convert.ToDecimal(models[i].LossModel[2]);
                    months_3 += Convert.ToDecimal(models[i].LossModel[3]);
                    months_4 += Convert.ToDecimal(models[i].LossModel[4]);
                    months_5 += Convert.ToDecimal(models[i].LossModel[5]);
                    months_6 += Convert.ToDecimal(models[i].LossModel[6]);
                    months_7 += Convert.ToDecimal(models[i].LossModel[7]);
                    months_8 += Convert.ToDecimal(models[i].LossModel[8]);
                    months_9 += Convert.ToDecimal(models[i].LossModel[9]);
                    months_10 += Convert.ToDecimal(models[i].LossModel[10]);
                    months_11 += Convert.ToDecimal(models[i].LossModel[11]);
                    months_12 += Convert.ToDecimal(models[i].LossModel[12]);
                    months_13 += Convert.ToDecimal(models[i].LossModel[13]);
                    months_14 += Convert.ToDecimal(models[i].LossModel[14]);
                    months_15 += Convert.ToDecimal(models[i].LossModel[15]);
                    months_16 += Convert.ToDecimal(models[i].LossModel[16]);
                    months_17 += Convert.ToDecimal(models[i].LossModel[17]);
                    months_18 += Convert.ToDecimal(models[i].LossModel[18]);
                    months_19 += Convert.ToDecimal(models[i].LossModel[19]);
                    months_20 += Convert.ToDecimal(models[i].LossModel[20]);
                    months_21 += Convert.ToDecimal(models[i].LossModel[21]);
                    months_22 += Convert.ToDecimal(models[i].LossModel[22]);
                    months_23 += Convert.ToDecimal(models[i].LossModel[23]);
                    months_24 += Convert.ToDecimal(models[i].LossModel[24]);
                    months_25 += Convert.ToDecimal(models[i].LossModel[25]);
                    months_26 += Convert.ToDecimal(models[i].LossModel[26]);
                    months_27 += Convert.ToDecimal(models[i].LossModel[27]);
                    months_28 += Convert.ToDecimal(models[i].LossModel[28]);
                    months_29 += Convert.ToDecimal(models[i].LossModel[29]);
                    months_30 += Convert.ToDecimal(models[i].LossModel[30]);
                    months_31 += Convert.ToDecimal(models[i].LossModel[31]);
                    months_32 += Convert.ToDecimal(models[i].LossModel[32]);
                    months_33 += Convert.ToDecimal(models[i].LossModel[33]);
                    months_34 += Convert.ToDecimal(models[i].LossModel[34]);
                    months_35 += Convert.ToDecimal(models[i].LossModel[35]);
                    months_36 += Convert.ToDecimal(models[i].LossModel[36]);
                    months_37 += Convert.ToDecimal(models[i].LossModel[37]);
                    months_38 += Convert.ToDecimal(models[i].LossModel[38]);
                    months_39 += Convert.ToDecimal(models[i].LossModel[39]);
                    months_40 += Convert.ToDecimal(models[i].LossModel[40]);*/
                }

                if (line.Contains("L19"))
                {

                    monthss_1 += Convert.ToDecimal(models[i].AnnualTgt);
                    monthss_2 += Convert.ToDecimal(models[i].m1);
                    monthss_3 += Convert.ToDecimal(models[i].s1);
                    monthss_4 += Convert.ToDecimal(models[i].l1);
                    monthss_5 += Convert.ToDecimal(models[i].m2);
                    monthss_6 += Convert.ToDecimal(models[i].s2);
                    monthss_7 += Convert.ToDecimal(models[i].l2);
                    monthss_8 += Convert.ToDecimal(models[i].m3);
                    monthss_9 += Convert.ToDecimal(models[i].s3);
                    monthss_10 += Convert.ToDecimal(models[i].l3);
                    monthss_11 += Convert.ToDecimal(models[i].m4);
                    monthss_12 += Convert.ToDecimal(models[i].s4);
                    monthss_13 += Convert.ToDecimal(models[i].l4);
                    monthss_14 += Convert.ToDecimal(models[i].m5);
                    monthss_15 += Convert.ToDecimal(models[i].s5);
                    monthss_16 += Convert.ToDecimal(models[i].l5);
                    monthss_17 += Convert.ToDecimal(models[i].m6);
                    monthss_18 += Convert.ToDecimal(models[i].s6);
                    monthss_19 += Convert.ToDecimal(models[i].l6);
                    monthss_20 += Convert.ToDecimal(models[i].m7);
                    monthss_21 += Convert.ToDecimal(models[i].s7);
                    monthss_22 += Convert.ToDecimal(models[i].l7);
                    monthss_23 += Convert.ToDecimal(models[i].m8);
                    monthss_24 += Convert.ToDecimal(models[i].s8);
                    monthss_25 += Convert.ToDecimal(models[i].l8);
                    monthss_26 += Convert.ToDecimal(models[i].m9);
                    monthss_27 += Convert.ToDecimal(models[i].s9);
                    monthss_28 += Convert.ToDecimal(models[i].l9);
                    monthss_29 += Convert.ToDecimal(models[i].m10);
                    monthss_30 += Convert.ToDecimal(models[i].s10);
                    monthss_31 += Convert.ToDecimal(models[i].l10);
                    monthss_32 += Convert.ToDecimal(models[i].m11);
                    monthss_33 += Convert.ToDecimal(models[i].s11);
                    monthss_34 += Convert.ToDecimal(models[i].l11);
                    monthss_35 += Convert.ToDecimal(models[i].m12);
                    monthss_36 += Convert.ToDecimal(models[i].s12);
                    monthss_37 += Convert.ToDecimal(models[i].l12);
                    monthss_38 += Convert.ToDecimal(models[i].m13);
                    monthss_39 += Convert.ToDecimal(models[i].s13);
                    monthss_40 += Convert.ToDecimal(models[i].l13);

/*                    monthss_1 += Convert.ToDecimal(models[i].LossModel[1]);
                    monthss_2 += Convert.ToDecimal(models[i].LossModel[2]);
                    monthss_3 += Convert.ToDecimal(models[i].LossModel[3]);
                    monthss_4 += Convert.ToDecimal(models[i].LossModel[4]);
                    monthss_5 += Convert.ToDecimal(models[i].LossModel[5]);
                    monthss_6 += Convert.ToDecimal(models[i].LossModel[6]);
                    monthss_7 += Convert.ToDecimal(models[i].LossModel[7]);
                    monthss_8 += Convert.ToDecimal(models[i].LossModel[8]);
                    monthss_9 += Convert.ToDecimal(models[i].LossModel[9]);
                    monthss_10 += Convert.ToDecimal(models[i].LossModel[10]);
                    monthss_11 += Convert.ToDecimal(models[i].LossModel[11]);
                    monthss_12 += Convert.ToDecimal(models[i].LossModel[12]);
                    monthss_13 += Convert.ToDecimal(models[i].LossModel[13]);
                    monthss_14 += Convert.ToDecimal(models[i].LossModel[14]);
                    monthss_15 += Convert.ToDecimal(models[i].LossModel[15]);
                    monthss_16 += Convert.ToDecimal(models[i].LossModel[16]);
                    monthss_17 += Convert.ToDecimal(models[i].LossModel[17]);
                    monthss_18 += Convert.ToDecimal(models[i].LossModel[18]);
                    monthss_19 += Convert.ToDecimal(models[i].LossModel[19]);
                    monthss_20 += Convert.ToDecimal(models[i].LossModel[20]);
                    monthss_21 += Convert.ToDecimal(models[i].LossModel[21]);
                    monthss_22 += Convert.ToDecimal(models[i].LossModel[22]);
                    monthss_23 += Convert.ToDecimal(models[i].LossModel[23]);
                    monthss_24 += Convert.ToDecimal(models[i].LossModel[24]);
                    monthss_25 += Convert.ToDecimal(models[i].LossModel[25]);
                    monthss_26 += Convert.ToDecimal(models[i].LossModel[26]);
                    monthss_27 += Convert.ToDecimal(models[i].LossModel[27]);
                    monthss_28 += Convert.ToDecimal(models[i].LossModel[28]);
                    monthss_29 += Convert.ToDecimal(models[i].LossModel[29]);
                    monthss_30 += Convert.ToDecimal(models[i].LossModel[30]);
                    monthss_31 += Convert.ToDecimal(models[i].LossModel[31]);
                    monthss_32 += Convert.ToDecimal(models[i].LossModel[32]);
                    monthss_33 += Convert.ToDecimal(models[i].LossModel[33]);
                    monthss_34 += Convert.ToDecimal(models[i].LossModel[34]);
                    monthss_35 += Convert.ToDecimal(models[i].LossModel[35]);
                    monthss_36 += Convert.ToDecimal(models[i].LossModel[36]);
                    monthss_37 += Convert.ToDecimal(models[i].LossModel[37]);
                    monthss_38 += Convert.ToDecimal(models[i].LossModel[38]);
                    monthss_39 += Convert.ToDecimal(models[i].LossModel[39]);
                    monthss_40 += Convert.ToDecimal(models[i].LossModel[40]);*/
                }
            }
            PackLossLineModel L12 = new PackLossLineModel { };
            PackLossLineModel L14 = new PackLossLineModel { };
            PackLossLineModel L19 = new PackLossLineModel { };
            models.Add(L12);
            models.Add(L14);
            models.Add(L19);
            #region 组合L12合并
            L12.LineName = "L12总计";
            L12.AnnualTgt = months;
            L12.m1= month1;
            L12.s1= month2;
            L12.l1= month3;
            L12.m2= month4;
            L12.s2= month5;
            L12.l2= month6;
            L12.m3= month7;
            L12.s3= month8;
            L12.l3= month9;
            L12.m4= month10;
            L12.s4= month11;
            L12.l4= month12;
            L12.m5= month13;
            L12.s5= month14;
            L12.l5= month15;
            L12.m6= month16;
            L12.s6= month17;
            L12.l6= month18;
            L12.m7= month19;
            L12.s7= month20;
            L12.l7= month21;
            L12.m8= month22;
            L12.s8= month23;
            L12.l8= month24;
            L12.m9= month25;
            L12.s9= month26;
            L12.l9= month27;
            L12.m10= month28;
            L12.s10= month29;
            L12.l10= month30;
            L12.m11= month31;
            L12.s11= month32;
            L12.l11= month33;
            L12.m12= month34;
            L12.s12= month35;
            L12.l12= month36;
            L12.m13= month37;
            L12.s13= month38;
            L12.l13= month39;
            /*

                        MaterialLossModel materialLossModel = new MaterialLossModel();
                        materialLossModel.LossModel = new List<string>();
                        materialLossModel.LossModel.Add(months.ToString());
                        materialLossModel.LossModel.Add(month1.ToString());
                        materialLossModel.LossModel.Add(month2.ToString());
                        materialLossModel.LossModel.Add(month3.ToString());
                        materialLossModel.LossModel.Add(month4.ToString());
                        materialLossModel.LossModel.Add(month5.ToString());
                        materialLossModel.LossModel.Add(month6.ToString());
                        materialLossModel.LossModel.Add(month7.ToString());
                        materialLossModel.LossModel.Add(month8.ToString());
                        materialLossModel.LossModel.Add(month9.ToString());
                        materialLossModel.LossModel.Add(month10.ToString());
                        materialLossModel.LossModel.Add(month11.ToString());
                        materialLossModel.LossModel.Add(month12.ToString());
                        materialLossModel.LossModel.Add(month13.ToString());
                        materialLossModel.LossModel.Add(month14.ToString());
                        materialLossModel.LossModel.Add(month15.ToString());
                        materialLossModel.LossModel.Add(month16.ToString());
                        materialLossModel.LossModel.Add(month17.ToString());
                        materialLossModel.LossModel.Add(month18.ToString());
                        materialLossModel.LossModel.Add(month19.ToString());
                        materialLossModel.LossModel.Add(month20.ToString());
                        materialLossModel.LossModel.Add(month21.ToString());
                        materialLossModel.LossModel.Add(month22.ToString());
                        materialLossModel.LossModel.Add(month23.ToString());
                        materialLossModel.LossModel.Add(month24.ToString());
                        materialLossModel.LossModel.Add(month25.ToString());
                        materialLossModel.LossModel.Add(month26.ToString());
                        materialLossModel.LossModel.Add(month27.ToString());
                        materialLossModel.LossModel.Add(month28.ToString());
                        materialLossModel.LossModel.Add(month29.ToString());
                        materialLossModel.LossModel.Add(month30.ToString());
                        materialLossModel.LossModel.Add(month31.ToString());
                        materialLossModel.LossModel.Add(month32.ToString());
                        materialLossModel.LossModel.Add(month33.ToString());
                        materialLossModel.LossModel.Add(month34.ToString());
                        materialLossModel.LossModel.Add(month35.ToString());
                        materialLossModel.LossModel.Add(month36.ToString());
                        materialLossModel.LossModel.Add(month37.ToString());
                        materialLossModel.LossModel.Add(month38.ToString());
                        materialLossModel.LossModel.Add(month39.ToString());

                        models.Add(materialLossModel);*/
            #endregion
            #region 组合L14合并

            L14.LineName = "L14总计";
            L14.AnnualTgt = months_1;
            L14.m1 = months_2;
            L14.s1 = months_3;
            L14.l1 = months_4;
            L14.m2 = months_5;
            L14.s2 = months_6;
            L14.l2 = months_7;
            L14.m3 = months_8;
            L14.s3 = months_9;
            L14.l3 = months_10;
            L14.m4 = months_11;
            L14.s4 = months_12;
            L14.l4 = months_13;
            L14.m5 = months_14;
            L14.s5 = months_15;
            L14.l5 = months_16;
            L14.m6 = months_17;
            L14.s6 = months_18;
            L14.l6 = months_19;
            L14.m7 = months_20;
            L14.s7 = months_21;
            L14.l7 = months_22;
            L14.m8 = months_23;
            L14.s8 = months_24;
            L14.l8 = months_25;
            L14.m9 = months_26;
            L14.s9 = months_27;
            L14.l9 = months_28;
            L14.m10 = months_29;
            L14.s10 = months_30;
            L14.l10 = months_31;
            L14.m11 = months_32;
            L14.s11 = months_33;
            L14.l11 = months_34;
            L14.m12 = months_35;
            L14.s12 = months_36;
            L14.l12 = months_37;
            L14.m13 = months_38;
            L14.s13 = months_39;
            L14.l13 = months_40;
            /*MaterialLossModel materialLossModel1 = new MaterialLossModel();
            materialLossModel1.LossModel = new List<string>();
            materialLossModel1.LossModel.Add(months_1.ToString());
            materialLossModel1.LossModel.Add(months_2.ToString());
            materialLossModel1.LossModel.Add(months_3.ToString());
            materialLossModel1.LossModel.Add(months_4.ToString());
            materialLossModel1.LossModel.Add(months_5.ToString());
            materialLossModel1.LossModel.Add(months_6.ToString());
            materialLossModel1.LossModel.Add(months_7.ToString());
            materialLossModel1.LossModel.Add(months_8.ToString());
            materialLossModel1.LossModel.Add(months_9.ToString());
            materialLossModel1.LossModel.Add(months_10.ToString());
            materialLossModel1.LossModel.Add(months_11.ToString());
            materialLossModel1.LossModel.Add(months_12.ToString());
            materialLossModel1.LossModel.Add(months_13.ToString());
            materialLossModel1.LossModel.Add(months_14.ToString());
            materialLossModel1.LossModel.Add(months_15.ToString());
            materialLossModel1.LossModel.Add(months_16.ToString());
            materialLossModel1.LossModel.Add(months_17.ToString());
            materialLossModel1.LossModel.Add(months_18.ToString());
            materialLossModel1.LossModel.Add(months_19.ToString());
            materialLossModel1.LossModel.Add(months_20.ToString());
            materialLossModel1.LossModel.Add(months_21.ToString());
            materialLossModel1.LossModel.Add(months_22.ToString());
            materialLossModel1.LossModel.Add(months_23.ToString());
            materialLossModel1.LossModel.Add(months_24.ToString());
            materialLossModel1.LossModel.Add(months_25.ToString());
            materialLossModel1.LossModel.Add(months_26.ToString());
            materialLossModel1.LossModel.Add(months_27.ToString());
            materialLossModel1.LossModel.Add(months_28.ToString());
            materialLossModel1.LossModel.Add(months_29.ToString());
            materialLossModel1.LossModel.Add(months_30.ToString());
            materialLossModel1.LossModel.Add(months_31.ToString());
            materialLossModel1.LossModel.Add(months_32.ToString());
            materialLossModel1.LossModel.Add(months_33.ToString());
            materialLossModel1.LossModel.Add(months_34.ToString());
            materialLossModel1.LossModel.Add(months_35.ToString());
            materialLossModel1.LossModel.Add(months_36.ToString());
            materialLossModel1.LossModel.Add(months_37.ToString());
            materialLossModel1.LossModel.Add(months_38.ToString());
            materialLossModel1.LossModel.Add(months_39.ToString());
            materialLossModel1.LossModel.Add(months_40.ToString());
            models.Add(materialLossModel1);*/
            #endregion
            #region 组合L19合并
            L19.LineName = "L19总计";
            L19.AnnualTgt = monthss_1;
            L19.m1 = monthss_2;
            L19.s1 = monthss_3;
            L19.l1 = monthss_4;
            L19.m2 = monthss_5;
            L19.s2 = monthss_6;
            L19.l2 = monthss_7;
            L19.m3 = monthss_8;
            L19.s3 = monthss_9;
            L19.l3 = monthss_10;
            L19.m4 = monthss_11;
            L19.s4 = monthss_12;
            L19.l4 = monthss_13;
            L19.m5 = monthss_14;
            L19.s5 = monthss_15;
            L19.l5 = monthss_16;
            L19.m6 = monthss_17;
            L19.s6 = monthss_18;
            L19.l6 = monthss_19;
            L19.m7 = monthss_20;
            L19.s7 = monthss_21;
            L19.l7 = monthss_22;
            L19.m8 = monthss_23;
            L19.s8 = monthss_24;
            L19.l8 = monthss_25;
            L19.m9 = monthss_26;
            L19.s9 = monthss_27;
            L19.l9 = monthss_28;
            L19.m10 = monthss_29;
            L19.s10 = monthss_30;
            L19.l10 = monthss_31;
            L19.m11 = monthss_32;
            L19.s11 = monthss_33;
            L19.l11 = monthss_34;
            L19.m12 = monthss_35;
            L19.s12 = monthss_36;
            L19.l12 = monthss_37;
            L19.m13 = monthss_38;
            L19.s13 = monthss_39;
            L19.l13 = monthss_40;
            /*MaterialLossModel materialLossModel2 = new MaterialLossModel();
            materialLossModel2.LossModel = new List<string>();
            materialLossModel2.LossModel.Add(monthss_1.ToString());
            materialLossModel2.LossModel.Add(monthss_2.ToString());
            materialLossModel2.LossModel.Add(monthss_3.ToString());
            materialLossModel2.LossModel.Add(monthss_4.ToString());
            materialLossModel2.LossModel.Add(monthss_5.ToString());
            materialLossModel2.LossModel.Add(monthss_6.ToString());
            materialLossModel2.LossModel.Add(monthss_7.ToString());
            materialLossModel2.LossModel.Add(monthss_8.ToString());
            materialLossModel2.LossModel.Add(monthss_9.ToString());
            materialLossModel2.LossModel.Add(monthss_10.ToString());
            materialLossModel2.LossModel.Add(monthss_11.ToString());
            materialLossModel2.LossModel.Add(monthss_12.ToString());
            materialLossModel2.LossModel.Add(monthss_13.ToString());
            materialLossModel2.LossModel.Add(monthss_14.ToString());
            materialLossModel2.LossModel.Add(monthss_15.ToString());
            materialLossModel2.LossModel.Add(monthss_16.ToString());
            materialLossModel2.LossModel.Add(monthss_17.ToString());
            materialLossModel2.LossModel.Add(monthss_18.ToString());
            materialLossModel2.LossModel.Add(monthss_19.ToString());
            materialLossModel2.LossModel.Add(monthss_20.ToString());
            materialLossModel2.LossModel.Add(monthss_21.ToString());
            materialLossModel2.LossModel.Add(monthss_22.ToString());
            materialLossModel2.LossModel.Add(monthss_23.ToString());
            materialLossModel2.LossModel.Add(monthss_24.ToString());
            materialLossModel2.LossModel.Add(monthss_25.ToString());
            materialLossModel2.LossModel.Add(monthss_26.ToString());
            materialLossModel2.LossModel.Add(monthss_27.ToString());
            materialLossModel2.LossModel.Add(monthss_28.ToString());
            materialLossModel2.LossModel.Add(monthss_29.ToString());
            materialLossModel2.LossModel.Add(monthss_30.ToString());
            materialLossModel2.LossModel.Add(monthss_31.ToString());
            materialLossModel2.LossModel.Add(monthss_32.ToString());
            materialLossModel2.LossModel.Add(monthss_33.ToString());
            materialLossModel2.LossModel.Add(monthss_34.ToString());
            materialLossModel2.LossModel.Add(monthss_35.ToString());
            materialLossModel2.LossModel.Add(monthss_36.ToString());
            materialLossModel2.LossModel.Add(monthss_37.ToString());
            materialLossModel2.LossModel.Add(monthss_38.ToString());
            materialLossModel2.LossModel.Add(monthss_39.ToString());
            materialLossModel2.LossModel.Add(monthss_40.ToString());
            models.Add(materialLossModel2);*/
            #endregion
            int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引   
            var dataList = models.OrderBy(p => p.LineName).Skip(startIndex).Take(reqModel.pageSize).ToList();
            result.dataCount = dataList.Count;
            result.data = dataList;
            return result;
            
        }


      
        /// <summary>
        /// 包装线设备（灌装机）停机率汇总（停机）
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<DownTimeListModel> DownTimeList(ImtableDowntimerateRequestModel reqModel)
        {
            //数据
            var whereExpression = Expressionable.Create<ImtableDowntimerateEntity>()
                .And(p=>p.Year==reqModel.Year && p.Month==reqModel.Month)
                             .ToExpression();
            var data = await _ImtableDowntimerateEntity.FindList(whereExpression);
            var list = data.GroupBy(p => new { p.Year, p.Month, p.LineId })
                .Select( p => 
                new { 
                    year = p.Key.Year, 
                    month = p.Key.Month, 
                    linid = p.Key.LineId, 
                    Time1 = p.Sum(t => t.RunningTime),
                    Time2=p.Sum(t => t.TotalOccupationTime),
                    Time3=p.Sum(t=>t.TotalDowntime),
                    Time4=p.Sum(t=>t.DowntimeAffactProducion)
                    }
                ).ToList();

            //包装车间下产线
            var LineModel = await _EquipmentEntity.FindList(p => p.ParentId == "02405071-5072-0299-163e-0370f6000000" && p.Deleted == 0);
            LineModel.OrderBy(p => p.EquipmentName).Select(p => p.EquipmentName).ToList();

            //目标
            var whereExpression1 = Expressionable.Create<DowntimetgtEntity>()
                    .And(p => p.Year == reqModel.Year && p.Month == reqModel.Month)
                    .ToExpression();
            var data1 = await _DowntimetgtEntity.FindList(whereExpression1);
            //List< DownTimeListModel > models=new List< DownTimeListModel >();
            List<DownTimeModel> models1=new List<DownTimeModel>();
            DownTimeListModel model = new DownTimeListModel();
            foreach (var item in LineModel)
            {
                DownTimeModel timeModel= new DownTimeModel();   
                //某月/某产线的
                var downTime = list.Where(p => p.linid == item.ID).FirstOrDefault();
                
                //某月/某产线的目标
                var target = data1.Where(p => p.LineId == item.ID).FirstOrDefault();
                decimal time1 = 0;
                decimal time2 = 0;
                decimal time3 = 0;
                decimal time4 = 0;
                decimal targer = 0;
                decimal TotalDowntimeRate = 0;
                decimal AffactDowntimeRate = 0;
                if (downTime!=null)
                {
                    time1 = downTime.Time1;
                    time2 = downTime.Time2;
                    time3 = downTime.Time3;
                    time4 = downTime.Time4;
                }
                if (target!=null)
                {
                    targer = target.Tgt;
                }
                timeModel.LineName = item.EquipmentName;
                timeModel.Target = targer;
                timeModel.RunningTime = time1;
                timeModel.TotalOccupationTime = time2;
                timeModel.TotalDowntime = time3;
                timeModel.DowntimeAffactProducion = time4;
                if (time2!=0)
                {
                    TotalDowntimeRate = Math.Round(time3 / time2, 3);
                }
                timeModel.TotalDowntimeRate = TotalDowntimeRate;
                if (time1!=0)
                {
                    AffactDowntimeRate=Math.Round(time4 / time1, 3);
                }
                timeModel.AffactDowntimeRate = AffactDowntimeRate;
                models1.Add(timeModel);
            }
            model.Year = reqModel.Year;
            model.Month = reqModel.Month;
            model.DownTimeModels = models1;
            //models.Add(model);
            return model;
        }

    }
}