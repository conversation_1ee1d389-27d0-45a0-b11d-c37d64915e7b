
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.PPM.Services
{
    public class KpiTgtViewServices : BaseServices<KpiTgtViewEntity>, IKpiTgtViewServices
    {
        private readonly IBaseRepository<KpiTgtViewEntity> _dal;
        public KpiTgtViewServices(IBaseRepository<KpiTgtViewEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<KpiTgtViewEntity>> GetList(KpiTgtViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<KpiTgtViewEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<KpiTgtViewEntity>> GetPageList(KpiTgtViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<KpiTgtViewEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

        public async Task<bool> SaveForm(KpiTgtViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}