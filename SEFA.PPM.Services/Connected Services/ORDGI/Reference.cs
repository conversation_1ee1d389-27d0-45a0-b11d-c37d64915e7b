//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//
//     对此文件的更改可能导致不正确的行为，并在以下条件下丢失:
//     代码重新生成。
// </auto-generated>
//------------------------------------------------------------------------------

namespace ORDGI
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="urn:sap-com:document:sap:rfc:functions", ConfigurationName="ORDGI.ZWS_PP_MES_ORDGI")]
    public interface ZWS_PP_MES_ORDGI
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:sap-com:document:sap:rfc:functions:ZWS_PP_MES_ORDGI:ZRFC_PP_MES_ORDGIRequest", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ORDGI.ZRFC_PP_MES_ORDGIResponse1> ZRFC_PP_MES_ORDGIAsync(ORDGI.ZRFC_PP_MES_ORDGIRequest request);
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="urn:sap-com:document:sap:rfc:functions")]
    public partial class ZRFC_PP_MES_ORDGI
    {
        
        private string bUDATField;
        
        private string bWARTField;
        
        private ZPP_MES_ORDGI[] iT_GIField;
        
        private ZPP_MES_GIMTDOC[] iT_RETField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string BUDAT
        {
            get
            {
                return this.bUDATField;
            }
            set
            {
                this.bUDATField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string BWART
        {
            get
            {
                return this.bWARTField;
            }
            set
            {
                this.bWARTField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public ZPP_MES_ORDGI[] IT_GI
        {
            get
            {
                return this.iT_GIField;
            }
            set
            {
                this.iT_GIField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public ZPP_MES_GIMTDOC[] IT_RET
        {
            get
            {
                return this.iT_RETField;
            }
            set
            {
                this.iT_RETField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sap-com:document:sap:rfc:functions")]
    public partial class ZPP_MES_ORDGI
    {
        
        private string aUFNRField;
        
        private string mATNRField;
        
        private string cHARGField;
        
        private decimal eRFMGField;
        
        private string eRFMEField;
        
        private string lGORTField;
        
        private string kZEARField;
        
        private string mJAHR_CField;
        
        private string mBLNR_CField;
        
        private string zEILE_CField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AUFNR
        {
            get
            {
                return this.aUFNRField;
            }
            set
            {
                this.aUFNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string MATNR
        {
            get
            {
                return this.mATNRField;
            }
            set
            {
                this.mATNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string CHARG
        {
            get
            {
                return this.cHARGField;
            }
            set
            {
                this.cHARGField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public decimal ERFMG
        {
            get
            {
                return this.eRFMGField;
            }
            set
            {
                this.eRFMGField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string ERFME
        {
            get
            {
                return this.eRFMEField;
            }
            set
            {
                this.eRFMEField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string LGORT
        {
            get
            {
                return this.lGORTField;
            }
            set
            {
                this.lGORTField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string KZEAR
        {
            get
            {
                return this.kZEARField;
            }
            set
            {
                this.kZEARField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string MJAHR_C
        {
            get
            {
                return this.mJAHR_CField;
            }
            set
            {
                this.mJAHR_CField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string MBLNR_C
        {
            get
            {
                return this.mBLNR_CField;
            }
            set
            {
                this.mBLNR_CField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string ZEILE_C
        {
            get
            {
                return this.zEILE_CField;
            }
            set
            {
                this.zEILE_CField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sap-com:document:sap:rfc:functions")]
    public partial class ZPP_MES_GIMTDOC
    {
        
        private string tYPEField;
        
        private string mJAHRField;
        
        private string mBLNRField;
        
        private string zEILEField;
        
        private string aUFNRField;
        
        private string mJAHR_CField;
        
        private string mBLNR_CField;
        
        private string zEILE_CField;
        
        private string mSGField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string TYPE
        {
            get
            {
                return this.tYPEField;
            }
            set
            {
                this.tYPEField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string MJAHR
        {
            get
            {
                return this.mJAHRField;
            }
            set
            {
                this.mJAHRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string MBLNR
        {
            get
            {
                return this.mBLNRField;
            }
            set
            {
                this.mBLNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string ZEILE
        {
            get
            {
                return this.zEILEField;
            }
            set
            {
                this.zEILEField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string AUFNR
        {
            get
            {
                return this.aUFNRField;
            }
            set
            {
                this.aUFNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string MJAHR_C
        {
            get
            {
                return this.mJAHR_CField;
            }
            set
            {
                this.mJAHR_CField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string MBLNR_C
        {
            get
            {
                return this.mBLNR_CField;
            }
            set
            {
                this.mBLNR_CField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string ZEILE_C
        {
            get
            {
                return this.zEILE_CField;
            }
            set
            {
                this.zEILE_CField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string MSG
        {
            get
            {
                return this.mSGField;
            }
            set
            {
                this.mSGField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="urn:sap-com:document:sap:rfc:functions")]
    public partial class ZRFC_PP_MES_ORDGIResponse
    {
        
        private ZPP_MES_ORDGI[] iT_GIField;
        
        private ZPP_MES_GIMTDOC[] iT_RETField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public ZPP_MES_ORDGI[] IT_GI
        {
            get
            {
                return this.iT_GIField;
            }
            set
            {
                this.iT_GIField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public ZPP_MES_GIMTDOC[] IT_RET
        {
            get
            {
                return this.iT_RETField;
            }
            set
            {
                this.iT_RETField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class ZRFC_PP_MES_ORDGIRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sap-com:document:sap:rfc:functions", Order=0)]
        public ORDGI.ZRFC_PP_MES_ORDGI ZRFC_PP_MES_ORDGI;
        
        public ZRFC_PP_MES_ORDGIRequest()
        {
        }
        
        public ZRFC_PP_MES_ORDGIRequest(ORDGI.ZRFC_PP_MES_ORDGI ZRFC_PP_MES_ORDGI)
        {
            this.ZRFC_PP_MES_ORDGI = ZRFC_PP_MES_ORDGI;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class ZRFC_PP_MES_ORDGIResponse1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sap-com:document:sap:rfc:functions", Order=0)]
        public ORDGI.ZRFC_PP_MES_ORDGIResponse ZRFC_PP_MES_ORDGIResponse;
        
        public ZRFC_PP_MES_ORDGIResponse1()
        {
        }
        
        public ZRFC_PP_MES_ORDGIResponse1(ORDGI.ZRFC_PP_MES_ORDGIResponse ZRFC_PP_MES_ORDGIResponse)
        {
            this.ZRFC_PP_MES_ORDGIResponse = ZRFC_PP_MES_ORDGIResponse;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public interface ZWS_PP_MES_ORDGIChannel : ORDGI.ZWS_PP_MES_ORDGI, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public partial class ZWS_PP_MES_ORDGIClient : System.ServiceModel.ClientBase<ORDGI.ZWS_PP_MES_ORDGI>, ORDGI.ZWS_PP_MES_ORDGI
    {
        
        public ZWS_PP_MES_ORDGIClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<ORDGI.ZRFC_PP_MES_ORDGIResponse1> ORDGI.ZWS_PP_MES_ORDGI.ZRFC_PP_MES_ORDGIAsync(ORDGI.ZRFC_PP_MES_ORDGIRequest request)
        {
            return base.Channel.ZRFC_PP_MES_ORDGIAsync(request);
        }
        
        public System.Threading.Tasks.Task<ORDGI.ZRFC_PP_MES_ORDGIResponse1> ZRFC_PP_MES_ORDGIAsync(ORDGI.ZRFC_PP_MES_ORDGI ZRFC_PP_MES_ORDGI)
        {
            ORDGI.ZRFC_PP_MES_ORDGIRequest inValue = new ORDGI.ZRFC_PP_MES_ORDGIRequest();
            inValue.ZRFC_PP_MES_ORDGI = ZRFC_PP_MES_ORDGI;
            return ((ORDGI.ZWS_PP_MES_ORDGI)(this)).ZRFC_PP_MES_ORDGIAsync(inValue);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
    }
}
