//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//
//     对此文件的更改可能导致不正确的行为，并在以下条件下丢失:
//     代码重新生成。
// </auto-generated>
//------------------------------------------------------------------------------

namespace MM_WMS_STOCK_TRANSFER
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="urn:sap-com:document:sap:rfc:functions", ConfigurationName="MM_WMS_STOCK_TRANSFER.ZWS_MM_WMS_STOCK_TRANSFER")]
    public interface ZWS_MM_WMS_STOCK_TRANSFER
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:sap-com:document:sap:rfc:functions:ZWS_MM_WMS_STOCK_TRANSFER:ZRFC_MM_WMS_STOC" +
            "K_TRANSFERRequest", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<MM_WMS_STOCK_TRANSFER.ZRFC_MM_WMS_STOCK_TRANSFERResponse1> ZRFC_MM_WMS_STOCK_TRANSFERAsync(MM_WMS_STOCK_TRANSFER.ZRFC_MM_WMS_STOCK_TRANSFERRequest request);
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="urn:sap-com:document:sap:rfc:functions")]
    public partial class ZRFC_MM_WMS_STOCK_TRANSFER
    {
        
        private ZRFC_WMS_MSEG[] eT_MSEGField;
        
        private ZRFC_WMS_MESSAGE[] eT_MSGField;
        
        private ZRFC_WMS_MKPF iS_MKPFField;
        
        private ZRFC_WMS_MSEG[] iT_MSEGField;
        
        private string iV_UNAMEField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public ZRFC_WMS_MSEG[] ET_MSEG
        {
            get
            {
                return this.eT_MSEGField;
            }
            set
            {
                this.eT_MSEGField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public ZRFC_WMS_MESSAGE[] ET_MSG
        {
            get
            {
                return this.eT_MSGField;
            }
            set
            {
                this.eT_MSGField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public ZRFC_WMS_MKPF IS_MKPF
        {
            get
            {
                return this.iS_MKPFField;
            }
            set
            {
                this.iS_MKPFField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public ZRFC_WMS_MSEG[] IT_MSEG
        {
            get
            {
                return this.iT_MSEGField;
            }
            set
            {
                this.iT_MSEGField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string IV_UNAME
        {
            get
            {
                return this.iV_UNAMEField;
            }
            set
            {
                this.iV_UNAMEField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sap-com:document:sap:rfc:functions")]
    public partial class ZRFC_WMS_MSEG
    {
        
        private string mBLNRField;
        
        private string mJAHRField;
        
        private string zEILEField;
        
        private string lINE_IDField;
        
        private string pARENT_IDField;
        
        private string bWARTField;
        
        private string xAUTOField;
        
        private string mATNRField;
        
        private decimal mENGEField;
        
        private string mEINSField;
        
        private string hSDATField;
        
        private string vFDATField;
        
        private string sHKZGField;
        
        private string wERKSField;
        
        private string lGORTField;
        
        private string cHARGField;
        
        private string iNSMKField;
        
        private string uMMATField;
        
        private string uMWRKField;
        
        private string uMLGOField;
        
        private string uMCHAField;
        
        private string lFBNRField;
        
        private string lFPOSField;
        
        private string eBELNField;
        
        private string eBELPField;
        
        private string kOSTLField;
        
        private string sAKTOField;
        
        private string wEMPFField;
        
        private string pS_PSP_PNRField;
        
        private string pOSIDField;
        
        private string lIFNRField;
        
        private string gRUNDField;
        
        private string sGTXTField;
        
        private string vLTYPField;
        
        private string vLBERField;
        
        private string vLPLAField;
        
        private string nLTYPField;
        
        private string nLBERField;
        
        private string nLPLAField;
        
        private string lETYPField;
        
        private string sJAHRField;
        
        private string sMBLNField;
        
        private string sMBLPField;
        
        private decimal eRFMGField;
        
        private string lFBJAField;
        
        private string lGNUMField;
        
        private string uBNUMField;
        
        private string aUFNRField;
        
        private string sOBKZField;
        
        private string mAT_KDAUFField;
        
        private string mAT_KDPOSField;
        
        private string kDAUFField;
        
        private string kDPOSField;
        
        private string vBELN_IMField;
        
        private string vBELP_IMField;
        
        private string rSNUMField;
        
        private string rSPOSField;
        
        private string aBLADField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string MBLNR
        {
            get
            {
                return this.mBLNRField;
            }
            set
            {
                this.mBLNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string MJAHR
        {
            get
            {
                return this.mJAHRField;
            }
            set
            {
                this.mJAHRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string ZEILE
        {
            get
            {
                return this.zEILEField;
            }
            set
            {
                this.zEILEField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string LINE_ID
        {
            get
            {
                return this.lINE_IDField;
            }
            set
            {
                this.lINE_IDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string PARENT_ID
        {
            get
            {
                return this.pARENT_IDField;
            }
            set
            {
                this.pARENT_IDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string BWART
        {
            get
            {
                return this.bWARTField;
            }
            set
            {
                this.bWARTField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string XAUTO
        {
            get
            {
                return this.xAUTOField;
            }
            set
            {
                this.xAUTOField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string MATNR
        {
            get
            {
                return this.mATNRField;
            }
            set
            {
                this.mATNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public decimal MENGE
        {
            get
            {
                return this.mENGEField;
            }
            set
            {
                this.mENGEField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string MEINS
        {
            get
            {
                return this.mEINSField;
            }
            set
            {
                this.mEINSField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string HSDAT
        {
            get
            {
                return this.hSDATField;
            }
            set
            {
                this.hSDATField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string VFDAT
        {
            get
            {
                return this.vFDATField;
            }
            set
            {
                this.vFDATField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string SHKZG
        {
            get
            {
                return this.sHKZGField;
            }
            set
            {
                this.sHKZGField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public string WERKS
        {
            get
            {
                return this.wERKSField;
            }
            set
            {
                this.wERKSField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public string LGORT
        {
            get
            {
                return this.lGORTField;
            }
            set
            {
                this.lGORTField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public string CHARG
        {
            get
            {
                return this.cHARGField;
            }
            set
            {
                this.cHARGField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public string INSMK
        {
            get
            {
                return this.iNSMKField;
            }
            set
            {
                this.iNSMKField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=17)]
        public string UMMAT
        {
            get
            {
                return this.uMMATField;
            }
            set
            {
                this.uMMATField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=18)]
        public string UMWRK
        {
            get
            {
                return this.uMWRKField;
            }
            set
            {
                this.uMWRKField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=19)]
        public string UMLGO
        {
            get
            {
                return this.uMLGOField;
            }
            set
            {
                this.uMLGOField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=20)]
        public string UMCHA
        {
            get
            {
                return this.uMCHAField;
            }
            set
            {
                this.uMCHAField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=21)]
        public string LFBNR
        {
            get
            {
                return this.lFBNRField;
            }
            set
            {
                this.lFBNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=22)]
        public string LFPOS
        {
            get
            {
                return this.lFPOSField;
            }
            set
            {
                this.lFPOSField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=23)]
        public string EBELN
        {
            get
            {
                return this.eBELNField;
            }
            set
            {
                this.eBELNField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=24)]
        public string EBELP
        {
            get
            {
                return this.eBELPField;
            }
            set
            {
                this.eBELPField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=25)]
        public string KOSTL
        {
            get
            {
                return this.kOSTLField;
            }
            set
            {
                this.kOSTLField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=26)]
        public string SAKTO
        {
            get
            {
                return this.sAKTOField;
            }
            set
            {
                this.sAKTOField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=27)]
        public string WEMPF
        {
            get
            {
                return this.wEMPFField;
            }
            set
            {
                this.wEMPFField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=28)]
        public string PS_PSP_PNR
        {
            get
            {
                return this.pS_PSP_PNRField;
            }
            set
            {
                this.pS_PSP_PNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=29)]
        public string POSID
        {
            get
            {
                return this.pOSIDField;
            }
            set
            {
                this.pOSIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=30)]
        public string LIFNR
        {
            get
            {
                return this.lIFNRField;
            }
            set
            {
                this.lIFNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=31)]
        public string GRUND
        {
            get
            {
                return this.gRUNDField;
            }
            set
            {
                this.gRUNDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=32)]
        public string SGTXT
        {
            get
            {
                return this.sGTXTField;
            }
            set
            {
                this.sGTXTField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=33)]
        public string VLTYP
        {
            get
            {
                return this.vLTYPField;
            }
            set
            {
                this.vLTYPField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=34)]
        public string VLBER
        {
            get
            {
                return this.vLBERField;
            }
            set
            {
                this.vLBERField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=35)]
        public string VLPLA
        {
            get
            {
                return this.vLPLAField;
            }
            set
            {
                this.vLPLAField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=36)]
        public string NLTYP
        {
            get
            {
                return this.nLTYPField;
            }
            set
            {
                this.nLTYPField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=37)]
        public string NLBER
        {
            get
            {
                return this.nLBERField;
            }
            set
            {
                this.nLBERField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=38)]
        public string NLPLA
        {
            get
            {
                return this.nLPLAField;
            }
            set
            {
                this.nLPLAField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=39)]
        public string LETYP
        {
            get
            {
                return this.lETYPField;
            }
            set
            {
                this.lETYPField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=40)]
        public string SJAHR
        {
            get
            {
                return this.sJAHRField;
            }
            set
            {
                this.sJAHRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=41)]
        public string SMBLN
        {
            get
            {
                return this.sMBLNField;
            }
            set
            {
                this.sMBLNField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=42)]
        public string SMBLP
        {
            get
            {
                return this.sMBLPField;
            }
            set
            {
                this.sMBLPField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=43)]
        public decimal ERFMG
        {
            get
            {
                return this.eRFMGField;
            }
            set
            {
                this.eRFMGField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=44)]
        public string LFBJA
        {
            get
            {
                return this.lFBJAField;
            }
            set
            {
                this.lFBJAField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=45)]
        public string LGNUM
        {
            get
            {
                return this.lGNUMField;
            }
            set
            {
                this.lGNUMField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=46)]
        public string UBNUM
        {
            get
            {
                return this.uBNUMField;
            }
            set
            {
                this.uBNUMField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=47)]
        public string AUFNR
        {
            get
            {
                return this.aUFNRField;
            }
            set
            {
                this.aUFNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=48)]
        public string SOBKZ
        {
            get
            {
                return this.sOBKZField;
            }
            set
            {
                this.sOBKZField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=49)]
        public string MAT_KDAUF
        {
            get
            {
                return this.mAT_KDAUFField;
            }
            set
            {
                this.mAT_KDAUFField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=50)]
        public string MAT_KDPOS
        {
            get
            {
                return this.mAT_KDPOSField;
            }
            set
            {
                this.mAT_KDPOSField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=51)]
        public string KDAUF
        {
            get
            {
                return this.kDAUFField;
            }
            set
            {
                this.kDAUFField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=52)]
        public string KDPOS
        {
            get
            {
                return this.kDPOSField;
            }
            set
            {
                this.kDPOSField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=53)]
        public string VBELN_IM
        {
            get
            {
                return this.vBELN_IMField;
            }
            set
            {
                this.vBELN_IMField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=54)]
        public string VBELP_IM
        {
            get
            {
                return this.vBELP_IMField;
            }
            set
            {
                this.vBELP_IMField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=55)]
        public string RSNUM
        {
            get
            {
                return this.rSNUMField;
            }
            set
            {
                this.rSNUMField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=56)]
        public string RSPOS
        {
            get
            {
                return this.rSPOSField;
            }
            set
            {
                this.rSPOSField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=57)]
        public string ABLAD
        {
            get
            {
                return this.aBLADField;
            }
            set
            {
                this.aBLADField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sap-com:document:sap:rfc:functions")]
    public partial class ZRFC_WMS_MKPF
    {
        
        private string mBLNRField;
        
        private string mJAHRField;
        
        private string bKTXTField;
        
        private string bUDATField;
        
        private string xBLNRField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string MBLNR
        {
            get
            {
                return this.mBLNRField;
            }
            set
            {
                this.mBLNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string MJAHR
        {
            get
            {
                return this.mJAHRField;
            }
            set
            {
                this.mJAHRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string BKTXT
        {
            get
            {
                return this.bKTXTField;
            }
            set
            {
                this.bKTXTField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string BUDAT
        {
            get
            {
                return this.bUDATField;
            }
            set
            {
                this.bUDATField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string XBLNR
        {
            get
            {
                return this.xBLNRField;
            }
            set
            {
                this.xBLNRField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sap-com:document:sap:rfc:functions")]
    public partial class ZRFC_WMS_MESSAGE
    {
        
        private string mSGTYPField;
        
        private string mSGIDField;
        
        private string mSGNRField;
        
        private string mSGV1Field;
        
        private string mSGV2Field;
        
        private string mSGV3Field;
        
        private string mSGV4Field;
        
        private string oBJECTField;
        
        private string tEXTField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string MSGTYP
        {
            get
            {
                return this.mSGTYPField;
            }
            set
            {
                this.mSGTYPField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string MSGID
        {
            get
            {
                return this.mSGIDField;
            }
            set
            {
                this.mSGIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string MSGNR
        {
            get
            {
                return this.mSGNRField;
            }
            set
            {
                this.mSGNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string MSGV1
        {
            get
            {
                return this.mSGV1Field;
            }
            set
            {
                this.mSGV1Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string MSGV2
        {
            get
            {
                return this.mSGV2Field;
            }
            set
            {
                this.mSGV2Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string MSGV3
        {
            get
            {
                return this.mSGV3Field;
            }
            set
            {
                this.mSGV3Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string MSGV4
        {
            get
            {
                return this.mSGV4Field;
            }
            set
            {
                this.mSGV4Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string OBJECT
        {
            get
            {
                return this.oBJECTField;
            }
            set
            {
                this.oBJECTField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string TEXT
        {
            get
            {
                return this.tEXTField;
            }
            set
            {
                this.tEXTField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="urn:sap-com:document:sap:rfc:functions")]
    public partial class ZRFC_MM_WMS_STOCK_TRANSFERResponse
    {
        
        private ZRFC_WMS_MKPF eS_MKPFField;
        
        private ZRFC_WMS_MSEG[] eT_MSEGField;
        
        private ZRFC_WMS_MESSAGE[] eT_MSGField;
        
        private ZRFC_WMS_MSEG[] iT_MSEGField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public ZRFC_WMS_MKPF ES_MKPF
        {
            get
            {
                return this.eS_MKPFField;
            }
            set
            {
                this.eS_MKPFField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public ZRFC_WMS_MSEG[] ET_MSEG
        {
            get
            {
                return this.eT_MSEGField;
            }
            set
            {
                this.eT_MSEGField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public ZRFC_WMS_MESSAGE[] ET_MSG
        {
            get
            {
                return this.eT_MSGField;
            }
            set
            {
                this.eT_MSGField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public ZRFC_WMS_MSEG[] IT_MSEG
        {
            get
            {
                return this.iT_MSEGField;
            }
            set
            {
                this.iT_MSEGField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class ZRFC_MM_WMS_STOCK_TRANSFERRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sap-com:document:sap:rfc:functions", Order=0)]
        public MM_WMS_STOCK_TRANSFER.ZRFC_MM_WMS_STOCK_TRANSFER ZRFC_MM_WMS_STOCK_TRANSFER;
        
        public ZRFC_MM_WMS_STOCK_TRANSFERRequest()
        {
        }
        
        public ZRFC_MM_WMS_STOCK_TRANSFERRequest(MM_WMS_STOCK_TRANSFER.ZRFC_MM_WMS_STOCK_TRANSFER ZRFC_MM_WMS_STOCK_TRANSFER)
        {
            this.ZRFC_MM_WMS_STOCK_TRANSFER = ZRFC_MM_WMS_STOCK_TRANSFER;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class ZRFC_MM_WMS_STOCK_TRANSFERResponse1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sap-com:document:sap:rfc:functions", Order=0)]
        public MM_WMS_STOCK_TRANSFER.ZRFC_MM_WMS_STOCK_TRANSFERResponse ZRFC_MM_WMS_STOCK_TRANSFERResponse;
        
        public ZRFC_MM_WMS_STOCK_TRANSFERResponse1()
        {
        }
        
        public ZRFC_MM_WMS_STOCK_TRANSFERResponse1(MM_WMS_STOCK_TRANSFER.ZRFC_MM_WMS_STOCK_TRANSFERResponse ZRFC_MM_WMS_STOCK_TRANSFERResponse)
        {
            this.ZRFC_MM_WMS_STOCK_TRANSFERResponse = ZRFC_MM_WMS_STOCK_TRANSFERResponse;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public interface ZWS_MM_WMS_STOCK_TRANSFERChannel : MM_WMS_STOCK_TRANSFER.ZWS_MM_WMS_STOCK_TRANSFER, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public partial class ZWS_MM_WMS_STOCK_TRANSFERClient : System.ServiceModel.ClientBase<MM_WMS_STOCK_TRANSFER.ZWS_MM_WMS_STOCK_TRANSFER>, MM_WMS_STOCK_TRANSFER.ZWS_MM_WMS_STOCK_TRANSFER
    {
        
        public ZWS_MM_WMS_STOCK_TRANSFERClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<MM_WMS_STOCK_TRANSFER.ZRFC_MM_WMS_STOCK_TRANSFERResponse1> MM_WMS_STOCK_TRANSFER.ZWS_MM_WMS_STOCK_TRANSFER.ZRFC_MM_WMS_STOCK_TRANSFERAsync(MM_WMS_STOCK_TRANSFER.ZRFC_MM_WMS_STOCK_TRANSFERRequest request)
        {
            return base.Channel.ZRFC_MM_WMS_STOCK_TRANSFERAsync(request);
        }
        
        public System.Threading.Tasks.Task<MM_WMS_STOCK_TRANSFER.ZRFC_MM_WMS_STOCK_TRANSFERResponse1> ZRFC_MM_WMS_STOCK_TRANSFERAsync(MM_WMS_STOCK_TRANSFER.ZRFC_MM_WMS_STOCK_TRANSFER ZRFC_MM_WMS_STOCK_TRANSFER)
        {
            MM_WMS_STOCK_TRANSFER.ZRFC_MM_WMS_STOCK_TRANSFERRequest inValue = new MM_WMS_STOCK_TRANSFER.ZRFC_MM_WMS_STOCK_TRANSFERRequest();
            inValue.ZRFC_MM_WMS_STOCK_TRANSFER = ZRFC_MM_WMS_STOCK_TRANSFER;
            return ((MM_WMS_STOCK_TRANSFER.ZWS_MM_WMS_STOCK_TRANSFER)(this)).ZRFC_MM_WMS_STOCK_TRANSFERAsync(inValue);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
    }
}
