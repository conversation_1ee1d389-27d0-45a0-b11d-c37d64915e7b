//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//
//     对此文件的更改可能导致不正确的行为，并在以下条件下丢失:
//     代码重新生成。
// </auto-generated>
//------------------------------------------------------------------------------

namespace AsynPlnCK
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="urn:sap-com:document:sap:rfc:functions", ConfigurationName="AsynPlnCK.ZWS_PP_MES_ASYN_PLNCK")]
    public interface ZWS_PP_MES_ASYN_PLNCK
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:sap-com:document:sap:rfc:functions:ZWS_PP_MES_ASYN_PLNCK:ZRFC_PP_MES_ASYN_PLN" +
            "CKRequest", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<AsynPlnCK.ZRFC_PP_MES_ASYN_PLNCKResponse1> ZRFC_PP_MES_ASYN_PLNCKAsync(AsynPlnCK.ZRFC_PP_MES_ASYN_PLNCKRequest request);
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="urn:sap-com:document:sap:rfc:functions")]
    public partial class ZRFC_PP_MES_ASYN_PLNCK
    {
        
        private ZPP_MES_ST_CKORD[] iT_CKORDField;
        
        private ZPP_MES_ST_PLNORD[] iT_PLNORDField;
        
        private string mESNUMField;
        
        private string oRDTYPField;
        
        private string wERKSField;

        private string aUARTField;

        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public ZPP_MES_ST_CKORD[] IT_CKORD
        {
            get
            {
                return this.iT_CKORDField;
            }
            set
            {
                this.iT_CKORDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public ZPP_MES_ST_PLNORD[] IT_PLNORD
        {
            get
            {
                return this.iT_PLNORDField;
            }
            set
            {
                this.iT_PLNORDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string MESNUM
        {
            get
            {
                return this.mESNUMField;
            }
            set
            {
                this.mESNUMField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string ORDTYP
        {
            get
            {
                return this.oRDTYPField;
            }
            set
            {
                this.oRDTYPField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string WERKS
        {
            get
            {
                return this.wERKSField;
            }
            set
            {
                this.wERKSField = value;
            }
        }
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified, Order = 5)]
        public string AUART
        {
            get
            {
                return this.aUARTField;
            }
            set
            {
                this.aUARTField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sap-com:document:sap:rfc:functions")]
    public partial class ZPP_MES_ST_CKORD
    {
        
        private string pCTYPField;
        
        private string aUFNRField;
        
        private string aUARTField;
        
        private string mATNRField;
        
        private decimal pSMNGField;
        
        private string aMEINField;
        
        private string gSTRPField;
        
        private string gLTRPField;
        
        private string vERIDField;
        
        private string zAFNR_MES_CKField;
        
        private string pLNUMField;
        
        private string zAFNR_FILField;
        
        private string wEMPFField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string PCTYP
        {
            get
            {
                return this.pCTYPField;
            }
            set
            {
                this.pCTYPField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AUFNR
        {
            get
            {
                return this.aUFNRField;
            }
            set
            {
                this.aUFNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string AUART
        {
            get
            {
                return this.aUARTField;
            }
            set
            {
                this.aUARTField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string MATNR
        {
            get
            {
                return this.mATNRField;
            }
            set
            {
                this.mATNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public decimal PSMNG
        {
            get
            {
                return this.pSMNGField;
            }
            set
            {
                this.pSMNGField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string AMEIN
        {
            get
            {
                return this.aMEINField;
            }
            set
            {
                this.aMEINField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string GSTRP
        {
            get
            {
                return this.gSTRPField;
            }
            set
            {
                this.gSTRPField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string GLTRP
        {
            get
            {
                return this.gLTRPField;
            }
            set
            {
                this.gLTRPField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string VERID
        {
            get
            {
                return this.vERIDField;
            }
            set
            {
                this.vERIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string ZAFNR_MES_CK
        {
            get
            {
                return this.zAFNR_MES_CKField;
            }
            set
            {
                this.zAFNR_MES_CKField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string PLNUM
        {
            get
            {
                return this.pLNUMField;
            }
            set
            {
                this.pLNUMField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string ZAFNR_FIL
        {
            get
            {
                return this.zAFNR_FILField;
            }
            set
            {
                this.zAFNR_FILField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string WEMPF
        {
            get
            {
                return this.wEMPFField;
            }
            set
            {
                this.wEMPFField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sap-com:document:sap:rfc:functions")]
    public partial class BAPIRET1
    {
        
        private string tYPEField;
        
        private string idField;
        
        private string nUMBERField;
        
        private string mESSAGEField;
        
        private string lOG_NOField;
        
        private string lOG_MSG_NOField;
        
        private string mESSAGE_V1Field;
        
        private string mESSAGE_V2Field;
        
        private string mESSAGE_V3Field;
        
        private string mESSAGE_V4Field;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string TYPE
        {
            get
            {
                return this.tYPEField;
            }
            set
            {
                this.tYPEField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string ID
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string NUMBER
        {
            get
            {
                return this.nUMBERField;
            }
            set
            {
                this.nUMBERField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string MESSAGE
        {
            get
            {
                return this.mESSAGEField;
            }
            set
            {
                this.mESSAGEField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string LOG_NO
        {
            get
            {
                return this.lOG_NOField;
            }
            set
            {
                this.lOG_NOField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string LOG_MSG_NO
        {
            get
            {
                return this.lOG_MSG_NOField;
            }
            set
            {
                this.lOG_MSG_NOField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string MESSAGE_V1
        {
            get
            {
                return this.mESSAGE_V1Field;
            }
            set
            {
                this.mESSAGE_V1Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string MESSAGE_V2
        {
            get
            {
                return this.mESSAGE_V2Field;
            }
            set
            {
                this.mESSAGE_V2Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string MESSAGE_V3
        {
            get
            {
                return this.mESSAGE_V3Field;
            }
            set
            {
                this.mESSAGE_V3Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string MESSAGE_V4
        {
            get
            {
                return this.mESSAGE_V4Field;
            }
            set
            {
                this.mESSAGE_V4Field = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sap-com:document:sap:rfc:functions")]
    public partial class ZPP_MES_ST_PLNORD
    {
        
        private string pRCTYPField;
        
        private string pAARTField;
        
        private string mATNRField;
        
        private decimal gSMNGField;
        
        private string mEINSField;
        
        private string pSTTRField;
        
        private string pEDTRField;
        
        private string vERIDField;
        
        private string aUFNR_MES_CKField;
        
        private string aUFNR_FILField;
        
        private string wEMPF_FILField;
        
        private string pLNUM_CHGField;
        
        private string pLNUM_DELField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string PRCTYP
        {
            get
            {
                return this.pRCTYPField;
            }
            set
            {
                this.pRCTYPField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string PAART
        {
            get
            {
                return this.pAARTField;
            }
            set
            {
                this.pAARTField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string MATNR
        {
            get
            {
                return this.mATNRField;
            }
            set
            {
                this.mATNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public decimal GSMNG
        {
            get
            {
                return this.gSMNGField;
            }
            set
            {
                this.gSMNGField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string MEINS
        {
            get
            {
                return this.mEINSField;
            }
            set
            {
                this.mEINSField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string PSTTR
        {
            get
            {
                return this.pSTTRField;
            }
            set
            {
                this.pSTTRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string PEDTR
        {
            get
            {
                return this.pEDTRField;
            }
            set
            {
                this.pEDTRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string VERID
        {
            get
            {
                return this.vERIDField;
            }
            set
            {
                this.vERIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string AUFNR_MES_CK
        {
            get
            {
                return this.aUFNR_MES_CKField;
            }
            set
            {
                this.aUFNR_MES_CKField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string AUFNR_FIL
        {
            get
            {
                return this.aUFNR_FILField;
            }
            set
            {
                this.aUFNR_FILField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string WEMPF_FIL
        {
            get
            {
                return this.wEMPF_FILField;
            }
            set
            {
                this.wEMPF_FILField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string PLNUM_CHG
        {
            get
            {
                return this.pLNUM_CHGField;
            }
            set
            {
                this.pLNUM_CHGField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string PLNUM_DEL
        {
            get
            {
                return this.pLNUM_DELField;
            }
            set
            {
                this.pLNUM_DELField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="urn:sap-com:document:sap:rfc:functions")]
    public partial class ZRFC_PP_MES_ASYN_PLNCKResponse
    {
        
        private ZPP_MES_ST_CKORD[] iT_CKORDField;
        
        private ZPP_MES_ST_PLNORD[] iT_PLNORDField;
        
        private BAPIRET1 rETField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public ZPP_MES_ST_CKORD[] IT_CKORD
        {
            get
            {
                return this.iT_CKORDField;
            }
            set
            {
                this.iT_CKORDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public ZPP_MES_ST_PLNORD[] IT_PLNORD
        {
            get
            {
                return this.iT_PLNORDField;
            }
            set
            {
                this.iT_PLNORDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public BAPIRET1 RET
        {
            get
            {
                return this.rETField;
            }
            set
            {
                this.rETField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class ZRFC_PP_MES_ASYN_PLNCKRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sap-com:document:sap:rfc:functions", Order=0)]
        public AsynPlnCK.ZRFC_PP_MES_ASYN_PLNCK ZRFC_PP_MES_ASYN_PLNCK;
        
        public ZRFC_PP_MES_ASYN_PLNCKRequest()
        {
        }
        
        public ZRFC_PP_MES_ASYN_PLNCKRequest(AsynPlnCK.ZRFC_PP_MES_ASYN_PLNCK ZRFC_PP_MES_ASYN_PLNCK)
        {
            this.ZRFC_PP_MES_ASYN_PLNCK = ZRFC_PP_MES_ASYN_PLNCK;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class ZRFC_PP_MES_ASYN_PLNCKResponse1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sap-com:document:sap:rfc:functions", Order=0)]
        public AsynPlnCK.ZRFC_PP_MES_ASYN_PLNCKResponse ZRFC_PP_MES_ASYN_PLNCKResponse;
        
        public ZRFC_PP_MES_ASYN_PLNCKResponse1()
        {
        }
        
        public ZRFC_PP_MES_ASYN_PLNCKResponse1(AsynPlnCK.ZRFC_PP_MES_ASYN_PLNCKResponse ZRFC_PP_MES_ASYN_PLNCKResponse)
        {
            this.ZRFC_PP_MES_ASYN_PLNCKResponse = ZRFC_PP_MES_ASYN_PLNCKResponse;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    public interface ZWS_PP_MES_ASYN_PLNCKChannel : AsynPlnCK.ZWS_PP_MES_ASYN_PLNCK, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    public partial class ZWS_PP_MES_ASYN_PLNCKClient : System.ServiceModel.ClientBase<AsynPlnCK.ZWS_PP_MES_ASYN_PLNCK>, AsynPlnCK.ZWS_PP_MES_ASYN_PLNCK
    {
        
        public ZWS_PP_MES_ASYN_PLNCKClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<AsynPlnCK.ZRFC_PP_MES_ASYN_PLNCKResponse1> AsynPlnCK.ZWS_PP_MES_ASYN_PLNCK.ZRFC_PP_MES_ASYN_PLNCKAsync(AsynPlnCK.ZRFC_PP_MES_ASYN_PLNCKRequest request)
        {
            return base.Channel.ZRFC_PP_MES_ASYN_PLNCKAsync(request);
        }
        
        public System.Threading.Tasks.Task<AsynPlnCK.ZRFC_PP_MES_ASYN_PLNCKResponse1> ZRFC_PP_MES_ASYN_PLNCKAsync(AsynPlnCK.ZRFC_PP_MES_ASYN_PLNCK ZRFC_PP_MES_ASYN_PLNCK)
        {
            AsynPlnCK.ZRFC_PP_MES_ASYN_PLNCKRequest inValue = new AsynPlnCK.ZRFC_PP_MES_ASYN_PLNCKRequest();
            inValue.ZRFC_PP_MES_ASYN_PLNCK = ZRFC_PP_MES_ASYN_PLNCK;
            return ((AsynPlnCK.ZWS_PP_MES_ASYN_PLNCK)(this)).ZRFC_PP_MES_ASYN_PLNCKAsync(inValue);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
    }
}
