//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//
//     对此文件的更改可能导致不正确的行为，并在以下条件下丢失:
//     代码重新生成。
// </auto-generated>
//------------------------------------------------------------------------------

namespace ORDGR2
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="urn:sap-com:document:sap:rfc:functions", ConfigurationName="ORDGR2.ZWS_PP_MES_ORDGR2")]
    public interface ZWS_PP_MES_ORDGR2
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:sap-com:document:sap:rfc:functions:ZWS_PP_MES_ORDGR2:ZRFC_PP_MES_ORDGR2Reques" +
            "t", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ORDGR2.ZRFC_PP_MES_ORDGR2Response1> ZRFC_PP_MES_ORDGR2Async(ORDGR2.ZRFC_PP_MES_ORDGR2Request request);
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="urn:sap-com:document:sap:rfc:functions")]
    public partial class ZRFC_PP_MES_ORDGR2
    {
        
        private string bUDATField;
        
        private string bWARTField;
        
        private ZPP_MES_ORDGR2[] iT_GRField;
        
        private ZPP_MES_GIMTDOC2[] iT_RETField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string BUDAT
        {
            get
            {
                return this.bUDATField;
            }
            set
            {
                this.bUDATField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string BWART
        {
            get
            {
                return this.bWARTField;
            }
            set
            {
                this.bWARTField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public ZPP_MES_ORDGR2[] IT_GR
        {
            get
            {
                return this.iT_GRField;
            }
            set
            {
                this.iT_GRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public ZPP_MES_GIMTDOC2[] IT_RET
        {
            get
            {
                return this.iT_RETField;
            }
            set
            {
                this.iT_RETField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sap-com:document:sap:rfc:functions")]
    public partial class ZPP_MES_ORDGR2
    {
        
        private string aUFNRField;
        
        private string hSDATField;
        
        private string mATNRField;
        
        private string cHARGField;
        
        private string lGORTField;
        
        private decimal mENGEField;
        
        private string mEINSField;
        
        private string mJAHR_CField;
        
        private string mBLNR_CField;
        
        private string zEILE_CField;
        
        private string sRNUMField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AUFNR
        {
            get
            {
                return this.aUFNRField;
            }
            set
            {
                this.aUFNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string HSDAT
        {
            get
            {
                return this.hSDATField;
            }
            set
            {
                this.hSDATField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string MATNR
        {
            get
            {
                return this.mATNRField;
            }
            set
            {
                this.mATNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string CHARG
        {
            get
            {
                return this.cHARGField;
            }
            set
            {
                this.cHARGField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string LGORT
        {
            get
            {
                return this.lGORTField;
            }
            set
            {
                this.lGORTField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public decimal MENGE
        {
            get
            {
                return this.mENGEField;
            }
            set
            {
                this.mENGEField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string MEINS
        {
            get
            {
                return this.mEINSField;
            }
            set
            {
                this.mEINSField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string MJAHR_C
        {
            get
            {
                return this.mJAHR_CField;
            }
            set
            {
                this.mJAHR_CField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string MBLNR_C
        {
            get
            {
                return this.mBLNR_CField;
            }
            set
            {
                this.mBLNR_CField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string ZEILE_C
        {
            get
            {
                return this.zEILE_CField;
            }
            set
            {
                this.zEILE_CField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string SRNUM
        {
            get
            {
                return this.sRNUMField;
            }
            set
            {
                this.sRNUMField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sap-com:document:sap:rfc:functions")]
    public partial class ZPP_MES_GIMTDOC2
    {
        
        private string tYPEField;
        
        private string mJAHRField;
        
        private string mBLNRField;
        
        private string zEILEField;
        
        private string sRNUMField;
        
        private string aUFNRField;
        
        private string mJAHR_CField;
        
        private string mBLNR_CField;
        
        private string zEILE_CField;
        
        private string mSGField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string TYPE
        {
            get
            {
                return this.tYPEField;
            }
            set
            {
                this.tYPEField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string MJAHR
        {
            get
            {
                return this.mJAHRField;
            }
            set
            {
                this.mJAHRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string MBLNR
        {
            get
            {
                return this.mBLNRField;
            }
            set
            {
                this.mBLNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string ZEILE
        {
            get
            {
                return this.zEILEField;
            }
            set
            {
                this.zEILEField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string SRNUM
        {
            get
            {
                return this.sRNUMField;
            }
            set
            {
                this.sRNUMField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string AUFNR
        {
            get
            {
                return this.aUFNRField;
            }
            set
            {
                this.aUFNRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string MJAHR_C
        {
            get
            {
                return this.mJAHR_CField;
            }
            set
            {
                this.mJAHR_CField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string MBLNR_C
        {
            get
            {
                return this.mBLNR_CField;
            }
            set
            {
                this.mBLNR_CField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string ZEILE_C
        {
            get
            {
                return this.zEILE_CField;
            }
            set
            {
                this.zEILE_CField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string MSG
        {
            get
            {
                return this.mSGField;
            }
            set
            {
                this.mSGField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="urn:sap-com:document:sap:rfc:functions")]
    public partial class ZRFC_PP_MES_ORDGR2Response
    {
        
        private ZPP_MES_ORDGR2[] iT_GRField;
        
        private ZPP_MES_GIMTDOC2[] iT_RETField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public ZPP_MES_ORDGR2[] IT_GR
        {
            get
            {
                return this.iT_GRField;
            }
            set
            {
                this.iT_GRField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        [System.Xml.Serialization.XmlArrayItemAttribute("item", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public ZPP_MES_GIMTDOC2[] IT_RET
        {
            get
            {
                return this.iT_RETField;
            }
            set
            {
                this.iT_RETField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class ZRFC_PP_MES_ORDGR2Request
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sap-com:document:sap:rfc:functions", Order=0)]
        public ORDGR2.ZRFC_PP_MES_ORDGR2 ZRFC_PP_MES_ORDGR2;
        
        public ZRFC_PP_MES_ORDGR2Request()
        {
        }
        
        public ZRFC_PP_MES_ORDGR2Request(ORDGR2.ZRFC_PP_MES_ORDGR2 ZRFC_PP_MES_ORDGR2)
        {
            this.ZRFC_PP_MES_ORDGR2 = ZRFC_PP_MES_ORDGR2;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class ZRFC_PP_MES_ORDGR2Response1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sap-com:document:sap:rfc:functions", Order=0)]
        public ORDGR2.ZRFC_PP_MES_ORDGR2Response ZRFC_PP_MES_ORDGR2Response;
        
        public ZRFC_PP_MES_ORDGR2Response1()
        {
        }
        
        public ZRFC_PP_MES_ORDGR2Response1(ORDGR2.ZRFC_PP_MES_ORDGR2Response ZRFC_PP_MES_ORDGR2Response)
        {
            this.ZRFC_PP_MES_ORDGR2Response = ZRFC_PP_MES_ORDGR2Response;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public interface ZWS_PP_MES_ORDGR2Channel : ORDGR2.ZWS_PP_MES_ORDGR2, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public partial class ZWS_PP_MES_ORDGR2Client : System.ServiceModel.ClientBase<ORDGR2.ZWS_PP_MES_ORDGR2>, ORDGR2.ZWS_PP_MES_ORDGR2
    {
        
        public ZWS_PP_MES_ORDGR2Client(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<ORDGR2.ZRFC_PP_MES_ORDGR2Response1> ORDGR2.ZWS_PP_MES_ORDGR2.ZRFC_PP_MES_ORDGR2Async(ORDGR2.ZRFC_PP_MES_ORDGR2Request request)
        {
            return base.Channel.ZRFC_PP_MES_ORDGR2Async(request);
        }
        
        public System.Threading.Tasks.Task<ORDGR2.ZRFC_PP_MES_ORDGR2Response1> ZRFC_PP_MES_ORDGR2Async(ORDGR2.ZRFC_PP_MES_ORDGR2 ZRFC_PP_MES_ORDGR2)
        {
            ORDGR2.ZRFC_PP_MES_ORDGR2Request inValue = new ORDGR2.ZRFC_PP_MES_ORDGR2Request();
            inValue.ZRFC_PP_MES_ORDGR2 = ZRFC_PP_MES_ORDGR2;
            return ((ORDGR2.ZWS_PP_MES_ORDGR2)(this)).ZRFC_PP_MES_ORDGR2Async(inValue);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
    }
}
