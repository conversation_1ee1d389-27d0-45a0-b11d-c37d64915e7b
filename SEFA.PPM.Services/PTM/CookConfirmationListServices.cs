using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.IServices.PTM;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels.PTM;
using SEFA.PTM.Model.Models;
using SEFA.PPM.Model.Models;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.IRepository.UnitOfWork;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Linq;

namespace SEFA.PPM.Services.PTM
{
	public class CookConfirmationListServices : BaseServices<CookConfirmationListEntity>, ICookConfirmationListServices
	{
		private readonly IBaseRepository<CookConfirmationListEntity> _dal;
		private readonly IBaseRepository<CookConfirmationEntity> _dal2;
		private readonly IUnitOfWork _unitOfWork;
		private readonly IUser _user;


		public CookConfirmationListServices(IBaseRepository<CookConfirmationListEntity> dal, IBaseRepository<CookConfirmationEntity> dal2, IUser user, IUnitOfWork unitOfWork)
		{
			_dal = dal;
			BaseDal = dal;
			_dal2 = dal2;
			_user = user;
			_unitOfWork = unitOfWork;
		}

		public async Task<List<CookConfirmationListEntity>> GetList(CookConfirmationListRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<CookConfirmationListEntity>()
			.AndIF(!string.IsNullOrEmpty(reqModel.Formula), a => a.Formula != null && a.Formula.Contains(reqModel.Formula))
			.AndIF(!string.IsNullOrEmpty(reqModel.MaterialCode), a => a.MaterialCode != null && a.MaterialCode.Contains(reqModel.MaterialCode))
			.AndIF(!string.IsNullOrEmpty(reqModel.MaterialDescription), a => a.MaterialDescription != null && a.MaterialDescription.Contains(reqModel.MaterialDescription))
			.AndIF(!string.IsNullOrEmpty(reqModel.MaterialVersionNumber), a => a.MaterialVersionNumber == reqModel.MaterialVersionNumber)
			.AndIF(!string.IsNullOrEmpty(reqModel.ProductionOrderNo), a => a.ProductionOrderNo != null && a.ProductionOrderNo.Contains(reqModel.ProductionOrderNo))
			.AndIF(!string.IsNullOrEmpty(reqModel.Resource), a => a.Resource != null && a.Resource.Contains(reqModel.Resource))
			.AndIF(!string.IsNullOrEmpty(reqModel.BomVersion), a => a.BomVersion == reqModel.BomVersion)
			.AndIF(!string.IsNullOrEmpty(reqModel.LineCode), a => a.LineCode != null && a.LineCode.Contains(reqModel.LineCode))
			.AndIF(reqModel.StartTime.HasValue, a => a.CreateDate >= reqModel.StartTime)
			.AndIF(reqModel.EndTime.HasValue, a => a.CreateDate <= reqModel.EndTime)
			.AndIF(!string.IsNullOrEmpty(reqModel.Key),
				a => a.MaterialCode.Contains(reqModel.Key) ||
					 a.MaterialDescription.Contains(reqModel.Key) ||
					 a.MaterialVersionNumber.Contains(reqModel.Key) ||
					 a.ProductionOrderNo.Contains(reqModel.Key) ||
					 a.BomVersion.Contains(reqModel.Key) ||
					 a.LineCode.Contains(reqModel.Key) ||
					 a.Formula.Contains(reqModel.Key)
				).ToExpression();
			var query = _dal.Db.Queryable<CookConfirmationListEntity>()
					.Where(whereExpression)
					.OrderBy(x => x.LineCode)
					.OrderByDescending(x => x.PlanStartTime)
					.OrderBy(x => x.Sequence);
			return await query.ToListAsync();
		}

		public async Task<PageModel<CookConfirmationListEntity>> GetPageList(CookConfirmationListRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<CookConfirmationListEntity>()
			.AndIF(!string.IsNullOrEmpty(reqModel.Formula), a => a.Formula != null && a.Formula.Contains(reqModel.Formula))
			.AndIF(!string.IsNullOrEmpty(reqModel.MaterialCode), a => a.MaterialCode != null && a.MaterialCode.Contains(reqModel.MaterialCode))
			.AndIF(!string.IsNullOrEmpty(reqModel.MaterialDescription), a => a.MaterialDescription != null && a.MaterialDescription.Contains(reqModel.MaterialDescription))
			.AndIF(!string.IsNullOrEmpty(reqModel.MaterialVersionNumber), a => a.MaterialVersionNumber == reqModel.MaterialVersionNumber)
			.AndIF(!string.IsNullOrEmpty(reqModel.ProductionOrderNo), a => a.ProductionOrderNo != null && a.ProductionOrderNo.Contains(reqModel.ProductionOrderNo))
			.AndIF(!string.IsNullOrEmpty(reqModel.Resource), a => a.Resource != null && a.Resource.Contains(reqModel.Resource))
			.AndIF(!string.IsNullOrEmpty(reqModel.BomVersion), a => a.BomVersion == reqModel.BomVersion)
			.AndIF(!string.IsNullOrEmpty(reqModel.LineCode), a => a.LineCode != null && a.LineCode.Contains(reqModel.LineCode))
			.AndIF(reqModel.StartTime.HasValue, a => a.CreateDate >= reqModel.StartTime)
			.AndIF(reqModel.EndTime.HasValue, a => a.CreateDate <= reqModel.EndTime)
			.AndIF(!string.IsNullOrEmpty(reqModel.Key),
				a => a.MaterialCode.Contains(reqModel.Key) ||
					 a.MaterialDescription.Contains(reqModel.Key) ||
					 a.MaterialVersionNumber.Contains(reqModel.Key) ||
					 a.ProductionOrderNo.Contains(reqModel.Key) ||
					 a.BomVersion.Contains(reqModel.Key) ||
					 a.LineCode.Contains(reqModel.Key) ||
					 a.Formula.Contains(reqModel.Key)

				).ToExpression();
			PageModel<CookConfirmationListEntity> result = new PageModel<CookConfirmationListEntity>()
			{
				page = reqModel.pageIndex,
				pageSize = reqModel.pageSize
			};
			int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引
			var query = _dal.Db.Queryable<CookConfirmationListEntity>()
				.Where(whereExpression)
				.OrderBy(x => x.LineCode)
				.OrderByDescending(x => x.PlanStartTime)
				.OrderBy(x => x.Sequence);
			var data = await query.ToListAsync();
			var rDat = data.Skip(startIndex).Take(reqModel.pageSize).ToList();
			result.dataCount = data.Count;
			result.data = rDat;
			return result;
		}

		public async Task<MessageModel<string>> Update(CookConfirmationEntity entity)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			var cookConfirmation = await _dal2.FindEntity(entity.ID);
			if (cookConfirmation == null)
			{
				result.msg = "未找到ID";
				return result;
			}
			cookConfirmation.StartTime = entity.StartTime;
			cookConfirmation.EndTime = entity.EndTime;
			cookConfirmation.Duration = entity.Duration;
			TimeSpan timeDifference = cookConfirmation.EndTime - cookConfirmation.StartTime;// 计算时间差
			var runTime = (decimal)timeDifference.TotalSeconds;// 获取总秒数
			cookConfirmation.RuntimeHours = Math.Round((runTime - cookConfirmation.Duration) / 3600, 3);
			//cookConfirmation.RuntimeHours = entity.RuntimeHours;
			cookConfirmation.Reason = entity.Reason;
			cookConfirmation.Modify(cookConfirmation.ID, _user.Name);
			_unitOfWork.BeginTran();
			try
			{
				await _dal2.Update(cookConfirmation);
			}
			catch (System.Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.msg = ex.Message;
				return result;
			}
			_unitOfWork.CommitTran();
			result.success = true;
			result.msg = "操作成功！";
			return result;
		}

		public async Task<bool> SaveForm(CookConfirmationListEntity entity)
		{
			if (string.IsNullOrEmpty(entity.ID))
			{
				return await Add(entity) > 0;
			}
			else
			{
				return await Update(entity);
			}
		}
	}
}