using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.IServices.PTM;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels.PTM;
using SEFA.PPM.Model.Models;
using SEFA.PTM.Model.ViewModels;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.DFM.Model.Models;
using System.Linq;
using System.Numerics;
using LKK.Lib.Core;
using Microsoft.AspNetCore.DataProtection.KeyManagement;
using System.Drawing.Drawing2D;
using Abp.Extensions;
using Abp.Domain.Entities;
using SEFA.DFM.Model.ViewModels;
using System;
using SEFA.Base.Common.Helper;
using SharpCompress.Common;
using Castle.Core.Internal;

namespace SEFA.PPM.Services.PTM
{
	public class ProcessDataViewServices : BaseServices<ProcessDataViewEntity>, IProcessDataViewServices
	{
		private readonly IBaseRepository<ProcessDataViewEntity> _dal;
		private readonly IBaseRepository<ProductionOrderEntity> _dal2;
		private readonly IUnitOfWork _unitOfWork;
		private readonly IUser _user;


		public ProcessDataViewServices(IBaseRepository<ProcessDataViewEntity> dal, IBaseRepository<ProductionOrderEntity> dal2, IUnitOfWork unitOfWork, IUser user)
		{
			_dal = dal;
			BaseDal = dal;
			_dal2 = dal2;
			_unitOfWork = unitOfWork;
			_user = user;
		}

		public async Task<List<ProcessDataViewEntity>> GetList(ProcessDataViewRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<ProcessDataViewEntity>()
				.AndIF(!string.IsNullOrEmpty(reqModel.Key),
				a =>
				a.ProcessData.Contains(reqModel.Key) ||
				a.HashData.Contains(reqModel.Key) ||
				a.TextVersion.Contains(reqModel.Key)
				).AndIF(reqModel.Type != -1,
				a => a.Type == reqModel.Type
				).
				AndIF(!string.IsNullOrEmpty(reqModel.VersionId),
				a => a.VersionId == reqModel.VersionId
				)
				.ToExpression();
			var data = await _dal.Db.Queryable<ProcessDataViewEntity>()
				.Where(whereExpression).OrderBy(x => Convert.ToInt32(x.TextVersion)).ToListAsync();
			data.ForEach(x => x.ProcessData = Enigma.Decrypt(x.ProcessData, x.Token)?.Replace("@@", "\n"));
			return data;
		}

		public async Task<PageModel<ProcessDataViewEntity>> GetPageList(ProcessDataViewRequestModel reqModel)
		{
			PageModel<ProcessDataViewEntity> result = new PageModel<ProcessDataViewEntity>();
			RefAsync<int> dataCount = 0;
			var whereExpression = Expressionable.Create<ProcessDataViewEntity>()
				.AndIF(!string.IsNullOrEmpty(reqModel.Key),
				a =>
				a.ProcessData.Contains(reqModel.Key) ||
				a.HashData.Contains(reqModel.Key) ||
				a.TextVersion.Contains(reqModel.Key)
				).AndIF(reqModel.Type != -1,
				a => a.Type == reqModel.Type
				).
				AndIF(!string.IsNullOrEmpty(reqModel.VersionId),
				a => a.VersionId == reqModel.VersionId
				)
				.ToExpression();
			var data = await _dal.Db.Queryable<ProcessDataViewEntity>()
				.Where(whereExpression)
				.OrderBy(x => Convert.ToInt32(x.TextVersion))
				.ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
			data.ForEach(x => x.ProcessData = Enigma.Decrypt(x.ProcessData, x.Token)?.Replace("@@", "\n"));
			result.dataCount = dataCount;
			result.data = data;
			return result;
		}

		/// <summary>
		/// 根据materialVersionId和状态获取最新预处理长文本
		/// </summary>
		/// <param name="materialVersionId"></param>
		/// <param name="status"></param>
		/// <returns></returns>
		public async Task<MessageModel<MaterialProcessDataEntity>> GetLastNewProcessData(string materialVersionId, string status)
		{
			var result = new MessageModel<MaterialProcessDataEntity>
			{
				success = true
			};
			//获取预处理长文本列表
			MessageModel<List<MaterialProcessDataEntity>> apiResult_processData = await HttpHelper.PostAsync<List<MaterialProcessDataEntity>>("DFM", "api/MaterialProcessData/GetList", _user.GetToken(), new { });
			var processDatas = apiResult_processData.response;
			//var processDatas = await _dal.FindList(x => x.VersionId == materialVersionId);
			if (processDatas == null || processDatas.Count == 0)
			{
				result.msg = "processDatas为空";
				return result;
			}
			var lastProcessData = processDatas.FindAll(x => x.VersionId == materialVersionId && x.Type == 1 && (status.IsNullOrEmpty() || x.Status == status)).OrderByDescending(x => Convert.ToInt32(x.TextVersion)).FirstOrDefault();
			if (lastProcessData == null)
			{
				result.response = new MaterialProcessDataEntity();
				result.msg = "lastProcessData为空";
				return result;
			}
			lastProcessData.ProcessData = Enigma.Decrypt(lastProcessData.ProcessData, lastProcessData.Token)?.Replace("@@", "\n");
			result.response = lastProcessData;
			result.success = true;
			result.msg = "获取成功";
			return result;
		}

		/// <summary>
		/// 根据materialVersionId和状态获取最新预处理长文本
		/// </summary>
		/// <param name="materialVersionId"></param>
		/// <param name="status"></param>
		/// <returns></returns>
		public async Task<MessageModel<MaterialProcessDataEntity>> GetLastProcessData(string materialVersionId, string status)
		{
			var result = new MessageModel<MaterialProcessDataEntity>
			{
				success = false
			};
			//获取预处理长文本列表
			MessageModel<List<MaterialProcessDataEntity>> apiResult_processData = await HttpHelper.PostAsync<List<MaterialProcessDataEntity>>("DFM", "api/MaterialProcessData/GetList", _user.GetToken(), new { });
			var processDatas = apiResult_processData.response;
			//var processDatas = await _dal.FindList(x => x.VersionId == materialVersionId);
			if (processDatas == null || processDatas.Count == 0)
			{
				result.msg = "processDatas为空";
				return result;
			}
			var lastProcessData = processDatas.FindAll(x => x.VersionId == materialVersionId && x.Type == 1 && (status.IsNullOrEmpty() || x.Status == status)).OrderByDescending(x => Convert.ToInt32(x.TextVersion)).FirstOrDefault();
			if (lastProcessData == null)
			{
				result.msg = "lastProcessData为空";
				return result;
			}
			lastProcessData.ProcessData = Enigma.Decrypt(lastProcessData.ProcessData, lastProcessData.Token)?.Replace("@@", "\n");
			result.response = lastProcessData;
			result.success = true;
			result.msg = "获取成功";
			return result;
		}


		/// <summary>
		/// 保存预处理长文本
		/// </summary>
		/// <param name="materialProcessDataEntity"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> SavePreprocessingData(MaterialProcessDataEntity materialProcessDataEntity)
		{
			var result = new MessageModel<string>
			{
				msg = "保存失败！",
				success = false
			};
			MaterialProcessDataEntity processData = null;
			if (!string.IsNullOrEmpty(materialProcessDataEntity.ID))
			{
				//获取工艺长文本
				MessageModel<MaterialProcessDataEntity> apiResult_processDatabyId = await HttpHelper.GetApiAsync<MaterialProcessDataEntity>("DFM", "api/MaterialProcessData/GetEntity/" + materialProcessDataEntity.ID, _user.GetToken(), null);
				processData = apiResult_processDatabyId.response;
				if (processData == null)
				{
					result.msg = "ID未找到";
					return result;
				}
				processData.Status = "1";
				processData.Modify(processData.ID, _user.Name);
			}
			var token = Guid.NewGuid().ToString();
			var maxVersion = (await GetLastProcessData(materialProcessDataEntity.VersionId, null))?.response?.TextVersion;
			//int.TryParse(maxVersion_str, out int maxVersion);
			maxVersion++;
			MaterialProcessDataEntity newProcessData = new()
			{
				VersionId = materialProcessDataEntity.VersionId,
				Token = token,
				ProcessData = Enigma.Encrypt(materialProcessDataEntity.ProcessData?.Replace( "\n", "@@"), token),
				HashData = SHAHelper.Sha256(materialProcessDataEntity.ProcessData),
				Status = "3",
				TextVersion = maxVersion.Value,
				IsReminded = "0",
				Type = 1
			};
			//调用API接口保存数据
			try
			{
				if (processData != null)
				{
					MessageModel<string> apiResult_update = await HttpHelper.PostAsync<string>("DFM", "api/MaterialProcessData/SaveForm", _user.GetToken(), processData);
				}
				MessageModel<string> apiResult_save = await HttpHelper.PostAsync<string>("DFM", "api/MaterialProcessData/SaveForm", _user.GetToken(), newProcessData);
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				return result;
			}
			result.msg = "保存成功！";
			result.success = true;
			return result;
		}

		/// <summary>
		/// 更新预处理长文本
		/// </summary>
		/// <param name="materialProcessDataEntity"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> EditPreprocessingData(MaterialProcessDataEntity materialProcessDataEntity)
		{
			var result = new MessageModel<string>
			{
				msg = "更新失败！",
				success = false
			};
			//获取工艺长文本
			MessageModel<MaterialProcessDataEntity> apiResult_processDatabyId = await HttpHelper.GetApiAsync<MaterialProcessDataEntity>("DFM", "api/MaterialProcessData/GetEntity/" + materialProcessDataEntity.ID, _user.GetToken(), null);
			var processData = apiResult_processDatabyId.response;
			if (processData == null)
			{
				result.msg = "ID未找到";
				return result;
			}
			processData.ProcessData = Enigma.Encrypt(materialProcessDataEntity.ProcessData?.Replace( "\n", "@@"), processData.Token);
			processData.HashData = SHAHelper.Sha256(materialProcessDataEntity.ProcessData);
			processData.Modify(processData.ID, _user.Name);
			//调用API接口更新数据
			try
			{
				MessageModel<string> apiResult_update = await HttpHelper.PostAsync<string>("DFM", "api/MaterialProcessData/SaveForm", _user.GetToken(), processData);
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				return result;
			}
			result.msg = "更新成功！";
			result.success = true;
			return result;
		}

		/// <summary>
		/// 根据ContextVersion获取工艺长文本
		/// </summary>
		/// <param name="contextVersionId"></param>
		/// <returns></returns>
		public async Task<MessageModel<MaterialProcessDataEntity>> GetLastProcessDataByContextVersion(string contextVersionId)
		{
			var result = new MessageModel<MaterialProcessDataEntity>
			{
				msg = "获取失败！",
				success = true
			};
			//获取PROCESS_DATA_MAPPING
			//MessageModel<List<ProcessDataMappingEntity>> apiResult_processDataMapping = await HttpHelper.PostAsync<List<ProcessDataMappingEntity>>("DFM", "api/ProcessDataMapping/GetList", _user.GetToken(), new { });

   //         var processDataMappings = apiResult_processDataMapping.response;
			//if (processDataMappings == null || processDataMappings.Count == 0)
			//{
			//	result.msg = "processDataMappings为空";
			//	return result;
			//}
			////获取工艺长文本列表
			//MessageModel<List<MaterialProcessDataEntity>> apiResult_processData = await HttpHelper.PostAsync<List<MaterialProcessDataEntity>>("DFM", "api/MaterialProcessData/GetList", _user.GetToken(), new { });
			//var processDatas = apiResult_processData.response;
			//if (processDatas == null || processDatas.Count == 0)
			//{
			//	result.msg = "processDatas为空";
			//	return result;
			//}
            
            var mappings = _dal2.Db.Queryable<ProcessDataMappingEntity>().Where(a => a.VersionId == contextVersionId).ToList();

            //var mappings = processDataMappings.FindAll(x => x.VersionId == contextVersionId);
			if (mappings == null || mappings.Count == 0)
			{
				result.msg = "mappings为空";
				return result;
			}
			var ids = mappings.Select(x => x.ProcessData);

            var lastProcessDataList = _dal2.Db.Queryable<MaterialProcessDataEntity>().Where(x => x.Status == "2" && x.Type == 0 && ids.Contains(x.ID)).ToList();
			var lastProcessData = lastProcessDataList.OrderByDescending(x => x.TextVersion)?.FirstOrDefault();
            //var lastProcessData = processDatas.FindAll(x => x.Status == "2" && x.Type == 0 && ids.Contains(x.ID))?.OrderByDescending(x => x.TextVersion)?.FirstOrDefault();
            if (lastProcessData == null)
			{
				result.msg = "lastProcessData为空";
				return result;
			}
			try
			{
				lastProcessData.ProcessData = Enigma.Decrypt(lastProcessData.ProcessData, lastProcessData.Token)?.Replace("@@", "\n");
			}
			catch (Exception ex)
			{

			}
			result.response = lastProcessData;
			result.success = true;
			result.msg = "获取成功！";
			return result;
		}

		/// <summary>
		/// 根据productionOrderId获取工艺长文本
		/// </summary>
		/// <param name="productionOrderId"></param>
		/// <returns></returns>
		public async Task<MessageModel<MaterialProcessDataEntity>> GetLastProcessData(string productionOrderId)
		{
			var result = new MessageModel<MaterialProcessDataEntity>
			{
				msg = "获取失败！",
				success = false
			};
			var result1 = await GetContextVersionId(productionOrderId);
			if (string.IsNullOrEmpty(result1.response))
			{
				return result;
			}
			return await GetLastProcessDataByContextVersion(result1.response);
		}

		/// <summary>
		/// 根据productionOrderId获取ContextVersionId
		/// </summary>
		/// <param name="productionOrderId"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> GetContextVersionId(string productionOrderId)
		{
			var result = new MessageModel<string>
			{
				success = false
			};
			ProductionOrderEntity entity = await _dal2.FindEntity(productionOrderId);
			if (entity == null)
			{
				result.msg = "productionOrderId不存在";
				return result;
			}
			//result.response = "02408281-5282-7946-163e-0370f6000002";
			//获取参数
			MessageModel<List<RecipeParameterModel>> apiResult_recipeParameter = await HttpHelper.PostAsync<List<RecipeParameterModel>>("DFM", "api/RecipeCommon/GetPoRecipeParameterList", _user.GetToken(), new { ProductionOrderId = entity.ID, MaterialVersionId = entity.MaterialVersionId, ProductionTime = entity.PlanStartTime });
			var recipeParameters = apiResult_recipeParameter.response;
			if (recipeParameters == null || recipeParameters.Count == 0)
			{
				result.msg = "recipeParameters为空";
				return result;
			}
			var contextVersionId = recipeParameters.FirstOrDefault()?.RecipeContextVersionId;
			if (contextVersionId == null)
			{
				result.msg = "contextVersionId为空";
				return result;
			}
			result.response = contextVersionId;
			result.success = true;
			return result;
		}

		/// <summary>
		/// 根据productionOrderId获取工艺长文本
		/// </summary>
		/// <param name="productionOrderId"></param>
		/// <returns></returns>
		public async Task<MessageModel<MaterialProcessDataEntity>> GetLastProcessDataByPoId(string productionOrderId)
		{
			var result = new MessageModel<MaterialProcessDataEntity>
			{
				msg = "获取失败！",
				success = false
			};
			ProductionOrderEntity entity = await _dal2.FindEntity(productionOrderId);
			if (entity == null)
			{
				result.msg = "productionOrderId不存在";
				return result;
			}
			//获取工艺长文本列表
			MessageModel<List<MaterialProcessDataEntity>> apiResult_processData = await HttpHelper.PostAsync<List<MaterialProcessDataEntity>>("DFM", "api/MaterialProcessData/GetList", _user.GetToken(), new { Type = 0, Status = 2, OrderId = productionOrderId });
			var processData = apiResult_processData.response?.FirstOrDefault();
			if (processData == null )
			{
				result.msg = "processData为空";
				return result;
			}
			result.msg = "获取成功！";
			processData.ProcessData = Enigma.Decrypt(processData.ProcessData, processData.Token)?.Replace("@@", "\n");
			result.response = processData;
			result.success = true;
			return result;
		}

		/// <summary>
		/// 保存工艺长文本
		/// </summary>
		/// <param name="materialProcessDataEntity"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> SaveMaterialProcessData(MaterialProcessDataEntity materialProcessDataEntity)
		{
			var result = new MessageModel<string>
			{
				msg = "保存失败！",
				success = false
			};
			//获取工艺长文本
			MessageModel<MaterialProcessDataEntity> apiResult_processDatabyId = await HttpHelper.GetApiAsync<MaterialProcessDataEntity>("DFM", "api/MaterialProcessData/GetEntity/" + materialProcessDataEntity.ID, _user.GetToken(), null);
			if (apiResult_processDatabyId.response == null)
			{
				result.msg = "ID未找到";
				return result;
			}
			var processData = apiResult_processDatabyId.response;
			//加密
			processData.ProcessData = Enigma.Encrypt(materialProcessDataEntity.ProcessData?.Replace("\n", "@@"), processData.Token);
			//转换为HashCode
			processData.HashData = SHAHelper.Sha256(materialProcessDataEntity.ProcessData);
			processData.Status = "2";
			//确认时要更新确认时间和确认人
			processData.Reviewtime = DateTime.Now;
			processData.Reviewuserid = _user.Name.ToString();
			processData.Modify(processData.ID, _user.Name);
			//调用API接口更新数据
			try
			{
				MessageModel<string> apiResult_updatepde = await HttpHelper.PostAsync<string>("DFM", "api/MaterialProcessData/SaveForm", _user.GetToken(), processData);
				//return apiResult_updatepde;
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				return result;
			}
			result.msg = "保存成功！";
			result.success = true;
			return result;
		}

		/// <summary>
		/// 更新工艺长文本状态
		/// </summary>
		/// <param name="materialProcessDataEntity"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> UpdateMaterialProcessDataStatus(MaterialProcessDataEntity materialProcessDataEntity)
		{
			var result = new MessageModel<string>
			{
				msg = "状态更新失败！",
				success = false
			};
			//获取工艺长文本
			MessageModel<MaterialProcessDataEntity> apiResult_processDatabyId = await HttpHelper.GetApiAsync<MaterialProcessDataEntity>("DFM", "api/MaterialProcessData/GetEntity/" + materialProcessDataEntity.ID, _user.GetToken(), null);
			if (apiResult_processDatabyId.response == null)
			{
				result.msg = "ID未找到";
				return result;
			}
			var processData = apiResult_processDatabyId.response;
			if (!string.IsNullOrEmpty(processData.Status))
			{
				processData.Status = materialProcessDataEntity.Status;
			}
			if (!string.IsNullOrEmpty(processData.IsReminded))
			{
				processData.IsReminded = materialProcessDataEntity.IsReminded;
			}
			//确认时要更新确认时间和确认人
			processData.Reviewtime = DateTime.Now;
			processData.Reviewuserid = _user.Name;
			processData.Modify(processData.ID, _user.Name);
			//调用API接口更新数据
			try
			{
				MessageModel<string> apiResult_updatepde = await HttpHelper.PostAsync<string>("DFM", "api/MaterialProcessData/SaveForm", _user.GetToken(), processData);
				return apiResult_updatepde;
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				return result;
			}
			result.msg = "状态更新成功！";
			result.success = true;
			return result;
		}
	}
}