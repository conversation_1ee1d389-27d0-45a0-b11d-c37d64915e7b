using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.IServices.PTM;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels.PTM;
using SEFA.PPM.Model.Models;
using System.Runtime.Versioning;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.DFM.Model.Models;
using System;
using LKK.Lib.Core;
using SEFA.Base.Common.Helper;

namespace SEFA.PPM.Services.PTM
{
	public class SegmentMaterialServices : BaseServices<SegmentMaterialEntity>, ISegmentMaterialServices
	{
		private readonly IBaseRepository<SegmentMaterialEntity> _dal;
		private readonly IUser _user;

		public SegmentMaterialServices(IBaseRepository<SegmentMaterialEntity> dal, IUser user)
		{
			_dal = dal;
			BaseDal = dal;
			_user = user;
		}

		public async Task<List<SegmentMaterialEntity>> GetList(SegmentMaterialRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<SegmentMaterialEntity>()
				 .AndIF(!string.IsNullOrEmpty(reqModel.Key),
				 a => a.MaterialCode.Contains(reqModel.Key) ||
				 a.MaterialName.Contains(reqModel.Key) ||
				 a.MaterialVersionNumber.Contains(reqModel.Key) ||
				 a.PhaseCode.Contains(reqModel.Key) ||
				 a.PhaseName.Contains(reqModel.Key) ||
				 a.OperationCode.Contains(reqModel.Key) ||
				 a.OperationName.Contains(reqModel.Key) ||
				 a.ResourceCode.Contains(reqModel.Key) ||
				 a.ResourceName.Contains(reqModel.Key)
				 ).ToExpression();
			var data = await _dal.Db.Queryable<SegmentMaterialEntity>()
				.Where(whereExpression).ToListAsync();
			return data;
		}

		public async Task<PageModel<SegmentMaterialEntity>> GetPageList(SegmentMaterialRequestModel reqModel)
		{
			PageModel<SegmentMaterialEntity> result = new PageModel<SegmentMaterialEntity>();
			RefAsync<int> dataCount = 0;
			var whereExpression = Expressionable.Create<SegmentMaterialEntity>()
				 .AndIF(!string.IsNullOrEmpty(reqModel.Key),
				 a => a.MaterialCode.Contains(reqModel.Key) ||
				 a.MaterialName.Contains(reqModel.Key) ||
				 a.MaterialVersionNumber.Contains(reqModel.Key) ||
				 a.PhaseCode.Contains(reqModel.Key) ||
				 a.PhaseName.Contains(reqModel.Key) ||
				 a.OperationCode.Contains(reqModel.Key) ||
				 a.OperationName.Contains(reqModel.Key) ||
				 a.ResourceCode.Contains(reqModel.Key) ||
				 a.ResourceName.Contains(reqModel.Key)
				 ).ToExpression();
			var data = await _dal.Db.Queryable<SegmentMaterialEntity>()
				.Where(whereExpression)
				.ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
			result.dataCount = dataCount;
			result.data = data;
			return result;
		}

		/// <summary>
		/// 根据materialVersionId获取预处理长文本列表
		/// </summary>
		/// <param name="materialVersionId"></param>
		/// <returns></returns>
		public async Task<PageModel<MaterialProcessDataEntity>> GetProcessDataList(MaterialProcessDataRequestModel reqModel)
		{
			reqModel.Type = 1;
			reqModel.orderByFileds = "TextVersion";
			//获取工艺长文本列表
			MessageModel<PageModel<MaterialProcessDataEntity>> apiResult_processData = await HttpHelper.PostAsync<PageModel<MaterialProcessDataEntity>>("DFM", "api/MaterialProcessData/GetPageList", _user.GetToken(), reqModel);
			apiResult_processData.response.data.ForEach(data => { data.ProcessData = Enigma.Decrypt(data.ProcessData, data.Token); });
			return apiResult_processData.response;
		}

		/// <summary>
		/// 保存新版本预处理长文本
		/// </summary>
		/// <param name="entity"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> SaveProcessData(MaterialProcessDataEntity entity)
		{
			var result = new MessageModel<string>
			{
				success = false
			};
			//获取工艺长文本列表
			//var dic1 = new Dictionary<string, string>()
			//{
			//	{"id", entity.ID},
			//};
			MessageModel<MaterialProcessDataEntity> apiResult_processDatabyId = await HttpHelper.GetApiAsync<MaterialProcessDataEntity>("DFM", "api/MaterialProcessData/GetEntity/" + entity.ID, _user.GetToken(), null);
			if (apiResult_processDatabyId.response == null)
			{
				result.msg = "ProcessDataId未找到";
				return result;
			}
			var processData = apiResult_processDatabyId.response;
			//_ = int.TryParse(processData.TextVersion, out int versionNumber);
			//versionNumber++;
			//修改上一版本状态
			processData.Status = "1";
			processData.Modify(processData.ID, _user.Name.ToString());
			string key = Guid.NewGuid().ToString();
			MaterialProcessDataEntity pde = new()
			{
				VersionId = entity.VersionId,
				ProcessData = Enigma.Encrypt(entity.ProcessData, key),
				HashData = SHAHelper.Sha256(entity.ProcessData),
				//TextVersion = versionNumber.ToString(),
				TextVersion = processData.TextVersion + 1,
				IsReminded = "1",
				Status = "3",
				Token = key,
				Type = 1,
				//OrderId = "",
			};
			//pde.CreateCustomGuid(_user.Name);
			//调用API接口保存和更新数据
			try
			{
				MessageModel<string> apiResult_updatepde = await HttpHelper.PostAsync<string>("DFM", "api/MaterialProcessData/SaveForm", _user.GetToken(), processData);
				if (apiResult_updatepde.success)
				{
					MessageModel<string> apiResult_savepde = await HttpHelper.PostAsync<string>("DFM", "api/MaterialProcessData/SaveForm", _user.GetToken(), pde);
				}
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				return result;
			}
			result.success = true;
			result.msg = "操作成功";
			return result;
		}

		/// <summary>
		/// 发布/确认
		/// </summary>
		/// <param name="entity"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> Release(string id)
		{
			var result = new MessageModel<string>
			{
				success = false
			};
			//获取工艺长文本
			//var dic1 = new Dictionary<string, string>()
			//{
			//	{"id", id},
			//};
			MessageModel<MaterialProcessDataEntity> apiResult_processDatabyId = await HttpHelper.GetApiAsync<MaterialProcessDataEntity>("DFM", "api/MaterialProcessData/GetEntity/" + id, _user.GetToken(), null);
			if (apiResult_processDatabyId.response == null)
			{
				result.msg = "ProcessDataId未找到";
				return result;
			}
			var processData = apiResult_processDatabyId.response;
			processData.Status = "2";
			//确认时要更新确认时间和确认人
			processData.Reviewtime = DateTime.Now;
			processData.Reviewuserid = _user.Name.ToString();
			processData.Modify(processData.ID, _user.Name.ToString());
			//调用API接口更新数据
			try
			{
				MessageModel<string> apiResult_updatepde = await HttpHelper.PostAsync<string>("DFM", "api/MaterialProcessData/SaveForm", _user.GetToken(), processData);
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				return result;
			}
			result.success = true;
			result.msg = "操作成功";
			return result;
		}

		/// <summary>
		/// 更新工单
		/// </summary>
		/// <param name="entity"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> UpdatePO(string key)
		{
			var result = new MessageModel<string>
			{
				success = false
			};
			//更新工单逻辑




			result.success = true;
			result.msg = "操作成功";
			return result;
		}

	}
}