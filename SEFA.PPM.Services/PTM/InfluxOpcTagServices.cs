using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.IServices.PTM;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels.PTM;
using SEFA.Base.Common.HttpContextUser;
using MongoDB.Driver;
using System;
using System.Linq;
using SEFA.DFM.Model.Models;
using SEFA.Base.IRepository.UnitOfWork;
using System.Text.RegularExpressions;

namespace SEFA.PPM.Services.PTM
{
	public class InfluxOpcTagServices : BaseServices<InfluxOpcTagEntity>, IInfluxOpcTagServices
	{
		private readonly IBaseRepository<InfluxOpcTagEntity> _dal;
		private readonly IBaseRepository<InfluxOpcServerEntity> _dal1;
		private readonly IBaseRepository<EquipmentEntity> _dal2;
		private readonly IUser _user;
		private readonly IUnitOfWork _unitOfWork;

		public InfluxOpcTagServices(IBaseRepository<InfluxOpcTagEntity> dal, IUser user, IUnitOfWork unitOfWork, IBaseRepository<InfluxOpcServerEntity> dal1, IBaseRepository<EquipmentEntity> dal2)
		{
			_dal = dal;
			_user = user;
			BaseDal = dal;
			_unitOfWork = unitOfWork;
			_dal1 = dal1;
			_dal2 = dal2;
		}

		public async Task<List<InfluxOpcTagEntity>> GetList(InfluxOpcTagRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<InfluxOpcTagEntity>()
			   .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId == reqModel.EquipmentId)
			   .AndIF(!string.IsNullOrEmpty(reqModel.ServerId), a => a.ServerId == reqModel.ServerId)
			   .AndIF(reqModel.Type != null, a => a.Type == reqModel.Type)
			   .AndIF(reqModel.IsSave != null, a => a.IsSave == reqModel.IsSave)
			   .AndIF(!string.IsNullOrEmpty(reqModel.Search),
			   a =>
			   (a.Tag != null && a.Tag.Contains(reqModel.Search)) ||
			   (a.Name != null && a.Name.Contains(reqModel.Search)) ||
			   (a.Describe != null && a.Describe.Contains(reqModel.Search)) ||
			   (a.Formula != null && a.Formula.Contains(reqModel.Search))
			   )
			   .ToExpression();
			var data = await _dal.Db.Queryable<InfluxOpcTagEntity>()
				.Where(whereExpression).OrderByDescending(x => x.CreateDate).ToListAsync();
			return data;
		}
        public async Task<List<InfluxOpcTagEntity>> GetDistinctNameList (InfluxOpcTagRequestModel reqModel) {
			var data = await GetList(reqModel);
			var grupList = data.GroupBy(a => a.Name).Select(a => new InfluxOpcTagEntity { Name = a.Key }).OrderBy(a => a.Name).ToList();
			return grupList;
        }


        public async Task<PageModel<InfluxOpcTagEntity>> GetPageList(InfluxOpcTagRequestModel reqModel)
		{
			PageModel<InfluxOpcTagEntity> result = new PageModel<InfluxOpcTagEntity>();
			RefAsync<int> dataCount = 0;
			var whereExpression = Expressionable.Create<InfluxOpcTagEntity>()
			   .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId == reqModel.EquipmentId)
			   .AndIF(!string.IsNullOrEmpty(reqModel.ServerId), a => a.ServerId == reqModel.ServerId)
			   .AndIF(reqModel.Type != null, a => a.Type == reqModel.Type)
			   .AndIF(reqModel.IsSave != null, a => a.IsSave == reqModel.IsSave)
			   .AndIF(!string.IsNullOrEmpty(reqModel.Search),
			   a =>
			   (a.Tag != null && a.Tag.Contains(reqModel.Search)) ||
			   (a.Name != null && a.Name.Contains(reqModel.Search)) ||
			   (a.Describe != null && a.Describe.Contains(reqModel.Search)) ||
			   (a.Formula != null && a.Formula.Contains(reqModel.Search))
			   )
			   .ToExpression();
			var data = await _dal.Db.Queryable<InfluxOpcTagEntity>()
				.Where(whereExpression)
				.OrderByDescending(x => x.CreateDate)
				.ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
			result.dataCount = dataCount;
			result.data = data;
			return result;
		}

		public async Task<List<InfluxOpcTagModel>> GetModelList(InfluxOpcTagRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<InfluxOpcTagEntity, InfluxOpcServerEntity, EquipmentEntity>()
			   .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), (t, s, e) => t.EquipmentId == reqModel.EquipmentId)
			   .AndIF(!string.IsNullOrEmpty(reqModel.ServerId), (t, s, e) => t.ServerId == reqModel.ServerId)
			   .AndIF(reqModel.Type != null, (t, s, e) => t.Type == reqModel.Type)
			   .AndIF(reqModel.IsSave != null, (t, s, e) => t.IsSave == reqModel.IsSave)
			   .AndIF(!string.IsNullOrEmpty(reqModel.Search),
			   (t, s, e) =>
			   (t.Tag != null && t.Tag.Contains(reqModel.Search)) ||
			   (t.Name != null && t.Name.Contains(reqModel.Search)) ||
			   (t.Describe != null && t.Describe.Contains(reqModel.Search)) ||
			   (t.Formula != null && t.Formula.Contains(reqModel.Search))
			   )
			   .ToExpression();

			var result = await _dal.QueryMuch((t, s, e) =>
			new object[] {
					JoinType.Left,
					s.ID == t.ServerId,
					JoinType.Left,
					e.ID == t.EquipmentId,
			},
			(t, s, e) => new InfluxOpcTagModel
			{
				ID = t.ID,
				EquipmentCode = e.EquipmentCode,
				EquipmentName = e.EquipmentName,
				EquipmentId = t.EquipmentId,
				ServerId = t.ServerId,
				ServerName = s.Name + "(" + s.Code + ")",
				Tag = t.Tag,
				Name = t.Name,
				Describe = t.Describe,
				SaveCyc = t.SaveCyc,
				IsSave = t.IsSave,
				Type = t.Type,
				Formula = t.Formula,
				LastValue = t.LastValue,
				Deleted = t.Deleted,
				CreateDate = t.CreateDate,
				CreateUserId = t.CreateUserId,
				ModifyDate = t.ModifyDate,
				ModifyUserId = t.ModifyUserId,
				UpdateTimeStamp = t.UpdateTimeStamp,
			},
			whereExpression
			);
			return result;
		}

		public async Task<PageModel<InfluxOpcTagModel>> GetPageModelList(InfluxOpcTagRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<InfluxOpcTagEntity, InfluxOpcServerEntity, EquipmentEntity>()
			   .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), (t, s, e) => t.EquipmentId == reqModel.EquipmentId)
			   .AndIF(!string.IsNullOrEmpty(reqModel.ServerId), (t, s, e) => t.ServerId == reqModel.ServerId)
			   .AndIF(reqModel.Type != null, (t, s, e) => t.Type == reqModel.Type)
			   .AndIF(reqModel.IsSave != null, (t, s, e) => t.IsSave == reqModel.IsSave)
			   .AndIF(!string.IsNullOrEmpty(reqModel.Search),
			   (t, s, e) =>
			   (t.Tag != null && t.Tag.Contains(reqModel.Search)) ||
			   (t.Name != null && t.Name.Contains(reqModel.Search)) ||
			   (t.Describe != null && t.Describe.Contains(reqModel.Search)) ||
			   (t.Formula != null && t.Formula.Contains(reqModel.Search))
			   )
			   .ToExpression();

			var result = await _dal.QueryMuchPage((t, s, e) =>
			new object[] {
					JoinType.Left,
					s.ID == t.ServerId,
					JoinType.Left,
					e.ID == t.EquipmentId,
			},
			(t, s, e) => new InfluxOpcTagModel
			{
				ID = t.ID,
				EquipmentCode = e.EquipmentCode,
				EquipmentName = e.EquipmentName,
				EquipmentId = t.EquipmentId,
				ServerId = t.ServerId,
				ServerName = s.Name + "(" + s.Code + ")",
				Tag = t.Tag,
				Name = t.Name,
				Describe = t.Describe,
				SaveCyc = t.SaveCyc,
				IsSave = t.IsSave,
				Type = t.Type,
				Formula = t.Formula,
				LastValue = t.LastValue,
				Deleted = t.Deleted,
				CreateDate = t.CreateDate,
				CreateUserId = t.CreateUserId,
				ModifyDate = t.ModifyDate,
				ModifyUserId = t.ModifyUserId,
				UpdateTimeStamp = t.UpdateTimeStamp,
			},
			whereExpression,
			reqModel.pageIndex, reqModel.pageSize
			);
			return result;
		}

		public async Task<bool> SaveForm(InfluxOpcTagEntity entity)
		{
			if (string.IsNullOrEmpty(entity.ID))
			{
				var influxOpcTag = await _dal.FindEntity(x => x.Tag == entity.Tag);
				if (influxOpcTag != null)
				{
					return false;
				}
				entity.CreateCustomGuid(_user.Name);
				return await Add(entity) > 0;
			}
			else
			{
				return await Update(entity);
			}
		}

		public async Task<MessageModel<string>> ImportData(IEnumerable<dynamic> data)
		{
			MessageModel<string> result = new MessageModel<string>()
			{
				success = false,
				msg = "操作失败！",
			};
			try
			{
				var influxOpcServers = await _dal1.Query();
				var influxOpcTags = await _dal.Query();
				var equipments = await _dal2.Query();
				var updateInfluxOpcTagEntities = new List<InfluxOpcTagEntity>();
				var addInfluxOpcTagEntities = new List<InfluxOpcTagEntity>();
				foreach (IDictionary<string, object> row in data)
				{
					var Col1 = row["物理模型编码"]?.ToString();
					var Col2 = row["物理模型名称"]?.ToString();
					var Col3 = row["站点名称(编码)"]?.ToString();
					var Col4 = row["点位地址"]?.ToString();
					var Col5 = row["点位名称"]?.ToString();
					var Col6 = row["点位描述"]?.ToString();
					var Col7 = row["存储周期（秒）"]?.ToString();
					var Col8 = row["是否存储"]?.ToString();
					var Col9 = row["采集类型（0：周期性，1：变化时）"]?.ToString();
					var Col10 = row["计算公式"]?.ToString();

					var equipment = equipments.Find(x => x.EquipmentCode == Col1);
					if (equipment == null)
					{
						result.msg = $"未找到物理模型编码：{Col1}";
						return result;
					}
					var equipmentId = equipment.ID;
					var siteCode = "";
					string pattern = @"\((.*?)\)";
					Match match = Regex.Match(Col3, pattern);
					if (match.Success)
					{
						siteCode = match.Groups[1].Value;//提取的内容
					}
					else
					{
						result.msg = $"未解析到站点编码{Col3}";
						return result;
					}
					var influxOpcServer = influxOpcServers.Find(x => x.Code == siteCode);
					if (influxOpcServer == null)
					{
						result.msg = $"未找到站点编码：{siteCode}";
						return result;
					}
					var serverId = influxOpcServer.ID;
					var influxOpcTagEntity = influxOpcTags.FindAll(x => x.Tag == Col4).FirstOrDefault();
					if (influxOpcTagEntity == null)
					{
						influxOpcTagEntity = new InfluxOpcTagEntity();
						influxOpcTagEntity.CreateCustomGuid(_user.Name);
						addInfluxOpcTagEntities.Add(influxOpcTagEntity);
					}
					else
					{
						influxOpcTagEntity.Modify(influxOpcTagEntity.ID, _user.Name);
						updateInfluxOpcTagEntities.Add(influxOpcTagEntity);
					}
					influxOpcTagEntity.EquipmentId = equipmentId;
					influxOpcTagEntity.ServerId = serverId;
					influxOpcTagEntity.Tag = Col4;
					influxOpcTagEntity.Name = Col5;
					influxOpcTagEntity.Describe = Col6;
					influxOpcTagEntity.SaveCyc = Convert.ToInt32(Col7);
					influxOpcTagEntity.IsSave = Convert.ToBoolean(Col8);
					influxOpcTagEntity.Type = Convert.ToInt32(Col9);
					influxOpcTagEntity.Formula = Col10;
				}
				int updateCount = 0;
				int addCount = 0;
				_unitOfWork.BeginTran();
				if (updateInfluxOpcTagEntities.Count > 0)
				{
					await _dal.Update(updateInfluxOpcTagEntities);
					updateCount = updateInfluxOpcTagEntities.Count;
				}
				if (addInfluxOpcTagEntities.Count > 1000)
				{
					addCount = await _dal.AddBigData(addInfluxOpcTagEntities);
				}
				else if (addInfluxOpcTagEntities.Count > 0)
				{
					addCount = await _dal.Add(addInfluxOpcTagEntities);
				}
				_unitOfWork.CommitTran();
				result.msg = $"操作成功！新增数据{addCount}条，更新数据{updateCount}条";
			}
			catch (Exception e)
			{
				result.msg = e.StackTrace.ToString();
				return result;
			}
			result.success = true;
			return result;
		}
	}
}