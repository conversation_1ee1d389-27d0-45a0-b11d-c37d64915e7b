
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.ViewModels.PTM;
using SEFA.Base.Common.HttpContextUser;
using SEFA.MKM.Model.Models;
using SEFA.Base.IRepository.UnitOfWork;
using Newtonsoft.Json.Linq;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.PTM.Model.ViewModels;
using System;
using Abp.Domain.Entities;
using SEFA.DFM.Model.ViewModels;
using SEFA.PPM.IServices.PTM;
using SEFA.PPM.Model.ViewModels;
using System.Linq;
using SEFA.Base.Common.LogHelper;
using SEFA.MKM.Model.ViewModels.View;

namespace SEFA.PPM.Services.PTM
{
	public class TippingPrecheckViewServices : BaseServices<TippingPrecheckViewEntity>, ITippingPrecheckViewServices
	{
		private readonly IBaseRepository<TippingPrecheckViewEntity> _dal;
		private readonly IBaseRepository<BatchEntity> _dal2;
		private readonly IBaseRepository<ContainerHistoryEntity> _dal3;
		private readonly IBaseRepository<MaterialInventoryEntity> _dal4;
		private readonly IBaseRepository<ContainerEntity> _dal5;

		private readonly IUnitOfWork _unitOfWork;
		private readonly IUser _user;

		public TippingPrecheckViewServices(IBaseRepository<TippingPrecheckViewEntity> dal, IBaseRepository<BatchEntity> dal2, IBaseRepository<ContainerHistoryEntity> dal3, IBaseRepository<MaterialInventoryEntity> dal4, IBaseRepository<ContainerEntity> dal5, IUnitOfWork unitOfWork, IUser user)
		{
			this._dal = dal;
			this._dal2 = dal2;
			this._dal3 = dal3;
			this._dal4 = dal4;
			this._dal5 = dal5;
			base.BaseDal = dal;
			_unitOfWork = unitOfWork;
			_user = user;

		}

		public async Task<List<TippingPrecheckViewEntity>> GetList(TippingPrecheckViewRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<TippingPrecheckViewEntity>()
			   .AndIF(!string.IsNullOrEmpty(reqModel.Status), a => a.Status.Equals(reqModel.Status))
			   .AndIF(!string.IsNullOrEmpty(reqModel.ContainerName), a => a.ContainerName.Equals(reqModel.ContainerName))
			   //.AndIF(!string.IsNullOrEmpty(reqModel.BatchId), a => a.Status.Equals(reqModel.BatchId))
			   .ToExpression();
			var data = await _dal.Db.Queryable<TippingPrecheckViewEntity>()
				.Where(whereExpression).ToListAsync();
			return data.OrderBy(p => p.Status).ThenBy(p => p.ModifyDate).ToList(); ;
		}



		public async Task<MessageModel<string>> GetCount(TippingPrecheckViewRequestModel reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "获取成功！",
				success = true,
				response = "0/0"
			};
			try
			{
				var whereExpression = Expressionable.Create<TippingPrecheckViewEntity>()
			   .AndIF(!string.IsNullOrEmpty(reqModel.ContainerName), a => a.ContainerName.Equals(reqModel.ContainerName))
			   //.AndIF(!string.IsNullOrEmpty(reqModel.BatchId), a => a.Status.Equals(reqModel.BatchId))
			   .ToExpression();
				var data = await _dal.Db.Queryable<TippingPrecheckViewEntity>()
					.Where(whereExpression).ToListAsync();
				var allCount = data?.Count ?? 0;
				var isCheckCount = data?.FindAll(x => x.Status == "1")?.Count ?? 0;
				result.response = isCheckCount + "/" + allCount;
			}
			catch (Exception ex)
			{
				result.success = false;
				result.msg = ex.Message;
				throw;
			}
			return result;
		}

		public async Task<PageModel<TippingPrecheckViewEntity>> GetPageList(TippingPrecheckViewRequestModel reqModel)
		{
			PageModel<TippingPrecheckViewEntity> result = new PageModel<TippingPrecheckViewEntity>();
			RefAsync<int> dataCount = 0;
			var whereExpression = Expressionable.Create<TippingPrecheckViewEntity>()
			   .AndIF(!string.IsNullOrEmpty(reqModel.Status), a => a.Status.Equals(reqModel.Status))
			   .AndIF(!string.IsNullOrEmpty(reqModel.ContainerName), a => a.Status.Equals(reqModel.ContainerName))
			   //.AndIF(!string.IsNullOrEmpty(reqModel.BatchId), a => a.Status.Equals(reqModel.BatchId))
			   .ToExpression();
			var data = await _dal.Db.Queryable<TippingPrecheckViewEntity>()
				.Where(whereExpression)
				.ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
			result.dataCount = dataCount;
			result.data = data;
			return result;
		}

		public async Task<bool> SaveForm(TippingPrecheckViewEntity entity)
		{
			if (string.IsNullOrEmpty(entity.ID))
			{
				return await Add(entity) > 0;
			}
			else
			{
				return await Update(entity);
			}
		}

		/// <summary>
		/// 预检查操作：通过key值判断执行不同逻辑 key=1开始预检查,key=2扫描库存
		/// Body需要根据key传不同格式的数据
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> Precheck(ConsolPoRequestModel reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			string batchId = string.Empty;

			JObject obj;
			try
			{
				obj = JObject.Parse(reqModel.Body);
				batchId = obj["BatchId"].ToString();
			}
			catch (Exception ex)
			{
				result.msg = "缺少参数[BatchId]";
				return result;
			}
			BatchEntity entity = await _dal2.FindEntity(batchId);
			bool updateBatch = false;
			if (entity == null)
			{
				result.msg = "BatchId未找到";
				return result;
			}
			//写入容器记录表
			ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
			switch (reqModel.Key)
			{
				//1开始预检查
				case "1":
					updateBatch = true;
					//更新Batch表PrepStatus
					entity.PrepStatus = "3";//START_PRECHECK
											//hisModel.ContainerId = obj["ContainerId"].ToString();
											//hisModel.Type = "Inventory pre-check";
											//hisModel.Comment = "Inventory pre-check Start";
											//hisModel.EquipmentId = obj["EquipmentId"].ToString();
											//hisModel.EquipmentRequirementId = obj["EquipmentRequirementId"].ToString();
											//hisModel.State = "";
											//hisModel.MaterialId = obj["MaterialId"].ToString();
											//hisModel.SublotId = obj["SublotId"].ToString();
											//hisModel.Quantity = obj["Quantity"].ToString();
											//hisModel.QuantityUomId = obj["QuantityUomId"].ToString();
											//hisModel.LotId = obj["LotId"].ToString();
											//hisModel.ExpirationDate = DateTime.Parse(obj["ExpirationDate"].ToString());
					break;
				//2扫描库存
				case "2":
					string traceCode = string.Empty;
					try
					{
						traceCode = obj["TraceCode"].ToString();
					}
					catch (Exception ex)
					{
						result.msg = "缺少参数[TraceCode]";
						return result;
					}
					var precheckViewEntity = await _dal.FindEntity(x => x.BatchId == batchId && x.Tracecode == traceCode);
					if (precheckViewEntity == null)
					{
						result.msg = "该批次下未找到此TraceCode：" + traceCode;
						return result;
					}
					MaterialInventoryEntity inventoryModel = await _dal4.FindEntity(precheckViewEntity.ID);
					hisModel.Type = "Inventory pre-check";
					hisModel.Comment = "Inventory pre-check Done";
					hisModel.ContainerId = inventoryModel.ContainerId;
					//hisModel.EquipmentRequirementId = inventoryModel.EquipmentRequirementId;
					//hisModel.MaterialId = inventoryModel.MaterialId;
					hisModel.SublotId = inventoryModel.SublotId;
					hisModel.Quantity = inventoryModel.Quantity.ToString();
					hisModel.QuantityUomId = inventoryModel.QuantityUomId;
					hisModel.LotId = inventoryModel.LotId;
					//hisModel.ExpirationDate = inventoryModel.ExpirationDate;
					var data = await _dal.Db.Queryable<TippingPrecheckViewEntity>()
					.Where(x => x.BatchId == batchId && x.SublotId != inventoryModel.SublotId && x.Status == "0").ToListAsync();
					if (data.Count == 0)
					{
						updateBatch = true;
						entity.PrepStatus = "4";//PRECHECK_DONE
					}
					break;
				default:
					break;
			}
			_unitOfWork.BeginTran();
			try
			{
				if (updateBatch)
				{
					entity.Modify(entity.ID, _user.Name.ToString());
					await _dal2.Update(entity);
				}
				hisModel.CreateCustomGuid(_user.Name);
				await _dal3.Add(hisModel);
				result.msg = "操作成功！";
				result.success = true;
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				_unitOfWork.RollbackTran();
			}
			return result;
		}

		/// <summary>
		/// 预检查扫描库存
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> ScanTraceCode(string traceCode)
		{
			SerilogServer.LogDebug($"【ScanTraceCode】request:{traceCode}", "PDALog");
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			var precheckViewEntity = await _dal.FindEntity(x => x.Tracecode == traceCode);
			if (precheckViewEntity == null)
			{
				result.msg = "未找到子批次：" + traceCode;
				return result;
			}
			if (precheckViewEntity.Status == "1")
			{
				result.msg = "该物料已完成预检查";
				return result;
			}
			BatchEntity batchEntity = await _dal2.FindEntity(precheckViewEntity.BatchId);
			if (batchEntity == null)
			{
				result.msg = "未找到BatchId";
				return result;
			}
			if (batchEntity.PrepStatus != "6")
			{
				result.msg =$"当前批次状态（{batchEntity.PrepStatus}）不允许进行投料预检查";
				return result;
			}
			//写入容器记录表
			MaterialInventoryEntity inventoryModel = await _dal4.FindEntity(precheckViewEntity.ID);

			ContainerHistoryEntity hisModel = new()
			{
				Type = "Inventory pre-check",
				Comment = "Inventory pre-check Done",
				ContainerId = inventoryModel.ContainerId,
				//hisModel.EquipmentRequirementId = inventoryModel.EquipmentRequirementId;
				//hisModel.MaterialId = inventoryModel.MaterialId;
				SublotId = inventoryModel.SublotId,
				Quantity = inventoryModel.Quantity.ToString(),
				QuantityUomId = inventoryModel.QuantityUomId,
				LotId = inventoryModel.LotId
			};
			//hisModel.ExpirationDate = inventoryModel.ExpirationDate;
			var data = await _dal.Db.Queryable<TippingPrecheckViewEntity>()
			.Where(x => x.BatchId == precheckViewEntity.BatchId && x.SublotId != inventoryModel.SublotId && x.Status == "0").ToListAsync();
			_unitOfWork.BeginTran();
			try
			{
				//插入容器记录表
				hisModel.CreateCustomGuid(_user.Name);
				await _dal3.Add(hisModel);
				if (data.Count == 0)
				{
					result.msgDev = "1";
					//所有库存都检查完，更新批次投料状态
					batchEntity.PrepStatus = "7";//PRECHECK_DONE
					batchEntity.Modify(batchEntity.ID, _user.Name);
					await _dal2.Update(batchEntity);
				}
				result.msg = "操作成功！";
				result.success = true;
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				result.msgDev = null;
				result.msg = ex.Message;
				_unitOfWork.RollbackTran();
			}
			return result;
		}

	}
}