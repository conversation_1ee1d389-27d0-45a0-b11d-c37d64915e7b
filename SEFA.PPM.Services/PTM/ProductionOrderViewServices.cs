
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using InfluxDB.Client.Api.Domain;
using System.Linq;
using SEFA.PPM.Model.Models.PTM;

namespace SEFA.PPM.Services
{
	public class ProductionOrderViewServices : BaseServices<ProductionOrderViewEntity>, IProductionOrderViewServices
	{
		private readonly IBaseRepository<ProductionOrderViewEntity> _dal;
		public ProductionOrderViewServices(IBaseRepository<ProductionOrderViewEntity> dal)
		{
			this._dal = dal;
			base.BaseDal = dal;
		}

		public async Task<List<ProductionOrderViewEntity>> GetList(ProductionOrderViewRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<ProductionOrderViewEntity>()
								.AndIF(!string.IsNullOrEmpty(reqModel.EquipmentCodes), a => a.EquipmentCodes.Contains(reqModel.EquipmentCodes))
								.AndIF(reqModel.PoStatus != null, a => a.PoStatus == reqModel.PoStatus)
								.AndIF(!string.IsNullOrEmpty(reqModel.ProductionOrderNo), a => a.ProductionOrderNo.Contains(reqModel.ProductionOrderNo))
								.AndIF(!string.IsNullOrEmpty(reqModel.Resource), a => a.Resource.Contains(reqModel.Resource))
								.AndIF(!string.IsNullOrEmpty(reqModel.Code), a => a.ProductionOrderNo.Contains(reqModel.Code))
								.AndIF(!string.IsNullOrEmpty(reqModel.Formula), a => a.ProductionOrderNo.Contains(reqModel.Formula))
								.AndIF(reqModel.Status != null, a => a.Status == reqModel.Status)
								.AndIF(!string.IsNullOrEmpty(reqModel.Search), a =>
									a.Formula.Contains(reqModel.Formula) ||
									a.ProductionOrderNo.Contains(reqModel.Search) ||
									a.EquipmentCodes.Contains(reqModel.Search) ||
									a.Resource.Contains(reqModel.Search) ||
									a.Code.Contains(reqModel.Search) ||
									a.Description.Contains(reqModel.Search) ||
									a.EquipmentCodes.Contains(reqModel.Search) ||
									a.Unit1.Contains(reqModel.Search)
									 )
								 .AndIF(reqModel.StartTime.HasValue, a => a.SapDate >= reqModel.StartTime)
								 .AndIF(reqModel.EndTime.HasValue, a => a.SapDate <= reqModel.EndTime)
							   .ToExpression();
			var data = await _dal.FindList(whereExpression, x => x.OrderCloseTime, false);
			return data;
		}

		public async Task<PageModel<ProductionOrderViewEntity>> GetPageList(ProductionOrderViewRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<ProductionOrderViewEntity>()
										 .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentCodes), a => a.EquipmentCodes.Contains(reqModel.EquipmentCodes))
										 .AndIF(reqModel.PoStatus != null, a => a.PoStatus == reqModel.PoStatus)
										 .AndIF(!string.IsNullOrEmpty(reqModel.ProductionOrderNo), a => a.ProductionOrderNo.Contains(reqModel.ProductionOrderNo))
										 .AndIF(!string.IsNullOrEmpty(reqModel.Resource), a => a.Resource.Contains(reqModel.Resource))
										 .AndIF(!string.IsNullOrEmpty(reqModel.Code), a => a.Code.Contains(reqModel.Code))
										 .AndIF(!string.IsNullOrEmpty(reqModel.Formula), a => a.Formula.Contains(reqModel.Formula))
										 .AndIF(reqModel.Status != null, a => a.Status == reqModel.Status)
										 .AndIF(!string.IsNullOrEmpty(reqModel.Search), a =>
											a.Formula.Contains(reqModel.Search) ||
											a.ProductionOrderNo.Contains(reqModel.Search) ||
											a.EquipmentCodes.Contains(reqModel.Search) ||
											a.Resource.Contains(reqModel.Search) ||
											a.Code.Contains(reqModel.Search) ||
											a.Description.Contains(reqModel.Search) ||
											a.EquipmentCodes.Contains(reqModel.Search) ||
											a.Unit1.Contains(reqModel.Search)
											 )
										 .AndIF(reqModel.StartTime.HasValue, a => a.SapDate >= reqModel.StartTime)
										 .AndIF(reqModel.EndTime.HasValue, a => a.SapDate <= reqModel.EndTime)
									 .ToExpression();
			var query = _dal.Db.Queryable<ProductionOrderViewEntity>().Where(whereExpression).OrderByDescending(x => x.CreateDate);
			//.ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
			PageModel<ProductionOrderViewEntity> result = new PageModel<ProductionOrderViewEntity>()
			{
				page = reqModel.pageIndex,
				pageSize = reqModel.pageSize
			};
			int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引
			var data = await query.ToListAsync();
			var rDat = data.Skip(startIndex).Take(reqModel.pageSize).ToList();
			result.dataCount = data.Count;
			result.data = rDat;
			return result;
		}

		public async Task<bool> SaveForm(ProductionOrderViewEntity entity)
		{
			if (string.IsNullOrEmpty(entity.ID))
			{
				return await this.Add(entity) > 0;
			}
			else
			{
				return await this.Update(entity);
			}
		}
	}
}