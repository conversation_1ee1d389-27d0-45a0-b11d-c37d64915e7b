using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels.PTM;
using SEFA.PPM.IServices.PTM;
using SEFA.Base.Common.HttpContextUser;
using System;
using SEFA.MKM.Model.Models;
using SEFA.PPM.Model.Models;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Common.Common;
using System.Drawing.Printing;
using SEFA.Base;
using System.Linq;
using SEFA.Base.Common.LogHelper;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.PPM.Services.PTM
{
	public class TippingPrecheckServices : BaseServices<TippingPrecheckEntity>, ITippingPrecheckServices
	{
		private readonly IBaseRepository<TippingPrecheckEntity> _dal;
		private readonly IBaseRepository<BatchEntity> _dal2;
		private readonly IBaseRepository<ContainerHistoryEntity> _dal3;
		private readonly IBaseRepository<MaterialInventoryEntity> _dal4;
		private readonly IBaseRepository<ContainerEntity> _dal5;
		private readonly IUnitOfWork _unitOfWork;
		private readonly IUser _user;
		private readonly IProcessDataViewServices _processDataViewServices;

		public TippingPrecheckServices(IBaseRepository<TippingPrecheckEntity> dal, IBaseRepository<BatchEntity> dal2, IBaseRepository<ContainerHistoryEntity> dal3, IBaseRepository<MaterialInventoryEntity> dal4, IBaseRepository<ContainerEntity> dal5, IUnitOfWork unitOfWork, IUser user, IProcessDataViewServices processDataViewServices)
		{
			this._dal = dal;
			this._dal2 = dal2;
			this._dal3 = dal3;
			this._dal4 = dal4;
			this._dal5 = dal5;
			base.BaseDal = dal;
			_unitOfWork = unitOfWork;
			_user = user;
			_processDataViewServices = processDataViewServices;
		}

		public async Task<List<TippingPrecheckEntity>> GetList(TippingPrecheckRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<TippingPrecheckEntity>()
			   //.AndIF(!string.IsNullOrEmpty(reqModel.Status), a => a.Status.Equals(reqModel.Status))
			   .AndIF(!string.IsNullOrEmpty(reqModel.Name), a => a.Name.Contains(reqModel.Name))
			   .AndIF(!string.IsNullOrEmpty(reqModel.LineName), a => a.LineName.Contains(reqModel.LineName))
			   .AndIF(!string.IsNullOrEmpty(reqModel.BatchCode), a => a.BatchCode.Contains(reqModel.BatchCode))
			   .AndIF(!string.IsNullOrEmpty(reqModel.ProductionOrderNo), a => a.ProductionOrderNo.Contains(reqModel.ProductionOrderNo))
			   .AndIF(!string.IsNullOrEmpty(reqModel.Machine), a => a.Machine.Contains(reqModel.Machine))
			   .AndIF(reqModel.StartTime.HasValue, a => a.PlanStartTime >= reqModel.StartTime.Value)
			   .AndIF(reqModel.EndTime.HasValue, a => a.PlanStartTime <= reqModel.EndTime.Value)
			   .AndIF(reqModel.ShowCompleted == true, a => Convert.ToInt32(a.PrepStatus) >= 7 && Convert.ToInt32(a.PrepStatus) <= 9)
			   .AndIF(reqModel.ShowCompleted == false, a => Convert.ToInt32(a.PrepStatus) >= 3 && Convert.ToInt32(a.PrepStatus) < 7)
			   .ToExpression();
			var data = await _dal.Db.Queryable<TippingPrecheckEntity>()
				.Where(whereExpression).ToListAsync();
			return data;
		}

		public async Task<PageModel<TippingPrecheckEntity>> GetPageList1(TippingPrecheckRequestModel reqModel)
		{
			PageModel<TippingPrecheckEntity> result = new PageModel<TippingPrecheckEntity>();
			RefAsync<int> dataCount = 0;
			int offset = (reqModel.pageIndex - 1) * reqModel.pageSize;
			var whereExpression = Expressionable.Create<TippingPrecheckEntity>()
			   .AndIF(!string.IsNullOrEmpty(reqModel.Name), a => a.Name.Contains(reqModel.Name))
			   .AndIF(!string.IsNullOrEmpty(reqModel.LineName), a => a.LineName.Contains(reqModel.LineName))
			   .AndIF(!string.IsNullOrEmpty(reqModel.BatchCode), a => a.BatchCode.Contains(reqModel.BatchCode))
			   .AndIF(!string.IsNullOrEmpty(reqModel.ProductionOrderNo), a => a.ProductionOrderNo.Contains(reqModel.ProductionOrderNo))
			   .AndIF(!string.IsNullOrEmpty(reqModel.Machine), a => a.Machine.Contains(reqModel.Machine))
			   .AndIF(reqModel.StartTime.HasValue, a => a.PlanStartTime >= reqModel.StartTime.Value)
			   .AndIF(reqModel.EndTime.HasValue, a => a.PlanStartTime <= reqModel.EndTime.Value)
			   .AndIF(reqModel.ShowCompleted == true, a => Convert.ToInt32(a.PrepStatus) >= 7 && Convert.ToInt32(a.PrepStatus) <= 9)
			   .AndIF(reqModel.ShowCompleted == false, a => Convert.ToInt32(a.PrepStatus) >= 3 && Convert.ToInt32(a.PrepStatus) < 7)
			   .ToExpression();
			var data = await _dal.Db.Queryable<TippingPrecheckEntity>()
				.Where(whereExpression).OrderBy(a => a.LineCode).OrderBy(p => p.PlanStartTime).OrderBy(a => a.ProductionOrderNo)
				.ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
			result.dataCount = dataCount;
			result.data = data;
			return result;
		}

		public async Task<PageModel<TippingPrecheckEntity>> GetPageList(TippingPrecheckRequestModel reqModel)
		{
			RefAsync<int> dataCount = 0;
			var whereExpression = Expressionable.Create<TippingPrecheckEntity>()
			   .AndIF(!string.IsNullOrEmpty(reqModel.Name), a => a.Name.Contains(reqModel.Name))
			   .AndIF(!string.IsNullOrEmpty(reqModel.LineName), a => a.LineName.Contains(reqModel.LineName))
			   .AndIF(!string.IsNullOrEmpty(reqModel.BatchCode), a => a.BatchCode.Contains(reqModel.BatchCode))
			   .AndIF(!string.IsNullOrEmpty(reqModel.ProductionOrderNo), a => a.ProductionOrderNo.Contains(reqModel.ProductionOrderNo))
			   .AndIF(!string.IsNullOrEmpty(reqModel.Machine), a => a.Machine.Contains(reqModel.Machine))
			   .AndIF(reqModel.ShowCompleted == true, a => Convert.ToInt32(a.PrepStatus) >= 7 && Convert.ToInt32(a.PrepStatus) <= 9)
			   .AndIF(reqModel.ShowCompleted == false, a => Convert.ToInt32(a.PrepStatus) >= 3 && Convert.ToInt32(a.PrepStatus) < 7)
			   .ToExpression();
			var data = await _dal.Db.Queryable<TippingPrecheckEntity>()
				.Where(whereExpression).OrderBy(a => a.LineCode).OrderBy(p => p.PlanStartTime).OrderBy(a => a.ProductionOrderNo).ToListAsync();
			if (reqModel.StartTime.HasValue)
			{
				data = data.FindAll(a => a.PlanStartTime >= reqModel.StartTime.Value);
			}
			if (reqModel.EndTime.HasValue)
			{
				data = data.FindAll(a => a.PlanStartTime <= reqModel.EndTime.Value);
			}
			PageModel<TippingPrecheckEntity> result = new PageModel<TippingPrecheckEntity>()
			{
				page = reqModel.pageIndex,
				pageSize = reqModel.pageSize
			};
			int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
			var rDat = data.Skip(startIndex).Take(reqModel.pageSize).ToList();
			result.dataCount = data.Count;
			result.data = rDat;
			return result;
		}

		/// <summary>
		/// 预检查扫描备料大标签
		/// </summary>
		/// <param name="containerCode"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> ScanContainerCode(string containerCode)
		{
			SerilogServer.LogDebug($"【ScanContainerCode】request:{containerCode}", "PDALog");
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			string code = string.Empty;
			var containerEntity = await _dal5.FindEntity(x => x.Name == containerCode);
			if (containerEntity == null)
			{
				result.msg = "未找到ContainerCode";
				return result;
			}
			BatchEntity batchEntity = await _dal2.FindEntity(containerEntity.ProductionBatchId);
			if (batchEntity == null)
			{
				result.msg = "未找到BatchId";
				return result;
			}
			result.response = batchEntity.ID;
			var messageModel = await _processDataViewServices.GetLastProcessData(batchEntity.MaterialVersionId, null);
			var processData = messageModel.response;
			//if (processData == null)
			//{
			//	result.msg = "通过MaterialVersionId未找到ProcessData";
			//	return result;
			//}
			if (processData?.Status == "3")
			{
				var processDataModel = new ProcessDataModel();
				var messageModel2 = await _processDataViewServices.GetLastProcessData(batchEntity.MaterialVersionId, "1");
				var processData2 = messageModel2?.response;
				if (processData2 != null)
				{
					processDataModel.Pending_Release = new ProcessModel() { ID = processData.ID, ProcessData = processData.ProcessData };
					processDataModel.Disable = new ProcessModel() { ID = processData2.ID, ProcessData = processData2.ProcessData };
				}
				processDataModel.BatchId = batchEntity.ID;
				result.success = true;
				result.msg = "长文本状态为Pending_Release";
				result.status = 300;
				result.response = FAJsonConvert.ToJson(processDataModel);
				return result;
			}
			int.TryParse(batchEntity.PrepStatus, out int status);
			_unitOfWork.BeginTran();
			try
			{
				if (status == 3)
				{
					//更新Batch表PrepStatus
					batchEntity.PrepStatus = "6";//START_PRECHECK
					batchEntity.Modify(batchEntity.ID, _user.Name.ToString());
					await _dal2.Update(batchEntity);
					_unitOfWork.CommitTran();
				}
				else if (status < 3)
				{
					result.msg = "未完成拼锅";
					//result.msg = "未完成备料后核对";
					return result;
				}
				//else if (status > 6)
				//{
				//	result.msg = "已完成投料前检查";
				//	return result;
				//}
				result.msg = "操作成功！";
				result.success = true;
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				_unitOfWork.RollbackTran();
			}
			return result;
		}
		public class ProcessDataModel
		{
			public ProcessModel Pending_Release { get; set; }
			public ProcessModel Disable { get; set; }
			public string BatchId { get; set; }
		}

		public class ProcessModel
		{
			public string ID { get; set; }
			public string ProcessData { get; set; }
		}
	}
}