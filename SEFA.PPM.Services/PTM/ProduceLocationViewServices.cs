
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.PPM.Services
{
    public class ProduceLocationViewServices : BaseServices<ProduceLocationViewEntity>, IProduceLocationViewServices
    {
        private readonly IBaseRepository<ProduceLocationViewEntity> _dal;
        public ProduceLocationViewServices(IBaseRepository<ProduceLocationViewEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<ProduceLocationViewEntity>> GetList(ProduceLocationViewRequestModel reqModel)
        {
			List<ProduceLocationViewEntity> result = new List<ProduceLocationViewEntity>();
			RefAsync<int> dataCount = 0;
			var whereExpression = Expressionable.Create<ProduceLocationViewEntity>()
				.AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId.Equals(reqModel.EquipmentId))
				.ToExpression();
			var data = await _dal.Db.Queryable<ProduceLocationViewEntity>()
				.Where(whereExpression).ToListAsync();
			return data;
		}

        public async Task<PageModel<ProduceLocationViewEntity>> GetPageList(ProduceLocationViewRequestModel reqModel)
        {
			PageModel<ProduceLocationViewEntity> result = new PageModel<ProduceLocationViewEntity>();
			RefAsync<int> dataCount = 0;
			var whereExpression = Expressionable.Create<ProduceLocationViewEntity>()
			   .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId.Equals(reqModel.EquipmentId))
			   .ToExpression();
			var data = await _dal.Db.Queryable<ProduceLocationViewEntity>()
				.Where(whereExpression)
				.ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
			result.dataCount = dataCount;
			result.data = data;
			return result;
		}

        public async Task<bool> SaveForm(ProduceLocationViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}