
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.Base.Common.HttpContextUser;
using SEFA.MKM.Model.Models;
using SEFA.Base.IRepository.UnitOfWork;
using System;
using SEFA.PPM.Model.ViewModels.PTM;
using SEFA.PPM.Model.Models.PTM;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.DFM.Model.Models;
using BatchEntity = SEFA.PPM.Model.Models.BatchEntity;
using EquipmentEntity = SEFA.DFM.Model.Models.EquipmentEntity;
using SEFA.DFM.Model.ViewModels;
using SEFA.MKM.Model.Models.MKM;
using Abp.Extensions;
using SEFA.MKM.IServices;
using SEFA.MKM.Services;
using static System.Collections.Specialized.BitVector32;
using SEFA.PPM.Model.Models.Interface;
using System.Linq;
using NodaTime.Calendars;
using Microsoft.Extensions.Logging;
using FunctionPropertyEntity = SEFA.DFM.Model.Models.FunctionPropertyEntity;
using AutoMapper;
using ActionPropertyEntity = SEFA.PPM.Model.Models.PTM.ActionPropertyEntity;
using SEFA.PTM.Model.Models;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.Common.Common;
using SEFA.Base.Extensions;
using Microsoft.AspNetCore.DataProtection.KeyManagement;
using Castle.Core.Internal;

namespace SEFA.PPM.Services
{
	public class TippingMlistViewServices : BaseServices<TippingMlistViewEntity>, ITippingMlistViewServices
	{
		private readonly IBaseRepository<TippingMlistViewEntity> _dal;
		private readonly IBaseRepository<BatchEntity> _dal2;
		private readonly IBaseRepository<ContainerHistoryEntity> _dal3;
		private readonly IBaseRepository<MaterialInventoryEntity> _dal4;
		private readonly IBaseRepository<PoConsumeActualEntity> _dal5;
		private readonly IBaseRepository<PoProducedExecutionEntity> _dal6;
		private readonly IBaseRepository<PoConsumeRequirementEntity> _dal7;
		private readonly IBaseRepository<MaterialSubLotEntity> _dal8;
		private readonly IBaseRepository<TippingSclistEntity> _dal9;
		private readonly IBaseRepository<MaterialLotEntity> _dal10;
		private readonly IBaseRepository<InventorylistingViewEntity> _dal11;
		private readonly IBaseRepository<EquipmentEntity> _dal12;
		private readonly IBaseRepository<BatchConsumeRequirementEntity> _dal13;
		private readonly IBaseRepository<ContainerEntity> _dal14;
		private readonly IBaseRepository<FunctionPropertyEntity> _funprdal;
		private readonly IBaseRepository<FunctionPropertyValueEntity> _funprvdal;
		private readonly IBaseRepository<ProductionOrderEntity> _proOrderdal;
		private readonly IBaseRepository<BProductionOrderListViewEntity> _bproOrderdal;
		private readonly IUnitOfWork _unitOfWork;
		private readonly IUser _user;
		private readonly IContainerServices _containerServices;
		private readonly IInterfaceServices _iInterfaceServices;
		private readonly IInventorylistingViewServices _iInventorylistingViewServices;
		private readonly IMapper _mapper;
		private readonly IBaseRepository<LogsheetEntity> _logsheetdal;
		private readonly IBaseRepository<CookConfirmationEntity> _cookConfdal;
		private readonly IBaseRepository<ActionPropertyEntity> _dal29;
		private readonly IBaseRepository<ConsumeViewEntity> _dal30;
		private readonly IRedisBasketRepository _redisBasketRepository;


		public TippingMlistViewServices(IBaseRepository<TippingMlistViewEntity> dal,
			IBaseRepository<BatchEntity> dal2,
			IBaseRepository<ContainerHistoryEntity> dal3,
			IBaseRepository<MaterialInventoryEntity> dal4,
			IBaseRepository<PoConsumeActualEntity> dal5,
			IBaseRepository<PoProducedExecutionEntity> dal6,
			IBaseRepository<PoConsumeRequirementEntity> dal7,
			IBaseRepository<MaterialSubLotEntity> dal8,
			IBaseRepository<TippingSclistEntity> dal9,
			IBaseRepository<MaterialLotEntity> dal10,
			IBaseRepository<InventorylistingViewEntity> dal11,
			IBaseRepository<EquipmentEntity> dal12,
			IUnitOfWork unitOfWork,
			IUser user,
			IContainerServices containerServices
,
			IBaseRepository<BatchConsumeRequirementEntity> dal13,
			IBaseRepository<ContainerEntity> dal14,
			IInterfaceServices iInterfaceServices,
			IBaseRepository<FunctionPropertyEntity> funprdal,
			IBaseRepository<FunctionPropertyValueEntity> funprvdal,
			IInventorylistingViewServices iInventorylistingViewServices,
			IMapper mapper,
			IBaseRepository<ProductionOrderEntity> proOrderdal,
			IBaseRepository<LogsheetEntity> logsheetdal,
			IBaseRepository<CookConfirmationEntity> cookConfdal,
			IBaseRepository<ActionPropertyEntity> dal29,
			IBaseRepository<ConsumeViewEntity> dal30,
			IBaseRepository<BProductionOrderListViewEntity> bproOrderdal,
			IRedisBasketRepository redisBasketRepository)
		{
			this._dal = dal;
			this._dal2 = dal2;
			this._dal3 = dal3;
			this._dal4 = dal4;
			this._dal5 = dal5;
			this._dal6 = dal6;
			this._dal7 = dal7;
			this._dal8 = dal8;
			this._dal9 = dal9;
			this._dal10 = dal10;
			this._dal11 = dal11;
			this._dal12 = dal12;
			this._containerServices = containerServices;
			base.BaseDal = dal;
			_unitOfWork = unitOfWork;
			_user = user;
			_dal13 = dal13;
			_dal14 = dal14;
			_iInterfaceServices = iInterfaceServices;
			_funprdal = funprdal;
			_funprvdal = funprvdal;
			_iInventorylistingViewServices = iInventorylistingViewServices;
			_mapper = mapper;
			_proOrderdal = proOrderdal;
			_logsheetdal = logsheetdal;
			_cookConfdal = cookConfdal;
			_dal29 = dal29;
			_dal30 = dal30;
			_bproOrderdal = bproOrderdal;
			_redisBasketRepository = redisBasketRepository;
		}

		public TippingMlistViewServices(IBaseRepository<TippingMlistViewEntity> dal)
		{
			this._dal = dal;
			base.BaseDal = dal;
		}

		public async Task<List<TippingMlistViewEntity>> GetList(TippingMlistViewRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<TippingMlistViewEntity>()
			   .And(a => !string.IsNullOrEmpty(a.BatchId) && a.BatchId.Equals(reqModel.BatchId))
			   .AndIF(reqModel.SortOrder != null, a => a.SortOrder == reqModel.SortOrder)
			   .ToExpression();
			var data = await _dal.Db.Queryable<TippingMlistViewEntity>()
				.Where(whereExpression).OrderBy(x => x.SortOrder).OrderBy(x => x.FeedStates).ToListAsync();
			return data;
		}

		public async Task<PageModel<TippingMlistViewEntity>> GetPageList(TippingMlistViewRequestModel reqModel)
		{
			PageModel<TippingMlistViewEntity> result = new PageModel<TippingMlistViewEntity>();
			RefAsync<int> dataCount = 0;
			var whereExpression = Expressionable.Create<TippingMlistViewEntity>()
			   .And(a => !string.IsNullOrEmpty(a.BatchId) && a.BatchId.Equals(reqModel.BatchId))
			   .AndIF(reqModel.SortOrder != null, a => a.SortOrder == reqModel.SortOrder)
			   .ToExpression();
			var data = await _dal.Db.Queryable<TippingMlistViewEntity>()
				.Where(whereExpression).OrderBy(x => x.SortOrder).OrderBy(x => x.FeedStates)
				.ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
			result.dataCount = dataCount;
			result.data = data;
			return result;
		}

		public async Task<MessageModel<SortModel>> GetTippingStatus(TippingMlistViewRequestModel reqModel)
		{
			var result = new MessageModel<SortModel>
			{
				msg = "获取成功！",
				success = true,
				response = new SortModel() { SortOrder = -1, Status = -1 }
			};
			var poex = await _dal6.FindEntity(x => x.ID == reqModel.PoExecutionId && x.RunEquipmentId == reqModel.RunEquipmentId && x.BatchId == reqModel.BatchId && x.Status == "1");
			if (poex == null)
			{
				result.success = false;
				result.msg = "未找到启动的工单";
				return result;
			}
			var batch = await _dal2.FindEntity(reqModel.BatchId);
			if (batch == null)
			{
				result.success = false;
				result.msg = "未找到batch";
				return result;
			}
			int.TryParse(batch.PrepStatus, out int preStatus);
			if (preStatus < 7)
			{
				return result;
			}
			//获取Function
			MessageModel<EquipmentAllDataModel> apiResult_equipmentAllData = await HttpHelper.PostAsync<EquipmentAllDataModel>("DFM", "api/Equipment/GetEquipmentAllData?EquipmentId=" + reqModel.RunEquipmentId, _user.GetToken(), new { });
			var equipmentAllData = apiResult_equipmentAllData.response;
			if (equipmentAllData == null)
			{
				result.success = false;
				result.msg = "equipmentAllData为空,请检查MES配置";
				return result;
			}
			var item1 = equipmentAllData.EquipmentFunctionPropertyList?.Find(x => x.FunctionCode == "Tipping");
			var item2 = item1?.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == "MMITrigger");
			var item3 = item1?.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == "ButtonEnable");
			var v1 = item2?.ActualValue ?? item2?.DefaultValue;
			var v2 = item3?.ActualValue ?? item3?.DefaultValue;
			var list = (await _dal.FindList(x => x.BatchId == reqModel.BatchId))?.Select(x => x.SortOrder)?.Distinct()?.OrderBy(x => x)?.ToList();
			var containerHistories = await _dal3.FindList(x => x.BatchId == reqModel.BatchId && (x.Type == "Inventory tipping Start" || x.Type == "Inventory tipping Complate"));
			foreach (var item in list)
			{
				var sort = item.ToString();
				var containerHistories1 = containerHistories.FindAll(x => x.Comment == sort);
				if (containerHistories1 == null || containerHistories1.Count == 0)
				{
					result.response.SortOrder = item;
					result.response.Status = 0;
					if (v1 == "True" && v2 != "True")
					{
						result.response.Status = -1;
					}
					return result;
				}
				else if (containerHistories1.Exists(x => x.Type == "Inventory tipping Complate") && containerHistories1.Exists(x => x.Type == "Inventory tipping Start"))
				{
					continue;
				}
				else if (containerHistories1.Exists(x => x.Type == "Inventory tipping Start") && !containerHistories1.Exists(x => x.Type == "Inventory tipping Complate"))
				{
					var r = await GetTippingCount(new TippingSclistRequestModel()
					{
						BatchId = poex.BatchId,
						SortOrder = item
					});
					if (r.response != "0")
					{
						result.response.Status = 1;
					}
					else
					{
						result.response.Status = 2;
					}
					result.response.SortOrder = item;
					return result;
				}
			}
			return result;
		}

		public async Task<MessageModel<string>> GetTippingCount(TippingSclistRequestModel reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "获取成功！",
				success = true,
				response = "0"
			};
			try
			{
				var materialIds = (await _dal.FindList(x => x.BatchId == reqModel.BatchId && x.SortOrder == reqModel.SortOrder)).Select(x => x.MaterialId);
				if (materialIds == null)
				{
					materialIds = new List<string>();
				}
				var whereExpression = Expressionable.Create<TippingSclistEntity>()
					.And(x => materialIds.Contains(x.MaterialId))
				   .AndIF(!string.IsNullOrEmpty(reqModel.BatchId), a => a.BatchId.Equals(reqModel.BatchId))
				   .ToExpression();
				var data = await _dal.Db.Queryable<TippingSclistEntity>()
					.Where(whereExpression).ToListAsync();
				var allCount = data?.Count ?? 0;
				//var isTippingCount = data?.FindAll(x => x.Precheckestatus == "2")?.Count ?? 0;
				result.response = /*isTippingCount + "/" + */allCount.ToString();
			}
			catch (Exception ex)
			{
				result.success = false;
				result.msg = ex.Message;
			}
			return result;
		}

		public async Task<bool> SaveForm(TippingMlistViewEntity entity)
		{
			if (string.IsNullOrEmpty(entity.ID))
			{
				return await this.Add(entity) > 0;
			}
			else
			{
				return await this.Update(entity);
			}
		}

		/// <summary>
		/// 开始投料
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> StartTipping(TippingMlistViewModel reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			var poex = await _dal6.FindEntity(x => x.ID == reqModel.PoExecutionId && x.RunEquipmentId == reqModel.RunEquipmentId && x.BatchId == reqModel.BatchId && x.Status == "1");
			if (poex == null)
			{
				result.msg = "未找到启动的工单";
				return result;
			}
			BatchEntity entity = await _dal2.FindEntity(reqModel.BatchId);
			if (entity == null)
			{
				result.msg = "BatchId未找到";
				return result;
			}
			if (entity.PrepStatus == "9")
			{
				result.msg = "该批次已完成投料";
				return result;
			}
			//else if (entity.PrepStatus == "8")
			//{
			//	result.msg = "该批次已开始投料";
			//	return result;
			//}
			else if (entity.PrepStatus != "7" && entity.PrepStatus != "8")
			{
				result.msg = "该批次未完成预检查";
				return result;
			}
			var tippingMlists = await _dal.FindList(x => x.BatchId == reqModel.BatchId && x.SortOrder == reqModel.SortOrder);
			var materialIds = tippingMlists?.Select(x => x.MaterialId);
			if (materialIds == null)
			{
				materialIds = new List<string>();
			}
			var tippintEntitys = await _dal9.FindList(x => x.BatchId == reqModel.BatchId && materialIds.Contains(x.MaterialId) && x.Precheckestatus == "0");
			if (tippintEntitys?.Count > 0)
			{
				result.msg = "存在未完成预检查物料";
				return result;
			}
			var sort = reqModel.SortOrder.ToString();
			var containerHistory = await _dal3.FindEntity(x => x.Type == "Inventory tipping Complate" && x.Comment == sort && x.BatchId == reqModel.BatchId);
			if (containerHistory != null)
			{
				result.msg = $"第{sort}批料已完成投料";
				return result;
			}
			ContainerHistoryEntity containerHistoryEntity = new()
			{
				Type = "Inventory tipping Start",
				Comment = reqModel.SortOrder.ToString(),
				ProductionExecutionId = poex.ID,
				ProductOrderId = poex.ProductionOrderId,
				BatchId = reqModel.BatchId,
			};
			containerHistoryEntity.CreateCustomGuid(_user.Name);
			var addList = new List<CookConfirmationEntity>();
			var productionOrder = await _proOrderdal.FindEntity(poex.ProductionOrderId);
			var cookConfirmations = await _cookConfdal.FindList(x => x.OrderId == poex.ProductionOrderId);
			if (!cookConfirmations.Any())
			{
				CookConfirmationEntity cookConfirmationEntity = new()
				{
					Status2 = 0,
					OrderId = poex.ProductionOrderId,
					MaterialId = productionOrder?.MaterialId ?? "",
					StartTime = DateTime.Now,

				};
				cookConfirmationEntity.CreateCustomGuid(_user.Name);
				addList.Add(cookConfirmationEntity);
			}
			_unitOfWork.BeginTran();
			try
			{
				await _dal3.Add(containerHistoryEntity);
				if (entity.PrepStatus == "7")
				{
					//更新Batch表PrepStatus
					entity.PrepStatus = "8";//START_TIPPING
					entity.Modify(entity.ID, _user.Name);
					await _dal2.Update(entity);
				}
				if (addList.Count > 0)
				{
					await _cookConfdal.Add(addList);
				}
				_unitOfWork.CommitTran();
				result.msg = "操作成功！";
				result.success = true;
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				_unitOfWork.RollbackTran();
			}
			return result;
		}

		/// <summary>
		/// 投料完成
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> ComplateTipping(TippingMlistViewModel reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			var poex = await _dal6.FindEntity(x => x.ID == reqModel.PoExecutionId && x.RunEquipmentId == reqModel.RunEquipmentId && x.BatchId == reqModel.BatchId && x.Status == "1");
			if (poex == null)
			{
				result.msg = "未找到启动的工单";
				return result;
			}
			var lastOrder = (await _dal.FindList(x => x.BatchId == reqModel.BatchId))?.OrderByDescending(x => x.SortOrder)?.FirstOrDefault()?.SortOrder;
			var tippingMlists = await _dal.FindList(x => x.BatchId == reqModel.BatchId && x.SortOrder == reqModel.SortOrder);
			var materialIds = tippingMlists?.Select(x => x.MaterialId);
			if (materialIds == null)
			{
				materialIds = new List<string>();
			}
			if (reqModel.IsPDA == true)
			{
				//6300230004和水7300030001这两种料的（已消耗总数 / 批次计划消耗量）是否大于70 %
				var sugarAndWaters = await _dal30.FindList(x => (x.MaterialCode == "6300230004" || x.MaterialCode == "7300030001") && x.EquipmentId == reqModel.RunEquipmentId && x.BatchId == reqModel.BatchId && x.Quantity2 > 0);
				if (sugarAndWaters?.Count > 0 && sugarAndWaters.Exists(x => (x.Quantity1 / x.Quantity2) * 100m <= 70))
				{
					result.msgDev = "请前往“扫码消耗”界面继续消耗白糖和水！";
				}
			}
			if (!reqModel.IsForcedCompletion)
			{
				var tippingSclists = await _dal9.FindList(x => x.BatchId == reqModel.BatchId && materialIds.Contains(x.MaterialId));
				if (tippingSclists?.Count > 0)
				{
					result.msg = "存在未投物料！";
					return result;
				}
			}
			var sort = reqModel.SortOrder.ToString();
			var containerHistory = await _dal3.FindEntity(x => x.Type == "Inventory tipping Complate" && x.Comment == sort && x.BatchId == reqModel.BatchId);
			if (containerHistory != null)
			{
				result.msg = $"第{sort}批料已完成投料";
				return result;
			}
			BatchEntity entity = await _dal2.FindEntity(reqModel.BatchId);
			if (entity == null)
			{
				result.msg = "BatchId未找到";
				return result;
			}
			var equipment = await _dal12.FindEntity(poex.RunEquipmentId);
			if (equipment == null)
			{
				result.msg = "未找到equipment";
				return result;
			}
			ContainerHistoryEntity containerHistoryEntity = new()
			{
				Type = "Inventory tipping Complate",
				Comment = reqModel.SortOrder.ToString(),
				ProductionExecutionId = poex.ID,
				ProductOrderId = poex.ProductionOrderId,
				BatchId = reqModel.BatchId,
			};
			containerHistoryEntity.CreateCustomGuid(_user.Name);
			_unitOfWork.BeginTran();
			try
			{
				await _dal3.Add(containerHistoryEntity);
				if (entity.PrepStatus == "8" && lastOrder == reqModel.SortOrder)
				{
					//更新Batch表PrepStatus
					entity.PrepStatus = "9";//TIPPING_DONE
					entity.Modify(entity.ID, _user.Name);
					await _dal2.Update(entity);
				}
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				_unitOfWork.RollbackTran();
				return result;
			}
			try
			{
				await _iInterfaceServices.FeedingCompleted(poex.ID, equipment.EquipmentCode, 2, poex.ProductionOrderId, entity.Number);
			}
			catch (Exception ex)
			{
				result.msg = "投料完成同步MMI时出现错误";
				SerilogServer.LogDebug($"【投料完成】{result.msg}：{ex.StackTrace}", "MMIInterfaceLog");
				return result;
			}
			result.msg = "操作成功！";
			result.success = true;

			return result;
		}

		public async Task<MessageModel<PoProducedExecutionModel>> GetRunOrderFromSampleEquipment(string sampleEquipment)
		{
			var result = new MessageModel<PoProducedExecutionModel>
			{
				msg = "获取失败！",
				success = false,
			};
			var functionProperty = (await _funprdal.FindList(x => x.PropertyCode == "SampleEquipment")).FirstOrDefault();
			if (functionProperty == null)
			{
				result.msg = "未找到属性SampleEquipment";
				return result;
			}
			var functionPropertyValue = (await _funprvdal.FindList(x => x.PropertyId == functionProperty.ID && x.PropertyValue == sampleEquipment)).FirstOrDefault();
			if (functionPropertyValue == null)
			{
				result.msg = $"未找到设备铭牌为：{sampleEquipment}的设备";
				return result;
			}
			var equipment = await _dal12.FindEntity(functionPropertyValue.EquipmentId);
			if (equipment == null)
			{
				result.msg = $"未找到设备铭牌为：{sampleEquipment}的设备";
				return result;
			}
			var runOrder = await _dal6.FindEntity(x => x.RunEquipmentId == equipment.ID && x.Status == "1" && x.EndTime == null);
			if (runOrder == null)
			{
				result.msg = "当前设备未找到运行工单！";
				return result;
			}
			result.response = _mapper.Map<PoProducedExecutionModel>(runOrder);
			var bProductionOrder = (await _bproOrderdal.FindList(x => x.ID == runOrder.ProductionOrderId))?.FirstOrDefault();
			var batch = await _dal2.FindEntity(runOrder.BatchId);
			result.response.ProductionOrder = string.Format("{0}({1})", bProductionOrder?.ProductionOrderNo ?? "", batch?.Number ?? "");
			//result.response.Gc = bProductionOrder != null ? string.Format("{0}/{1}", bProductionOrder.Sequence, bProductionOrder.Count) : "";
			result.response.Formula = bProductionOrder?.Formula ?? "";
			result.success = true;
			result.msg = "获取成功！";
			return result;
		}

		/// <summary>
		/// 扫描库存
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> Tipping(TippingSclistRequestModel reqModel)
		{
			SerilogServer.LogDebug($"【Tipping】request:{FAJsonConvert.ToJson(reqModel)}", "PDALog");
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			if (string.IsNullOrEmpty(reqModel.BatchId))
			{
				result.msg = "BatchId为空！";
				return result;
			}
			if (string.IsNullOrEmpty(reqModel.Tracecode))
			{
				result.msg = "Tracecode为空！";
				return result;
			}

			string key = reqModel.BatchId+"|"+ reqModel.Tracecode;

			if (await _redisBasketRepository.Exist(key))
			{
				result.msg = $"存在未完成的任务，本次退出,key:{key}";
				SerilogServer.LogDebug(result.msg, "LockLog");
				return result;
			}

			try
			{
		        await _redisBasketRepository.Set(key, "1", new TimeSpan(0, 0, 3, 0));
			    var poex = await _dal6.FindEntity(x => x.ID == reqModel.PoExecutionId && x.RunEquipmentId == reqModel.RunEquipmentId && x.BatchId == reqModel.BatchId && x.Status == "1");
				if (poex == null)
				{
					result.msg = "未找到启动的工单";
					return result;
				}
				BatchEntity entity = await _dal2.FindEntity(reqModel.BatchId);
				bool updateBatch = false;
				if (entity == null)
				{
					result.msg = "BatchId未找到";
					return result;
				}
				var tippingEntity = await _dal9.FindEntity(x => x.BatchId == reqModel.BatchId && x.Tracecode == reqModel.Tracecode);
				if (tippingEntity == null)
				{
					result.msg = "该批次下未找到此TraceCode：" + reqModel.Tracecode;
					return result;
				}
				var statusModel = await GetTippingStatus(new TippingMlistViewRequestModel() { BatchId = reqModel.BatchId, RunEquipmentId = reqModel.RunEquipmentId, PoExecutionId = reqModel.PoExecutionId });
				if (!statusModel.success)
				{
					result.msg = statusModel.msg;
					return result;
				}
				if (statusModel.response.Status != 1)
				{
					result.msg = "当前状态不允许进行投料";
					return result;
				}
				var tippingMlists = (await _dal.FindList(x => x.BatchId == reqModel.BatchId && x.SortOrder == statusModel.response.SortOrder))?.Select(x => x.MaterialId) ?? new List<string>();
				if (!tippingMlists.Contains(tippingEntity.MaterialId))
				{
					result.msg = $"扫描物料不属于当前投料顺序（{statusModel.response.SortOrder}）";
					return result;
				}
				//写入consumed_actual表
				PoConsumeActualEntity pcae = null;
				//库存
				MaterialInventoryEntity inventoryModel = null;
				//需要清库存
				inventoryModel = await _dal4.FindEntity(tippingEntity.ID);
				if (inventoryModel == null)
				{
					result.msg = "未找到InventoryId";
					return result;
				}
				var lot = await _dal10.FindEntity(inventoryModel.LotId);
				if (lot == null)
				{
					result.msg = "未找到lot";
					return result;
				}
				//inventoryModel.Quantity = 0;
				var poconsumere = await _dal7.FindEntity(x => x.MaterialId == lot.MaterialId && x.PoSegmentRequirementId == entity.PoSegmentRequirementId);
				if (poconsumere == null)
				{
					result.msg = "未找到PoConsumeRequirementId";
					return result;
				}
				var subLotEntity = await _dal8.FindEntity(tippingEntity.SublotId);
				if (subLotEntity == null)
				{
					result.msg = "未找到SublotId";
					return result;
				}
				var batchConsume = await _dal13.FindEntity(x => x.BatchId == reqModel.BatchId && x.PoConsumeRequirementId == poconsumere.ID);
				if (batchConsume == null)
				{
					result.msg = "未找到batchConsume";
					return result;
				}
				int v = poconsumere.ChangeUnit?.ToLower() == "g" ? 1000 : 1;
				//写入consumed_actual表
				pcae = new PoConsumeActualEntity
				{
					ProductionOrderId = poex.ProductionOrderId,
					PoConsumeRequirementId = poconsumere.ID,
					ProductExecutionId = poex.ID,
					EquipmentId = poex.RunEquipmentId,
					SourceEquipmentId = inventoryModel.EquipmentId,
					Quantity = tippingEntity.Quantity * v,
					UnitId = poconsumere.UnitId,
					LotId = inventoryModel.LotId,
					SubLotId = inventoryModel.SublotId,
					SubLotStatus = int.Parse(subLotEntity.ExternalStatus),
					StorageBin = poconsumere.StorageBin,
					StorageLocation = poconsumere.StorageLocation,
					ContainerId = inventoryModel.ContainerId,
					TeamId = "",
					ShiftId = "",
					ReasonCode = "",
					Comment = "",
					Deleted = 0,
					SendExternal = poconsumere.Quantity == 0 ? 1 : 0
				};
				//当前批次未投料的
				var data = await _dal9.FindList(x => x.BatchId == reqModel.BatchId && x.SublotId != inventoryModel.SublotId && x.Precheckestatus != "2");
				//未投料
				var data1 = data.FindAll(x => x.ContainerHistoryId == tippingEntity.ContainerHistoryId);
				var tippingSclists_material = data.FindAll(x => x.MaterialId == poconsumere.MaterialId);

				//容器记录
				ContainerHistoryEntity hisModel = null;
				//容器
				ContainerEntity container = null;
				//更新容器状态
				if (data1.Count == 0)
				{
					hisModel = await _dal3.FindEntity(tippingEntity.ContainerHistoryId);
					if (hisModel == null)
					{
						result.msg = "未找到ContainerHistoryId";
						return result;
					}
					//更新容器状态
					container = await _dal14.FindEntity(hisModel.ContainerId);
					if (container == null)
					{
						result.msg = "未找到container";
						return result;
					}
					container.Status = "2";//empty
										   //hisModel.State = "2";//empty
				}

				ContainerHistoryEntity containerHistoryEntity = new()
				{
					Type = "Inventory tipping",
					Comment = "Inventory tipping Done",
					ContainerId = inventoryModel.ContainerId,
					//hisModel.EquipmentRequirementId = inventoryModel.EquipmentRequirementId;
					//hisModel.MaterialId = inventoryModel.MaterialId;
					SublotId = inventoryModel.SublotId,
					Quantity = inventoryModel.Quantity.ToString(),
					QuantityUomId = inventoryModel.QuantityUomId,
					LotId = inventoryModel.LotId
				};
				containerHistoryEntity.CreateCustomGuid(_user.Name);
				//更新批次状态
				//if (data.Count == 0)
				//{
				//	updateBatch = true;
				//	//更新Batch表PrepStatus
				//	entity.PrepStatus = "9";//TIPPING_DONE
				//}
				_unitOfWork.BeginTran();
				if (updateBatch)
				{
					entity.Modify(entity.ID, _user.Name);
					await _dal2.Update(entity);
				}
				if (pcae != null)
				{
					pcae.CreateCustomGuid(_user.Name);
					await _dal5.Add(pcae);
				}
				if (inventoryModel != null)
				{
					await _dal4.Delete(inventoryModel);
				}
				if (container != null)
				{
					container.Modify(container.ID, _user.Name);
					await _dal14.Update(container);
				}
				await _dal3.Add(containerHistoryEntity);
				if (tippingSclists_material == null || tippingSclists_material.Count == 0)
				{
					batchConsume.FeedStates = 2;
					batchConsume.Modify(batchConsume.ID, _user.Name);
					await _dal13.Update(batchConsume);
				}
				else if (batchConsume.FeedStates == 0)
				{
					batchConsume.FeedStates = 1;
					batchConsume.Modify(batchConsume.ID, _user.Name);
					await _dal13.Update(batchConsume);
				}
				_unitOfWork.CommitTran();
				result.msg = "操作成功！";
				result.success = true;
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				_unitOfWork.RollbackTran();
			}
			finally
			{
				await _redisBasketRepository.Remove(key);
			}
			return result;
		}

		/// <summary>
		/// 获取设备Function属性值
		/// </summary>
		/// <param name="equipmentId"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> GetFunctionPropertyValue2(string equipmentId, string functionCode, string propertyName)
		{
			var result = new MessageModel<string>
			{
				msg = "获取失败！",
				success = false
			};
			//获取Function
			MessageModel<List<FunctionEntity>> apiResult_functionEntities = await HttpHelper.PostAsync<List<FunctionEntity>>("DFM", "api/Function/GetList", _user.GetToken(), new { });
			var functionEntities = apiResult_functionEntities.response;
			if (functionEntities == null || functionEntities.Count == 0)
			{
				result.msg = "functionEntities为空";
				return result;
			}
			var tippingFunction = functionEntities.Find(x => x.FunctionCode == functionCode);
			if (tippingFunction == null)
			{
				result.msg = "tippingFunction为空";
				return result;
			}

			//获取EquipmentFunctions
			MessageModel<List<EquipmentFunctionEntity>> apiResult_equipmentFunctions = await HttpHelper.PostAsync<List<EquipmentFunctionEntity>>("DFM", "api/EquipmentFunction/GetList", _user.GetToken(), new { });
			var quipmentFunction = apiResult_equipmentFunctions.response?.Find(x => x.EquipmentId == equipmentId && x.FunctionId == tippingFunction.ID);
			if (quipmentFunction == null)
			{
				result.msg = "未找到quipmentFunction";
				return result;
			}

			//获取TippingFunctionProperties
			MessageModel<List<FunctionPropertyEntity>> apiResult_functionPropertyEntities = await HttpHelper.PostAsync<List<FunctionPropertyEntity>>("DFM", "api/FunctionProperty/GetList", _user.GetToken(), new { });
			var functionPropertyEntity = apiResult_functionPropertyEntities.response?.Find(x => x.FunctionId == tippingFunction.ID && x.PropertyName == propertyName);
			if (functionPropertyEntity == null)
			{
				result.msg = "未找到functionPropertyEntity";
				return result;
			}
			result.response = functionPropertyEntity.DefaultValue;

			//获取TippingFunctionProperties
			MessageModel<List<FunctionPropertyValueEntity>> apiResult_functionPropertyValueEntities = await HttpHelper.PostAsync<List<FunctionPropertyValueEntity>>("DFM", "api/FunctionProperty/GetList", _user.GetToken(), new { });
			var functionPropertyValueEntity = apiResult_functionPropertyValueEntities.response?.Find(x => x.EquipmentFunctionId == quipmentFunction.ID && x.PropertyId == functionPropertyEntity.ID);
			if (functionPropertyEntity != null)
			{
				result.response = functionPropertyValueEntity.PropertyValue;
			}

			result.msg = "获取成功！";
			result.success = true;
			return result;
		}

		/// <summary>
		/// 获取设备Function属性值
		/// </summary>
		/// <param name="equipmentId"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> GetFunctionPropertyValue(string equipmentId, string functionCode, string propertyName)
		{
			var result = new MessageModel<string>
			{
				msg = "获取失败！",
				success = false
			};
			//获取Function
			MessageModel<EquipmentAllDataModel> apiResult_equipmentAllData = await HttpHelper.PostAsync<EquipmentAllDataModel>("DFM", "api/Equipment/GetEquipmentAllData?EquipmentId=" + equipmentId, _user.GetToken(), new { });
			var equipmentAllData = apiResult_equipmentAllData.response;
			if (equipmentAllData == null)
			{
				result.msg = "equipmentAllData为空";
				return result;
			}
			var item = equipmentAllData.EquipmentFunctionPropertyList?.Find(x => x.FunctionCode == functionCode);
			var item2 = item?.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == propertyName);
			result.response = item2?.ActualValue ?? item2?.DefaultValue;
			result.msg = "获取成功！";
			result.success = true;
			return result;
		}

		/// <summary>
		/// 获取TippingTransfer下拉选
		/// </summary>
		/// <param name="equipmentId"></param>
		/// <returns></returns>
		public async Task<MessageModel<List<DestinationSelect>>> GetTippingTransferSelectList(string equipmentId)
		{
			var result = new MessageModel<List<DestinationSelect>>
			{
				msg = "获取成功！",
				success = true,
				response = new List<DestinationSelect>()
			};
			//return result;  
			//获取EquipmentAction
			MessageModel<List<EquipmentActionModel>> apiResult_equipmentActionModels = await HttpHelper.PostAsync<List<EquipmentActionModel>>("DFM", "api/EquipmentAction/GetEquipmentAction", _user.GetToken(), new { EquipmentId = equipmentId });
			var equipmentActionModels = apiResult_equipmentActionModels.response;
			if (equipmentActionModels == null || equipmentActionModels.Count == 0)
			{
				//result.msg = "equipmentActionModels为空";
				return result;
			}
			var actions = equipmentActionModels.FindAll(x => x.ActionCode == "Transfer");
			//获取Equipments
			MessageModel<List<DFM.Model.Models.EquipmentEntity>> apiResult_Equipments = await HttpHelper.PostAsync<List<DFM.Model.Models.EquipmentEntity>>("DFM", "api/Equipment/GetList", _user.GetToken(), new { });
			var equipments = apiResult_Equipments.response;
			if (equipments == null || equipments.Count == 0)
			{
				//result.msg = "equipments为空";
				return result;
			}
			foreach (var action in actions)
			{
				//获取EquipmentActionProperty
				MessageModel<List<EquipmentActionPropertyModel>> apiResult_equipmentActionProperties = await HttpHelper.PostAsync<List<EquipmentActionPropertyModel>>("DFM", "api/ActionPropertyValue/GetEquipmentActionProperty", _user.GetToken(), new { EquipmentId = equipmentId, EquipmentActionId = action.EquipmentActionId });
				var equipmentActionProperties = apiResult_equipmentActionProperties.response;
				if (equipmentActionProperties == null || equipmentActionProperties.Count == 0)
				{
					continue;
					//result.msg = "equipmentActionProperties为空";
					//return result;
				}
				var associatedNode = equipmentActionProperties.Find(x => x.PropertyCode == "Associated Node");
				if (associatedNode != null)
				{
					DestinationSelect select = new();
					var v = associatedNode.ActualValue ?? associatedNode.DefaultValue;
					if (v.IsNullOrEmpty())
					{
						continue;
					}
					var equipment = equipments.Find(x => x.EquipmentCode == v);
					if (equipment == null)
					{
						continue;
					}
					select.key = equipment?.ID;
					select.value = equipment?.EquipmentName;
					select.isSelect = false;
					select.equipmentId = equipmentId;
					select.equipmentActionId = action.EquipmentActionId;
					if (!result.response.Exists(x => x.isSelect == true))
					{
						var isDefault = equipmentActionProperties.Find(x => x.PropertyCode == "Is Default");
						var v2 = isDefault.ActualValue ?? isDefault.DefaultValue;
						if (v2 == "1")
						{
							select.isSelect = true;
						}
					}
					result.response.Add(select);
				}
			}
			result.msg = "获取成功！";
			result.success = true;
			return result;
		}

		/// <summary>
		/// 获取选中的
		/// </summary>
		/// <param name="equipmentId"></param>
		/// <returns></returns>
		public async Task<MessageModel<DestinationSelect>> GetTippingTransferSelect(string equipmentId)
		{
			var result = new MessageModel<DestinationSelect>
			{
				msg = "获取失败！",
				success = false,
			};
			var message = await GetTippingTransferSelectList(equipmentId);
			if (message.response != null || message.response.Count > 0)
			{
				result.response = message.response.Find(x => x.isSelect == true);
			}
			result.msg = "获取成功！";
			result.success = true;
			return result;
		}

		/// <summary>
		/// 选中后保存
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> SaveSelect(DestinationSelect reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "保存失败！",
				success = false,
			};
			//获取EquipmentAction
			MessageModel<List<EquipmentActionModel>> apiResult_equipmentActionModels = await HttpHelper.PostAsync<List<EquipmentActionModel>>("DFM", "api/EquipmentAction/GetEquipmentAction", _user.GetToken(), new { EquipmentId = reqModel.equipmentId });
			var equipmentActionModels = apiResult_equipmentActionModels.response;
			if (equipmentActionModels == null || equipmentActionModels.Count == 0)
			{
				result.msg = "equipmentActionModels为空";
				return result;
			}
			var actions = equipmentActionModels.FindAll(x => x.ActionCode == "Transfer");
			foreach (var action in actions)
			{
				var saveActionPerValues = new List<EquipmentActionPropertyModel>();
				//获取EquipmentActionProperty
				MessageModel<List<EquipmentActionPropertyModel>> apiResult_equipmentActionProperties = await HttpHelper.PostAsync<List<EquipmentActionPropertyModel>>("DFM", "api/ActionPropertyValue/GetEquipmentActionProperty", _user.GetToken(), new { EquipmentId = reqModel.equipmentId, EquipmentActionId = action.EquipmentActionId });
				var equipmentActionProperties = apiResult_equipmentActionProperties.response;
				if (equipmentActionProperties == null || equipmentActionProperties.Count == 0)
				{
					continue;
					//result.msg = "equipmentActionProperties为空";
					//return result;
				}
				var isDefault = equipmentActionProperties.Find(x => x.PropertyCode == "Is Default");
				if (isDefault != null)
				{
					if (action.EquipmentActionId == reqModel.equipmentActionId)
					{
						isDefault.ActualValue = "1";
					}
					else
					{
						isDefault.ActualValue = "0";
					}
					saveActionPerValues.Add(isDefault);
				}
				var associatedNode = equipmentActionProperties.Find(x => x.PropertyCode == "Associated Node");
				if (associatedNode != null)
				{
					saveActionPerValues.Add(associatedNode);
				}
				//获取EquipmentActionProperty
				MessageModel<string> apiResult_saveEquipmentActionProperty = await HttpHelper.PostAsync<string>("DFM", "api/ActionPropertyValue/SaveEquipmentActionProperty", _user.GetToken(), saveActionPerValues);
				if (apiResult_saveEquipmentActionProperty.success != true)
				{
					return apiResult_saveEquipmentActionProperty;
				}
			}
			result.msg = "保存成功！";
			result.success = true;
			return result;
		}

		/// <summary>
		/// 检查TippingType
		/// </summary>
		/// <param name="equipmentId"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> CheckTippingType(string equipmentId)
		{
			var result = new MessageModel<string>
			{
				msg = "Is Not TransferContainer！",
				success = true,
				response = "false"
			};
			var value = (await GetFunctionPropertyValue(equipmentId, "Tipping", "TippingType")).response;
			if (value == "TransferContainer")
			{
				result.msg = "Is TransferContainer！";
				result.success = true;
				result.response = "true";
			}
			return result;
		}

		/// <summary>
		/// 扫描库存
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> DockScan(DockScanModel reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			var inventorylistingView = await _dal11.FindEntity(x => x.Sscc == reqModel.TraceCode);
			if (inventorylistingView == null)
			{
				result.msg = "未找到子批次" + reqModel.TraceCode;
				return result;
			}
			if (inventorylistingView.EquipmentId == reqModel.DestinationId)
			{
				result.msg = "目的地与源地点相同，无需转移";
				return result;
			}
			//if (inventorylistingView.StatusF == "1" || inventorylistingView.StatusS == "1")
			//{
			//	result.msg = "库存状态为锁定，不能进行转移";
			//	return result;
			//}
			MessageModel<List<EquipmentInterLockModel>> apiResult_eils = await HttpHelper.PostAsync<List<EquipmentInterLockModel>>("DFM", "api/EquipmentInterlock/GetEquipmentInterLock", _user.GetToken(), new { EquipmentActionId = reqModel.EquipmentActionId });
			var equipmentInterLocks = apiResult_eils.response?.FindAll(x => x.ActualStatus == "1");
			var actionProperty = await _dal29.FindEntity(x => x.EquipmentId == reqModel.DestinationId && x.ActionCode == "Transfer" && x.PropertyCode == "InterlockEquipment");
			//if (actionProperty == null)
			//{
			//	result.msg = "未找到InterlockEquipment，请检查配置";
			//	return result;
			//}
			if (actionProperty != null)
			{
				var value = !string.IsNullOrEmpty(actionProperty.PropertyValue) ? actionProperty.PropertyValue : actionProperty.DefaultValue;
				//if (!string.IsNullOrEmpty(value))
				//{
				var equipment = await _dal12.FindEntity(x => x.EquipmentCode == value);
				if (equipment == null)
				{
					result.msg = "未找到equipment，请检查配置";
					return result;
				}
				var runOrder = (await _dal6.FindList(x => x.RunEquipmentId == equipment.ID && x.Status == "1" && x.EndTime == null, x => x.StartTime, false))?.FirstOrDefault();
				foreach (var item in equipmentInterLocks)
				{
					switch (item.InterLockCode)
					{
						case "ActiveOrder":
							if (runOrder == null)
							{
								result.msg = $"ActiveOrder:目的地设备未运行工单";
								return result;
							}
							break;
						case "InventoryBlocked":
							var status = await _iInventorylistingViewServices.GetStateByInventID(new string[] { inventorylistingView.InventoryId });
							if (!string.IsNullOrEmpty(status))
							{
								result.msg = $"InventoryBlocked:{status}";
								return result;
							}
							break;
						case "MaterialInBom":
							if (runOrder == null)
							{
								result.msg = $"MaterialInBom:目的地设备未运行工单";
								return result;
							}
							var poConsumeRequirements = await _dal7.FindList(x => x.ProductionOrderId == runOrder.ProductionOrderId && x.MaterialId == inventorylistingView.MaterialId);
							if (!poConsumeRequirements.Any())
							{
								result.msg = $"MaterialInBom:目的地设备运行工单不存在该物料";
								return result;
							}
							break;
						default:
							break;
					}
				}
				//}
			}
			try
			{
				bool flag = await _containerServices.TransferContainer(new string[] { inventorylistingView.InventoryId }, reqModel.DestinationId, reqModel.DestinationName, "上料-转移");
				if (!flag)
				{
					return result;
				}
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				return result;
			}
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		public class EquipmentAllDataModel
		{
			public DFM.Model.Models.EquipmentEntity EquipmentEntity { get; set; }

			public List<EquipmentFunctionModel> EquipmentFunctionList { get; set; }

			public List<EquFunctionPropertyModel> EquipmentFunctionPropertyList { get; set; }

			public List<EquipmentActionModel> EquipmentActionList { get; set; }

			public List<EquipmentActionPropertyModel> EquipmentActionPropertyList { get; set; }

			public List<EquipmentInterLockModel> EquipmentInterLockList { get; set; }
		}
	}
}