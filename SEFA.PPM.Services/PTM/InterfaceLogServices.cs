using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.IServices.PTM;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels.PTM;
using Abp.Domain.Entities;
using MongoDB.Driver.Core.Clusters;
using Remotion.Linq.Clauses;
using System.Xml.Linq;
using System.Linq;

namespace SEFA.PPM.Services.PTM
{
	public class InterfaceLogServices : BaseServices<InterfaceLogEntity>, IInterfaceLogServices
	{
		private readonly IBaseRepository<InterfaceLogEntity> _dal;
		public InterfaceLogServices(IBaseRepository<InterfaceLogEntity> dal)
		{
			_dal = dal;
			BaseDal = dal;
		}

		public async Task<List<InterfaceLogEntity>> GetList(InterfaceLogRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<InterfaceLogEntity>()
							 .AndIF(reqModel.StartTime.HasValue, a => a.CreateDate >= reqModel.StartTime)
							 .AndIF(reqModel.EndTime.HasValue, a => a.CreateDate <= reqModel.EndTime)
							 .AndIF(!string.IsNullOrEmpty(reqModel.OrderNo), a => a.OrderNo != null && a.OrderNo.Contains(reqModel.OrderNo))
							 .AndIF(!string.IsNullOrEmpty(reqModel.BatchNo), a => a.BatchNo != null && a.BatchNo.Contains(reqModel.BatchNo))
							 .AndIF(!string.IsNullOrEmpty(reqModel.Name), a => a.Name != null && a.Name.Contains(reqModel.Name))
							 .AndIF(!string.IsNullOrEmpty(reqModel.Description), a => a.Description != null && a.Description.Contains(reqModel.Description))
							 .AndIF(!string.IsNullOrEmpty(reqModel.Content), a => a.Content != null && a.Content.Contains(reqModel.Content))
							 .AndIF(!string.IsNullOrEmpty(reqModel.Detail), a => a.Detail != null && a.Detail.Contains(reqModel.Detail))
							 .AndIF(!string.IsNullOrEmpty(reqModel.Search),
							a =>
							(a.OrderNo != null && a.OrderNo.Contains(reqModel.Search)) ||
							(a.BatchNo != null && a.BatchNo.Contains(reqModel.Search)) ||
							(a.Name != null && a.Name.Contains(reqModel.Search)) ||
							(a.Description != null && a.Description.Contains(reqModel.Search)) ||
							(a.Content != null && a.Content.Contains(reqModel.Search)) ||
							(a.Detail != null && a.Detail.Contains(reqModel.Search))
							 ).ToExpression();
			var data = await _dal.FindList(whereExpression);
			return data;
		}

		public async Task<PageModel<InterfaceLogEntity>> GetPageList(InterfaceLogRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<InterfaceLogEntity>()
							 .AndIF(reqModel.StartTime.HasValue, a => a.CreateDate >= reqModel.StartTime)
							 .AndIF(reqModel.EndTime.HasValue, a => a.CreateDate <= reqModel.EndTime)
							 .AndIF(!string.IsNullOrEmpty(reqModel.OrderNo), a => a.OrderNo != null && a.OrderNo.Contains(reqModel.OrderNo))
							 .AndIF(!string.IsNullOrEmpty(reqModel.BatchNo), a => a.BatchNo != null && a.BatchNo.Contains(reqModel.BatchNo))
							 .AndIF(!string.IsNullOrEmpty(reqModel.Name), a => a.Name != null && a.Name.Contains(reqModel.Name))
							 .AndIF(!string.IsNullOrEmpty(reqModel.Description), a => a.Description != null && a.Description.Contains(reqModel.Description))
							 .AndIF(!string.IsNullOrEmpty(reqModel.Content), a => a.Content != null && a.Content.Contains(reqModel.Content))
							 .AndIF(!string.IsNullOrEmpty(reqModel.Detail), a => a.Detail != null && a.Detail.Contains(reqModel.Detail))
							 .AndIF(!string.IsNullOrEmpty(reqModel.Search),
							a =>
							(a.OrderNo != null && a.OrderNo.Contains(reqModel.Search)) ||
							(a.BatchNo != null && a.BatchNo.Contains(reqModel.Search)) ||
							(a.Name != null && a.Name.Contains(reqModel.Search)) ||
							(a.Description != null && a.Description.Contains(reqModel.Search)) ||
							(a.Content != null && a.Content.Contains(reqModel.Search)) ||
							(a.Detail != null && a.Detail.Contains(reqModel.Search))
							 ).ToExpression();
			var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);

			return data;
		}

		public async Task<bool> SaveForm(InterfaceLogEntity entity)
		{
			if (string.IsNullOrEmpty(entity.ID))
			{
				return await Add(entity) > 0;
			}
			else
			{
				return await Update(entity);
			}
		}

		/// <summary>
		/// 写入日志
		/// </summary>
		/// <param name="executionId">工单执行ID</param>
		/// <param name="orderNo">工单号</param>
		/// <param name="batchNo">批号</param>
		/// <param name="name">接口名</param>
		/// <param name="description">接口描述</param>
		/// <param name="content">日志内容</param>
		/// <param name="detail">明细</param>
		/// <returns></returns>
		public async Task<bool> AddInterFaceLog(string executionId, string orderNo, string batchNo, string name, string description, string content, string detail)
		{
			InterfaceLogEntity entity = new InterfaceLogEntity()
			{
				ExecutionId = executionId,
				OrderNo = orderNo,
				BatchNo = batchNo,
				Name = name,
				Description = description,
				Content = content,
				Detail = detail
			};
			entity.CreateCustomGuid("MMI");
			return await Add(entity) > 0;
		}

		public async Task<MessageModel<InterfaceLogEntity>> GetLastLog(InterfaceLogRequestModel reqModel)
		{
			var result = new MessageModel<InterfaceLogEntity>
			{
				success = true,
				msg = "获取成功！"
			};
			var data = (await _dal.FindList(x => x.ExecutionId == reqModel.ExecutionId && x.Name == reqModel.Name, x => x.CreateDate, false))?.FirstOrDefault();
			result.response = data;
			return result;
		}
	}
}