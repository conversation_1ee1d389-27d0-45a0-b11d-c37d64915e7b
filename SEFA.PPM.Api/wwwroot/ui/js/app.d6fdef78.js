(function(e){function t(t){for(var a,o,s=t[0],c=t[1],l=t[2],u=0,d=[];u<s.length;u++)o=s[u],r[o]&&d.push(r[o][0]),r[o]=0;for(a in c)Object.prototype.hasOwnProperty.call(c,a)&&(e[a]=c[a]);p&&p(t);while(d.length)d.shift()();return i.push.apply(i,l||[]),n()}function n(){for(var e,t=0;t<i.length;t++){for(var n=i[t],a=!0,o=1;o<n.length;o++){var s=n[o];0!==r[s]&&(a=!1)}a&&(i.splice(t--,1),e=c(c.s=n[0]))}return e}var a={},o={app:0},r={app:0},i=[];function s(e){return c.p+"js/"+({}[e]||e)+"."+{"chunk-23e41f57":"ae5fabaa","chunk-276b085c":"39103cdf","chunk-2d0a4854":"aee50383","chunk-2d0c0c66":"4faa5607","chunk-2d0c4aa3":"703d6172","chunk-2d0cf4f3":"4034e115","chunk-2d0d2f25":"359b78e4","chunk-2d0da5bf":"c22ad0ee","chunk-2d213196":"5c2d76ce","chunk-2d21f214":"ec5ee5a8","chunk-2d229214":"cfe33fe9","chunk-2d22d746":"bc86ccfd","chunk-40df6ae2":"e79ba86d","chunk-47211100":"16761898","chunk-479d738e":"57cdb42a","chunk-47dd42da":"97513c3e","chunk-4b6066be":"f63d0f19","chunk-6e83591c":"a520c082","chunk-7287e918":"7e428c29","chunk-ef28925c":"a547d73e","chunk-6f1c3bea":"5a9acc22","chunk-735deb8e":"2bbe62a6","chunk-770e833a":"0890b50d","chunk-77279526":"e54ae03e","chunk-789b0e7e":"df774071","chunk-bf843d8a":"ed731235","chunk-c5ac0cca":"605768e7","chunk-c673e236":"156eaf15","chunk-c75b8e6e":"73103030","chunk-cae4df82":"7e75b1da","chunk-d726e0f8":"c8fe5894"}[e]+".js"}function c(t){if(a[t])return a[t].exports;var n=a[t]={i:t,l:!1,exports:{}};return e[t].call(n.exports,n,n.exports,c),n.l=!0,n.exports}c.e=function(e){var t=[],n={"chunk-47211100":1,"chunk-4b6066be":1,"chunk-7287e918":1,"chunk-ef28925c":1,"chunk-789b0e7e":1,"chunk-c673e236":1,"chunk-c75b8e6e":1,"chunk-d726e0f8":1};o[e]?t.push(o[e]):0!==o[e]&&n[e]&&t.push(o[e]=new Promise(function(t,n){for(var a="css/"+({}[e]||e)+"."+{"chunk-23e41f57":"31d6cfe0","chunk-276b085c":"31d6cfe0","chunk-2d0a4854":"31d6cfe0","chunk-2d0c0c66":"31d6cfe0","chunk-2d0c4aa3":"31d6cfe0","chunk-2d0cf4f3":"31d6cfe0","chunk-2d0d2f25":"31d6cfe0","chunk-2d0da5bf":"31d6cfe0","chunk-2d213196":"31d6cfe0","chunk-2d21f214":"31d6cfe0","chunk-2d229214":"31d6cfe0","chunk-2d22d746":"31d6cfe0","chunk-40df6ae2":"31d6cfe0","chunk-47211100":"6b9a8428","chunk-479d738e":"31d6cfe0","chunk-47dd42da":"31d6cfe0","chunk-4b6066be":"57cc0d2f","chunk-6e83591c":"31d6cfe0","chunk-7287e918":"735a054c","chunk-ef28925c":"f5aa9d10","chunk-6f1c3bea":"31d6cfe0","chunk-735deb8e":"31d6cfe0","chunk-770e833a":"31d6cfe0","chunk-77279526":"31d6cfe0","chunk-789b0e7e":"2afc78bb","chunk-bf843d8a":"31d6cfe0","chunk-c5ac0cca":"31d6cfe0","chunk-c673e236":"597cf4d0","chunk-c75b8e6e":"40b63f23","chunk-cae4df82":"31d6cfe0","chunk-d726e0f8":"ac918284"}[e]+".css",r=c.p+a,i=document.getElementsByTagName("link"),s=0;s<i.length;s++){var l=i[s],u=l.getAttribute("data-href")||l.getAttribute("href");if("stylesheet"===l.rel&&(u===a||u===r))return t()}var d=document.getElementsByTagName("style");for(s=0;s<d.length;s++){l=d[s],u=l.getAttribute("data-href");if(u===a||u===r)return t()}var p=document.createElement("link");p.rel="stylesheet",p.type="text/css",p.onload=t,p.onerror=function(t){var a=t&&t.target&&t.target.src||r,i=new Error("Loading CSS chunk "+e+" failed.\n("+a+")");i.request=a,delete o[e],p.parentNode.removeChild(p),n(i)},p.href=r;var f=document.getElementsByTagName("head")[0];f.appendChild(p)}).then(function(){o[e]=0}));var a=r[e];if(0!==a)if(a)t.push(a[2]);else{var i=new Promise(function(t,n){a=r[e]=[t,n]});t.push(a[2]=i);var l,u=document.createElement("script");u.charset="utf-8",u.timeout=120,c.nc&&u.setAttribute("nonce",c.nc),u.src=s(e),l=function(t){u.onerror=u.onload=null,clearTimeout(d);var n=r[e];if(0!==n){if(n){var a=t&&("load"===t.type?"missing":t.type),o=t&&t.target&&t.target.src,i=new Error("Loading chunk "+e+" failed.\n("+a+": "+o+")");i.type=a,i.request=o,n[1](i)}r[e]=void 0}};var d=setTimeout(function(){l({type:"timeout",target:u})},12e4);u.onerror=u.onload=l,document.head.appendChild(u)}return Promise.all(t)},c.m=e,c.c=a,c.d=function(e,t,n){c.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},c.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},c.t=function(e,t){if(1&t&&(e=c(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(c.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)c.d(n,a,function(t){return e[t]}.bind(null,a));return n},c.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return c.d(t,"a",t),t},c.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},c.p="/ui/",c.oe=function(e){throw console.error(e),e};var l=window["webpackJsonp"]=window["webpackJsonp"]||[],u=l.push.bind(l);l.push=t,l=l.slice();for(var d=0;d<l.length;d++)t(l[d]);var p=u;i.push([0,"chunk-vendors"]),n()})({0:function(e,t,n){e.exports=n("56d7")},"034f":function(e,t,n){"use strict";var a=n("64a9"),o=n.n(a);o.a},"07bb":function(e,t,n){},"0ccd":function(e,t,n){},"199c":function(e,t,n){"use strict";(function(e){var a=n("5d73"),o=n.n(a),r=(n("96cf"),n("3b8d")),i=n("f499"),s=n.n(i),c=(n("a481"),n("5ea5")),l=n("d38f"),u=n("4ec3"),d=n("3180"),p=n("3b00");t["a"]={components:{Sidebar:c["a"],ScrollPane:l["a"]},mixins:[p["a"]],data:function(){return{sysName:"BlogAdmin",sysNameShort:"BD",NewsVisible:!1,collapsed:!1,zModalShadow:!1,SidebarVisible:!1,collapsedClass:"menu-expanded",sysUserName:"",newsDialogCss:"news-dialog",sysUserAvatar:"",tagsList:[],form:{name:"",region:"",date1:"",date2:"",delivery:!1,type:[],resource:"",desc:""},routes:[],tagNews:[{name:"前后端分离",type:""},{name:"vue.js",type:""},{name:"DDD领域驱动设计",type:"success"},{name:"标签三",type:"info"},{name:"欠费警告！",type:"warning"},{name:"异常报告！",type:"danger"}],visible:!1,top:0,left:0,selectedTag:{},affixTags:[]}},methods:{gotappath:function(e){console.log(e),this.$router.replace({path:e})},handleOpen:function(e,t){console.log(e,t)},toindex:function(){this.$router.replace({path:"/"})},handleClose:function(e,t){console.log(e,t)},onSubmit:function(){console.log("submit!")},myNews:function(){this.newsDialogCss+=" show ",this.NewsVisible=!0},handleopen:function(){},handleclose:function(){},handleselect:function(e,t){},logout:function(){var t=this,n=this;this.$confirm("确认退出吗?","提示",{}).then(function(){window.localStorage.removeItem("user"),window.localStorage.removeItem("Token"),window.localStorage.removeItem("TokenExpire"),window.localStorage.removeItem("NavigationBar"),window.localStorage.removeItem("refreshtime"),window.localStorage.removeItem("router"),sessionStorage.removeItem("Tags"),e.antRouter=[],t.tagsList=[],t.routes=[],t.$store.commit("saveTagsData",""),e.IS_IDS4?d["a"].logout():n.$router.push("/login")}).catch(function(){})},Setting:function(){var e=this;e.$router.push("/System/My")},goGithub:function(){window.open("https://github.com/anjoy8/Blog.Admin")},collapse:function(){this.collapsed=!this.collapsed,this.collapsed?(this.collapsedClass="menu-collapsed",this.SidebarVisible=!1):(this.collapsedClass="menu-expanded",window.screen.width<680&&(this.SidebarVisible=!0)),window.localStorage.collapse=this.collapsed},showMenu:function(e,t){this.$refs.menuCollapsed.getElementsByClassName("submenu-hook-"+e)[0].style.display=t?"block":"none"},isActive:function(e){return e===this.$route.fullPath},closeTags:function(e){var t=this.tagsList.splice(e,1)[0],n=this.tagsList[e]?this.tagsList[e]:this.tagsList[e-1];n?(t.path===this.$route.fullPath&&this.$router.push(n.path),this.$store.commit("saveTagsData",s()(this.tagsList))):this.$router.push("/")},closeAll:function(){this.tagsList=[],this.$router.push("/"),sessionStorage.removeItem("Tags")},closeOther:function(){var e=this,t=this.tagsList.filter(function(t){return t.path===e.$route.fullPath});this.tagsList=t,sessionStorage.setItem("Tags",s()(this.tagsList))},setTags:function(e){if(!e.meta.NoTabPage){var t=this.tagsList.some(function(t){return t.path===e.fullPath});!t&&this.tagsList.push({title:e.meta.title,path:e.fullPath})}},handleTags:function(e){"other"===e?this.closeOther():this.closeAll()},getUserInfoByToken:function(e){var t=this,n=this,a={token:e};Object(u["Q"])(a).then(function(e){t.logining=!1,e.success?(n.$notify({type:"success",message:"欢迎管理员：".concat(e.response.uRealName," ！"),duration:3e3}),n.sysUserName=e.response.uRealName,window.localStorage.user=s()(e.response)):n.$message({message:e.message,type:"error"})})}},mounted:function(){console.log(this.$route);var e=sessionStorage.getItem("Tags")?JSON.parse(sessionStorage.getItem("Tags")):[];e&&e.length>0&&(this.tagsList=e);var t=JSON.parse(window.localStorage.router?window.localStorage.router:null);this.routes.length<=0&&t&&t.length>=0&&(this.routes=t)},updated:function(){var e=JSON.parse(window.localStorage.user?window.localStorage.user:null);e&&(this.sysUserName=e.uRealName||"老张的哲学",this.sysUserAvatar=e.avatar||"../assets/user.png");var t=JSON.parse(window.localStorage.router?window.localStorage.router:null);t&&t.length>=0&&(this.routes.length<=0||s()(this.routes)!=s()(t))&&(this.routes=t)},computed:{showTags:function(){return this.tagsList.length>1&&this.$store.commit("saveTagsData",s()(this.tagsList)),this.tagsList.length>0}},watch:{$route:function(){var t=Object(r["a"])(regeneratorRuntime.mark(function t(n,a){var r,i=this;return regeneratorRuntime.wrap(function(t){while(1)switch(t.prev=t.next){case 0:if(!e.IS_IDS4){t.next=3;break}return t.next=3,this.refreshUserInfo();case 3:this.setTags(n),r=this.$refs.tag,this.$nextTick(function(){if(r){var e=!0,t=!1,n=void 0;try{for(var a,s=o()(r);!(e=(a=s.next()).done);e=!0){var c=a.value;if(c.to.path===i.$route.path){i.$refs.scrollPane.moveToTarget(c,r);break}}}catch(l){t=!0,n=l}finally{try{e||null==s.return||s.return()}finally{if(t)throw n}}}});case 6:case"end":return t.stop()}},t,this)}));function n(e,n){return t.apply(this,arguments)}return n}()},created:function(){this.setTags(this.$route),"true"==window.localStorage.collapse?(this.collapsed=!1,this.collapse()):(this.collapsed=!0,this.collapse())}}}).call(this,n("c8ba"))},"1aad":function(e,t,n){"use strict";var a=n("0ccd"),o=n.n(a);o.a},3180:function(e,t,n){"use strict";n.d(t,"a",function(){return d});n("96cf");var a=n("3b8d"),o=n("d225"),r=n("b0b4"),i=n("308d"),s=n("6bb5"),c=n("4e2b"),l=n("dd17"),u=function(e){function t(){return Object(o["a"])(this,t),Object(i["a"])(this,Object(s["a"])(t).call(this,{authority:"https://ids.neters.club",client_id:"blogadminjs",redirect_uri:"https://vueadmin.neters.club/callback",response_type:"id_token token",scope:"openid profile roles blog.core.api",post_logout_redirect_uri:"https://vueadmin.neters.club"}))}return Object(c["a"])(t,e),Object(r["a"])(t,[{key:"login",value:function(){var e=Object(a["a"])(regeneratorRuntime.mark(function e(){return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.signinRedirect();case 2:return e.abrupt("return",this.getUser());case 3:case"end":return e.stop()}},e,this)}));function t(){return e.apply(this,arguments)}return t}()},{key:"logout",value:function(){var e=Object(a["a"])(regeneratorRuntime.mark(function e(){return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this.signoutRedirect());case 1:case"end":return e.stop()}},e,this)}));function t(){return e.apply(this,arguments)}return t}()}]),t}(l["UserManager"]),d=new u},"32e93":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-scrollbar",{staticClass:"scrollbar-handle",staticStyle:{height:"100%"}},[n("keep-alive",[e.$route.meta.keepAlive?n("router-view"):e._e()],1),e.$route.meta.keepAlive?e._e():n("router-view")],1)},o=[],r=n("2877"),i={},s=Object(r["a"])(i,a,o,!1,null,null,null);s.options.__file="Layout.vue";t["default"]=s.exports},"38cf":function(e,t,n){},"3b00":function(e,t,n){"use strict";n("7f7f"),n("96cf");var a=n("3b8d"),o=n("3180"),r={data:function(){return{user:{name:"",isAuthenticated:!1}}},methods:{refreshUserInfo:function(){var e=Object(a["a"])(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,o["a"].getUser();case 2:t=e.sent,t?(this.user.name=t.profile.name,this.user.isAuthenticated=!0):(this.user.name="",this.user.isAuthenticated=!1);case 4:case"end":return e.stop()}},e,this)}));function t(){return e.apply(this,arguments)}return t}()},created:function(){var e=Object(a["a"])(regeneratorRuntime.mark(function e(){return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.refreshUserInfo();case 2:case"end":return e.stop()}},e,this)}));function t(){return e.apply(this,arguments)}return t}()};t["a"]=r},"3e70":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement;e._self._c;return e._m(0)},o=[function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"thanks"},[n("iframe",{attrs:{src:"https://apk.neters.club",scrolling:"auto",id:"apidoc",width:"100%",frameborder:"0"}})])}],r=(n("c5f6"),{name:"APIDoc",data:function(){return{}},mounted:function(){function e(){var e=document.getElementById("apidoc"),t=(document.body.clientWidth,document.body.clientHeight);e.style.height=Number(t)-128+"px"}e(),window.onresize=function(){e()}}}),i=r,s=(n("1aad"),n("2877")),c=Object(s["a"])(i,a,o,!1,null,"1759f995",null);c.options.__file="APIDoc.vue";t["default"]=c.exports},"4ec3":function(e,t,n){"use strict";(function(e){n.d(t,"a",function(){return f}),n.d(t,"ob",function(){return m}),n.d(t,"pb",function(){return h}),n.d(t,"rb",function(){return b}),n.d(t,"Q",function(){return w}),n.d(t,"ub",function(){return k}),n.d(t,"R",function(){return y}),n.d(t,"lb",function(){return x}),n.d(t,"v",function(){return C}),n.d(t,"i",function(){return T}),n.d(t,"n",function(){return S}),n.d(t,"M",function(){return _}),n.d(t,"jb",function(){return D}),n.d(t,"t",function(){return I}),n.d(t,"f",function(){return E}),n.d(t,"G",function(){return P}),n.d(t,"hb",function(){return A}),n.d(t,"r",function(){return O}),n.d(t,"d",function(){return B}),n.d(t,"K",function(){return N}),n.d(t,"ib",function(){return L}),n.d(t,"s",function(){return j}),n.d(t,"e",function(){return M}),n.d(t,"J",function(){return R}),n.d(t,"I",function(){return $}),n.d(t,"g",function(){return z}),n.d(t,"H",function(){return U}),n.d(t,"C",function(){return F}),n.d(t,"fb",function(){return W}),n.d(t,"p",function(){return V}),n.d(t,"b",function(){return J}),n.d(t,"B",function(){return q}),n.d(t,"A",function(){return G}),n.d(t,"o",function(){return H}),n.d(t,"eb",function(){return Q}),n.d(t,"F",function(){return K}),n.d(t,"L",function(){return X}),n.d(t,"w",function(){return Y}),n.d(t,"x",function(){return Z}),n.d(t,"N",function(){return ee}),n.d(t,"z",function(){return te}),n.d(t,"O",function(){return ne}),n.d(t,"kb",function(){return ae}),n.d(t,"u",function(){return oe}),n.d(t,"h",function(){return re}),n.d(t,"sb",function(){return ie}),n.d(t,"tb",function(){return se}),n.d(t,"cb",function(){return ce}),n.d(t,"Z",function(){return le}),n.d(t,"qb",function(){return ue}),n.d(t,"P",function(){return de}),n.d(t,"y",function(){return pe}),n.d(t,"S",function(){return fe}),n.d(t,"mb",function(){return me}),n.d(t,"l",function(){return he}),n.d(t,"j",function(){return ge}),n.d(t,"vb",function(){return be}),n.d(t,"Y",function(){return ve}),n.d(t,"db",function(){return we}),n.d(t,"V",function(){return ke}),n.d(t,"xb",function(){return ye}),n.d(t,"U",function(){return xe}),n.d(t,"nb",function(){return Ce}),n.d(t,"m",function(){return Te}),n.d(t,"k",function(){return Se}),n.d(t,"wb",function(){return _e}),n.d(t,"W",function(){return De}),n.d(t,"X",function(){return Ie}),n.d(t,"T",function(){return Ee}),n.d(t,"bb",function(){return Pe}),n.d(t,"ab",function(){return Ae}),n.d(t,"E",function(){return Oe}),n.d(t,"gb",function(){return Be}),n.d(t,"q",function(){return Ne}),n.d(t,"c",function(){return Le}),n.d(t,"D",function(){return je});n("a481");var a=n("795b"),o=n.n(a),r=(n("cadf"),n("551c"),n("097d"),n("bc3a")),i=n.n(r),s=n("a18c"),c=n("c0d6"),l=n("2b0e"),u=n("3180"),d="";i.a.defaults.timeout=2e4;var p=c["a"];i.a.interceptors.request.use(function(e){var t=new Date,n=new Date(Date.parse(p.state.tokenExpire));return p.state.token&&t<n&&p.state.tokenExpire&&(e.headers.Authorization="Bearer "+p.state.token),b(),e},function(e){return o.a.reject(e)}),i.a.interceptors.response.use(function(e){return e},function(e){var t={success:!1,message:"错误"},n=e.config;if("ECONNABORTED"!=e.code||-1==e.message.indexOf("timeout")||n._retry)if(e.response)if(401==e.response.status){var a=new Date,o=new Date(Date.parse(window.localStorage.refreshtime));if(window.localStorage.refreshtime&&a<=o)return g({token:window.localStorage.Token}).then(function(t){if(t.success){l["default"].prototype.$message({message:"refreshToken success! loading data...",type:"success"}),c["a"].commit("saveToken",t.response.token);var n=new Date,a=new Date(n.setSeconds(n.getSeconds()+t.response.expires_in));return c["a"].commit("saveTokenExpire",a),e.config.__isRetryRequest=!0,e.config.headers.Authorization="Bearer "+t.response.token,i()(e.config)}v()});v()}else 403==e.response.status?t.message="失败！该操作无权限":429==e.response.status?t.message="刷新次数过多，请稍事休息重试！":404==e.response.status?t.message="失败！访问接口不存在":500==e.response.status?t.message="失败！服务器异常":405==e.response.status?t.message="失败！请求http方法错误":415==e.response.status?t.message="失败！参数没有指定Body还是Query":t.message="失败！请求错误"+e.response.status;else t.message="失败！服务器断开";else t.message="请求超时！",n._retry=!0;return l["default"].prototype.$message({message:t.message,type:"error"}),t});var f=d,m=function(e){return i.a.get("".concat(d,"/api/login/jwttoken3.0"),{params:e}).then(function(e){return e.data})},h=function(e){return i.a.post("".concat(d,"/login"),e).then(function(e){return e.data})},g=function(e){return i.a.get("".concat(d,"/api/login/RefreshToken"),{params:e}).then(function(e){return e.data})},b=function(e){var t=new Date,n=window.localStorage.refreshtime?new Date(window.localStorage.refreshtime):new Date(-1),a=new Date(Date.parse(window.localStorage.TokenExpire)),o=1;n>=t?(n=t>a?t:a,n.setMinutes(n.getMinutes()+o),window.localStorage.refreshtime=n):window.localStorage.refreshtime=new Date(-1)},v=function(t){c["a"].commit("saveToken",""),c["a"].commit("saveTokenExpire",""),c["a"].commit("saveTagsData",""),window.localStorage.removeItem("user"),window.localStorage.removeItem("NavigationBar"),e.IS_IDS4?u["a"].login():s["a"].replace({path:"/login",query:{redirect:s["a"].currentRoute.fullPath}})},w=function(e){return i.a.get("".concat(d,"/api/user/getInfoByToken"),{params:e}).then(function(e){return e.data})};var k=function(e){console.log("api is ok.")},y=function(e){return i.a.get("".concat(d,"/api/user/get"),{params:e})},x=function(e){return i.a.delete("".concat(d,"/api/user/delete"),{params:e})},C=function(e){return i.a.put("".concat(d,"/api/user/put"),e)},T=function(e){return i.a.post("".concat(d,"/api/user/post"),e)},S=function(e){return i.a.delete("".concat(d,"/api/Claims/BatchDelete"),{params:e})},_=function(e){return i.a.get("".concat(d,"/api/role/get"),{params:e})},D=function(e){return i.a.delete("".concat(d,"/api/role/delete"),{params:e})},I=function(e){return i.a.put("".concat(d,"/api/role/put"),e)},E=function(e){return i.a.post("".concat(d,"/api/role/post"),e)},P=function(e){return i.a.get("".concat(d,"/api/module/get"),{params:e})},A=function(e){return i.a.delete("".concat(d,"/api/module/delete"),{params:e})},O=function(e){return i.a.put("".concat(d,"/api/module/put"),e)},B=function(e){return i.a.post("".concat(d,"/api/module/post"),e)},N=function(e){return i.a.get("".concat(d,"/api/permission/GetTreeTable"),{params:e})},L=function(e){return i.a.delete("".concat(d,"/api/permission/delete"),{params:e})},j=function(e){return i.a.put("".concat(d,"/api/permission/put"),e)},M=function(e){return i.a.post("".concat(d,"/api/permission/post"),e)},R=function(e){return i.a.get("".concat(d,"/api/permission/getpermissiontree"),{params:e})},$=function(e){return i.a.get("".concat(d,"/api/permission/GetPermissionIdByRoleId"),{params:e})},z=function(e){return i.a.post("".concat(d,"/api/permission/Assign"),e)},U=function(e){return i.a.get("".concat(d,"/api/permission/GetNavigationBar"),{params:e}).then(function(e){return e.data})},F=function(e){return i.a.get("".concat(d,"/api/TopicDetail/get"),{params:e})},W=function(e){return i.a.delete("".concat(d,"/api/TopicDetail/delete"),{params:e})},V=function(e){return i.a.put("".concat(d,"/api/TopicDetail/update"),e)},J=function(e){return i.a.post("".concat(d,"/api/TopicDetail/post"),e)},q=function(e){return i.a.get("".concat(d,"/api/Blog"),{params:e})},G=function(e){return i.a.get("".concat(d,"/api/Blog/DetailNuxtNoPer"),{params:e})},H=function(e){return i.a.put("".concat(d,"/api/Blog/update"),e)},Q=function(e){return i.a.delete("".concat(d,"/api/Blog/delete"),{params:e})},K=function(e){return i.a.get("".concat(d,"/api/Monitor/get"),{params:e})},X=function(e){return i.a.get("".concat(d,"/api/Monitor/GetRequestApiinfoByWeek"),{params:e})},Y=function(e){return i.a.get("".concat(d,"/api/Monitor/GetAccessApiByDate"),{params:e})},Z=function(e){return i.a.get("".concat(d,"/api/Monitor/GetAccessApiByHour"),{params:e})},ee=function(e){return i.a.get("".concat(d,"/api/Monitor/Server"),{params:e})},te=function(e){return i.a.get("".concat(d,"/api/Monitor/GetActiveUsers"),{params:e})},ne=function(e){return i.a.get("".concat(d,"/api/TasksQz/get"),{params:e})},ae=function(e){return i.a.delete("".concat(d,"/api/TasksQz/delete"),{params:e})},oe=function(e){return i.a.put("".concat(d,"/api/TasksQz/put"),e)},re=function(e){return i.a.post("".concat(d,"/api/TasksQz/post"),e)},ie=function(e){return i.a.get("".concat(d,"/api/TasksQz/StartJob"),{params:e})},se=function(e){return i.a.get("".concat(d,"/api/TasksQz/StopJob"),{params:e})},ce=function(e){return i.a.get("".concat(d,"/api/TasksQz/ReCovery"),{params:e})},le=function(e){return i.a.get("".concat(d,"/api/TasksQz/PauseJob"),{params:e})},ue=function(e){return i.a.get("".concat(d,"/api/TasksQz/ResumeJob"),{params:e})},de=function(e){return i.a.get("".concat(d,"/api/TasksQz/GetTaskNameSpace"),{params:e})},pe=function(e){return i.a.get("".concat(d,"/is4api/GetAchieveUsers"),{params:e})},fe=function(e){return i.a.get("".concat(d,"/api/WeChatConfig/get"),{params:e})},me=function(e){return i.a.delete("".concat(d,"/api/WeChatConfig/delete"),{params:e})},he=function(e){return i.a.delete("".concat(d,"/api/WeChatConfig/BatchDelete"),{params:e})},ge=function(e){return i.a.post("".concat(d,"/api/WeChatConfig/post"),e)},be=function(e){return i.a.put("".concat(d,"/api/WeChatConfig/put"),e)},ve=function(e){return i.a.get("".concat(d,"/api/WeChat/GetTemplate"),{params:e})},we=function(e){return i.a.get("".concat(d,"/api/WeChat/RefreshToken"),{params:e})},ke=function(e){return i.a.get("".concat(d,"/api/WeChat/GetMenu"),{params:e})},ye=function(e){return i.a.put("".concat(d,"/api/WeChat/updateMenu"),e)},xe=function(e){return i.a.get("".concat(d,"/api/WeChatCompany/get"),{params:e})},Ce=function(e){return i.a.delete("".concat(d,"/api/WeChatCompany/delete"),{params:e})},Te=function(e){return i.a.delete("".concat(d,"/api/WeChatCompany/BatchDelete"),{params:e})},Se=function(e){return i.a.post("".concat(d,"/api/WeChatCompany/post"),e)},_e=function(e){return i.a.put("".concat(d,"/api/WeChatCompany/put"),e)},De=function(e){return i.a.get("".concat(d,"/api/WeChatPushLog/get"),{params:e})},Ie=function(e){return i.a.get("".concat(d,"/api/WeChat/GetSubUsers"),{params:e})},Ee=function(e){return i.a.get("".concat(d,"/api/WeChatSub/get"),{params:e})},Pe=function(e){return i.a.post("".concat(d,"/api/WeChat/PushTxtMsg"),e)},Ae=function(e){return i.a.post("".concat(d,"/api/WeChat/PushCardMsg"),e)},Oe=function(e){return i.a.get("".concat(d,"/api/department/getTreeTable"),{params:e})},Be=function(e){return i.a.delete("".concat(d,"/api/department/delete"),{params:e})},Ne=function(e){return i.a.put("".concat(d,"/api/department/put"),e)},Le=function(e){return i.a.post("".concat(d,"/api/department/post"),e)},je=function(e){return i.a.get("".concat(d,"/api/department/getDepartmentTree"),{params:e})}}).call(this,n("c8ba"))},"55ad":function(e,t,n){"use strict";(function(e){n("a481");var a=n("2b0e"),o=n("2819"),r=n.n(o),i=n("3180"),s=n("4ec3");a["default"].use(r.a),t["a"]={name:"Welcome",data:function(){return{listLoading:!1,welcomeInitData:{activeUsers:[],activeUserCount:0,logs:[],errorCount:0},serverInfo:{},extend:{series:{label:{normal:{show:!0}}}},lineChartDataIDS4:{columns:[],rows:[],today:0},lineChartSettings7Day:{metrics:["count"],dimension:["date"]},lineChartMarkPoint:{data:[{name:"最大值",type:"max"},{name:"最小值",type:"min"}]}}},methods:{getTypeName:function(e){return e>=10&&e<50?"warning":e<10?"primary":""},getBck:function(e){return"background: rgb(".concat(43+14*e,", ").concat(148+7*e,", 255) none repeat scroll 0% 0%;")},toLogs:function(){this.$router.replace({path:"/Logs/Index"})}},mounted:function(){var t=this,n=new Date;if(window.localStorage.TokenExpire){var a=new Date(Date.parse(window.localStorage.TokenExpire));n>=a&&(e.IS_IDS4?i["a"].login():this.$router.push("/login"))}else e.IS_IDS4?i["a"].login():this.$router.push("/login");e.IS_IDS4&&Object(s["y"])({}).then(function(e){t.lineChartDataIDS4=e.data.response}),Object(s["N"])({}).then(function(e){t.serverInfo=e.data.response}),Object(s["z"])({}).then(function(e){t.welcomeInitData=e.data.response})}}}).call(this,n("c8ba"))},"56d7":function(e,t,n){"use strict";n.r(t);n("cadf"),n("551c"),n("097d");var a=n("2b0e"),o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{attrs:{id:"app"}},[e.$route.meta.NoNeedHome?a("transition",{attrs:{name:"fade",mode:"out-in"}},[a("div",[a("router-view")],1)]):a("transition",{attrs:{name:"fade",mode:"out-in"}},[a("el-row",{staticClass:"container"},[a("el-col",{staticClass:"header",attrs:{span:24}},[a("el-col",{staticClass:"logo collapsedLogo",class:e.collapsed?"logo-collapse-width":"logo-width",attrs:{span:10}},[a("div",{on:{click:e.toindex}},[e._v(" "+e._s(e.collapsed?e.sysNameShort:e.sysName))])]),a("el-col",{staticClass:"logoban",attrs:{span:10}},[a("div",{class:e.collapsed?"tools collapsed":"tools",on:{click:e.collapse}},[a("i",{staticClass:"fa fa-align-justify"})]),a("el-breadcrumb",{staticClass:"breadcrumb-inner collapsedLogo",attrs:{separator:"/"}},e._l(e.$route.matched,function(t){return a("el-breadcrumb-item",{key:t.path},[a("span",{},[e._v(" "+e._s(t.name))])])}),1)],1),a("el-col",{staticClass:"userinfo",attrs:{span:4}},[a("el-dropdown",{attrs:{trigger:"hover"}},[a("span",{staticClass:"el-dropdown-link userinfo-inner"},[e._v("\n                    "+e._s(e.sysUserName)+"\n                    "),a("img",{attrs:{src:n("cf05"),height:"128",width:"128"}})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{nativeOn:{click:function(t){return e.myNews(t)}}},[a("el-badge",{staticClass:"item",attrs:{value:2,type:"warning"}},[e._v("\n                                    我的消息\n                                ")])],1),a("el-dropdown-item",{nativeOn:{click:function(t){return e.Setting(t)}}},[e._v("设置")]),a("el-dropdown-item",{nativeOn:{click:function(t){return e.goGithub(t)}}},[e._v("Github")]),a("el-dropdown-item",{attrs:{divided:""},nativeOn:{click:function(t){return e.logout(t)}}},[e._v("退出登录")])],1)],1)],1)],1),a("el-col",{staticClass:"main",attrs:{span:24}},[a("aside",{class:e.collapsedClass},[a("el-scrollbar",{staticClass:"scrollbar-handle",staticStyle:{height:"100%",background:"#2f3e52"}},[a("el-menu",{staticClass:"el-menu-vertical-demo",staticStyle:{"border-right":"none"},attrs:{"default-active":e.$route.path,"unique-opened":"",router:"",collapse:e.collapsed,"background-color":"#2f3e52","text-color":"#fff","active-text-color":"#ffd04b"},on:{open:e.handleopen,close:e.handleclose,select:e.handleselect}},e._l(e.routes,function(e,t){return a("sidebar",{key:t,attrs:{item:e}})}),1)],1)],1),a("el-col",{staticClass:"content-wrapper",class:e.collapsed?"content-collapsed":"content-expanded",attrs:{span:24}},[e.showTags?a("div",{staticClass:"tags"},[a("div",{staticClass:"tags-view-container",attrs:{id:"tags-view-container"}},[a("scroll-pane",{ref:"scrollPane",staticClass:"tags-view-wrapper"},e._l(e.tagsList,function(t,n){return a("router-link",{key:t.path,ref:"tag",refInFor:!0,staticClass:"tags-view-item",class:{active:e.isActive(t.path)},attrs:{to:{path:t.path,query:t.query,fullPath:t.fullPath},tag:"span"},nativeOn:{mouseup:function(t){if("button"in t&&1!==t.button)return null;e.closeTags(n)}}},[e._v("\n                                    "+e._s(t.title)+"\n                                    "),a("span",{staticClass:"el-icon-close",on:{click:function(t){t.preventDefault(),t.stopPropagation(),e.closeTags(n)}}})])}),1)],1),a("div",{staticClass:"tags-close-box"},[a("el-dropdown",{on:{command:e.handleTags}},[a("el-button",{attrs:{size:"mini"}},[a("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown",size:"small"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{command:"other"}},[e._v("关闭其他")]),a("el-dropdown-item",{attrs:{command:"all"}},[e._v("关闭所有")])],1)],1)],1)]):e._e(),a("transition",{attrs:{name:"fade",mode:"out-in"}},[a("div",{staticClass:"content-az router-view-withly"},[a("router-view")],1)])],1)],1)],1)],1),a("el-dialog",{class:e.newsDialogCss,attrs:{title:"Unread Messages",visible:e.NewsVisible,"close-on-click-modal":!1},on:{"update:visible":function(t){e.NewsVisible=t}},model:{value:e.NewsVisible,callback:function(t){e.NewsVisible=t},expression:"NewsVisible"}},[a("div",e._l(e.tagNews,function(t){return a("el-tag",{key:t.name,staticClass:"tag-new",attrs:{closable:"",type:t.type}},[e._v("\n                "+e._s(t.name)+"\n            ")])}),1)]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.SidebarVisible,expression:"SidebarVisible"}],staticClass:"v-modal ",staticStyle:{"z-index":"2999"},attrs:{tabindex:"0"},on:{click:e.collapse}})],1)},r=[],i=n("199c"),s=i["a"],c=(n("034f"),n("b0a0"),n("6964"),n("dc82"),n("9672"),n("2877")),l=Object(c["a"])(s,o,r,!1,null,null,null);l.options.__file="App.vue";var u=l.exports,d=n("c0d6"),p=n("a18c"),f=(n("7e4f"),n("cdc6"),n("cebc")),m=n("a925"),h=n("a78e"),g=n.n(h),b=n("b2d6"),v=n.n(b),w=n("f0d9"),k=n.n(w),y=n("3ed6"),x=n.n(y),C={route:{dashboard:"Dashboard",introduction:"Introduction",documentation:"Documentation",guide:"Guide",permission:"Permission",pagePermission:"Page Permission",rolePermission:"Role Permission",directivePermission:"Directive Permission",icons:"Icons",components:"Components",componentIndex:"Introduction",tinymce:"Tinymce",markdown:"Markdown",jsonEditor:"JSON Editor",dndList:"Dnd List",splitPane:"SplitPane",avatarUpload:"Avatar Upload",dropzone:"Dropzone",sticky:"Sticky",countTo:"CountTo",componentMixin:"Mixin",backToTop:"BackToTop",dragDialog:"Drag Dialog",dragSelect:"Drag Select",dragKanban:"Drag Kanban",charts:"Charts",keyboardChart:"Keyboard Chart",lineChart:"Line Chart",mixChart:"Mix Chart",example:"Example",nested:"Nested Routes",menu1:"Menu 1","menu1-1":"Menu 1-1","menu1-2":"Menu 1-2","menu1-2-1":"Menu 1-2-1","menu1-2-2":"Menu 1-2-2","menu1-3":"Menu 1-3",menu2:"Menu 2",Table:"Table",dynamicTable:"Dynamic Table",dragTable:"Drag Table",inlineEditTable:"Inline Edit",complexTable:"Complex Table",treeTable:"Tree Table",customTreeTable:"Custom TreeTable",tab:"Tab",form:"Form",createArticle:"Create Article",editArticle:"Edit Article",articleList:"Article List",errorPages:"Error Pages",page401:"401",page404:"404",errorLog:"Error Log",excel:"Excel",exportExcel:"Export Excel",selectExcel:"Export Selected",mergeHeader:"Merge Header",uploadExcel:"Upload Excel",zip:"Zip",pdf:"PDF",exportZip:"Export Zip",theme:"Theme",clipboardDemo:"Clipboard",i18n:"I18n",externalLink:"External Link"},navbar:{logOut:"Log Out",dashboard:"Dashboard",github:"Github",theme:"Theme",size:"Global Size"},login:{title:"Login Form",logIn:"Log in",username:"Username",password:"Password",any:"any",thirdparty:"Or connect with",thirdpartyTips:"Can not be simulated on local, so please combine you own business simulation! ! !"},documentation:{documentation:"Documentation",github:"Github Repository"},permission:{addRole:"New Role",editPermission:"Edit Permission",roles:"Your roles",switchRoles:"Switch roles",tips:"In some cases it is not suitable to use v-permission, such as element Tab component or el-table-column and other asynchronous rendering dom cases which can only be achieved by manually setting the v-if.",delete:"Delete",confirm:"Confirm",cancel:"Cancel"},guide:{description:"The guide page is useful for some people who entered the project for the first time. You can briefly introduce the features of the project. Demo is based on ",button:"Show Guide"},components:{documentation:"Documentation",tinymceTips:"Rich text editor is a core part of management system, but at the same time is a place with lots of problems. In the process of selecting rich texts, I also walked a lot of detours. The common rich text editors in the market are basically used, and the finally chose Tinymce. See documentation for more detailed rich text editor comparisons and introductions.",dropzoneTips:"Because my business has special needs, and has to upload images to qiniu, so instead of a third party, I chose encapsulate it by myself. It is very simple, you can see the detail code in @/components/Dropzone.",stickyTips:"when the page is scrolled to the preset position will be sticky on the top.",backToTopTips1:"When the page is scrolled to the specified position, the Back to Top button appears in the lower right corner",backToTopTips2:"You can customize the style of the button, show / hide, height of appearance, height of the return. If you need a text prompt, you can use element-ui el-tooltip elements externally",imageUploadTips:"Since I was using only the vue@1 version, and it is not compatible with mockjs at the moment, I modified it myself, and if you are going to use it, it is better to use official version."},table:{dynamicTips1:"Fixed header, sorted by header order",dynamicTips2:"Not fixed header, sorted by click order",dragTips1:"The default order",dragTips2:"The after dragging order",title:"Title",importance:"Imp",type:"Type",remark:"Remark",search:"Search",add:"Add",export:"Export",reviewer:"reviewer",id:"ID",date:"Date",author:"Author",readings:"Readings",status:"Status",actions:"Actions",edit:"Edit",publish:"Publish",draft:"Draft",delete:"Delete",cancel:"Cancel",confirm:"Confirm"},errorLog:{tips:"Please click the bug icon in the upper right corner",description:"Now the management system are basically the form of the spa, it enhances the user experience, but it also increases the possibility of page problems, a small negligence may lead to the entire page deadlock. Fortunately Vue provides a way to catch handling exceptions, where you can handle errors or report exceptions.",documentation:"Document introduction"},excel:{export:"Export",selectedExport:"Export Selected Items",placeholder:"Please enter the file name(default excel-list)"},zip:{export:"Export",placeholder:"Please enter the file name(default file)"},pdf:{tips:"Here we use window.print() to implement the feature of downloading pdf."},theme:{change:"Change Theme",documentation:"Theme documentation",tips:"Tips: It is different from the theme-pick on the navbar is two different skinning methods, each with different application scenarios. Refer to the documentation for details."},tagsView:{refresh:"Refresh",close:"Close",closeOthers:"Close Others",closeAll:"Close All"}},T={route:{dashboard:"首页",introduction:"简述",documentation:"文档",guide:"引导页",permission:"权限测试页",rolePermission:"角色权限",pagePermission:"页面权限",directivePermission:"指令权限",icons:"图标",components:"组件",componentIndex:"介绍",tinymce:"富文本编辑器",markdown:"Markdown",jsonEditor:"JSON编辑器",dndList:"列表拖拽",splitPane:"Splitpane",avatarUpload:"头像上传",dropzone:"Dropzone",sticky:"Sticky",countTo:"CountTo",componentMixin:"小组件",backToTop:"返回顶部",dragDialog:"拖拽 Dialog",dragSelect:"拖拽 Select",dragKanban:"可拖拽看板",charts:"图表",keyboardChart:"键盘图表",lineChart:"折线图",mixChart:"混合图表",example:"综合实例",nested:"路由嵌套",menu1:"菜单1","menu1-1":"菜单1-1","menu1-2":"菜单1-2","menu1-2-1":"菜单1-2-1","menu1-2-2":"菜单1-2-2","menu1-3":"菜单1-3",menu2:"菜单2",Table:"Table",dynamicTable:"动态Table",dragTable:"拖拽Table",inlineEditTable:"Table内编辑",complexTable:"综合Table",treeTable:"树形表格",customTreeTable:"自定义树表",tab:"Tab",form:"表单",createArticle:"创建文章",editArticle:"编辑文章",articleList:"文章列表",errorPages:"错误页面",page401:"401",page404:"404",errorLog:"错误日志",excel:"Excel",exportExcel:"导出 Excel",selectExcel:"导出 已选择项",mergeHeader:"导出 多级表头",uploadExcel:"上传 Excel",zip:"Zip",pdf:"PDF",exportZip:"Export Zip",theme:"换肤",clipboardDemo:"Clipboard",i18n:"国际化",externalLink:"外链"},navbar:{logOut:"退出登录",dashboard:"首页",github:"项目地址",theme:"换肤",size:"布局大小"},login:{title:"系统登录",logIn:"登录",username:"账号",password:"密码",any:"随便填",thirdparty:"第三方登录",thirdpartyTips:"本地不能模拟，请结合自己业务进行模拟！！！"},documentation:{documentation:"文档",github:"Github 地址"},permission:{addRole:"新增角色",editPermission:"编辑权限",roles:"你的权限",switchRoles:"切换权限",tips:"在某些情况下，不适合使用 v-permission。例如：Element-UI 的 Tab 组件或 el-table-column 以及其它动态渲染 dom 的场景。你只能通过手动设置 v-if 来实现。",delete:"删除",confirm:"确定",cancel:"取消"},guide:{description:"引导页对于一些第一次进入项目的人很有用，你可以简单介绍下项目的功能。本 Demo 是基于",button:"打开引导"},components:{documentation:"文档",tinymceTips:"富文本是管理后台一个核心的功能，但同时又是一个有很多坑的地方。在选择富文本的过程中我也走了不少的弯路，市面上常见的富文本都基本用过了，最终权衡了一下选择了Tinymce。更详细的富文本比较和介绍见",dropzoneTips:"由于我司业务有特殊需求，而且要传七牛 所以没用第三方，选择了自己封装。代码非常的简单，具体代码你可以在这里看到 @/components/Dropzone",stickyTips:"当页面滚动到预设的位置会吸附在顶部",backToTopTips1:"页面滚动到指定位置会在右下角出现返回顶部按钮",backToTopTips2:"可自定义按钮的样式、show/hide、出现的高度、返回的位置 如需文字提示，可在外部使用Element的el-tooltip元素",imageUploadTips:"由于我在使用时它只有vue@1版本，而且和mockjs不兼容，所以自己改造了一下，如果大家要使用的话，优先还是使用官方版本。"},table:{dynamicTips1:"固定表头, 按照表头顺序排序",dynamicTips2:"不固定表头, 按照点击顺序排序",dragTips1:"默认顺序",dragTips2:"拖拽后顺序",title:"标题",importance:"重要性",type:"类型",remark:"点评",search:"搜索",add:"添加",export:"导出",reviewer:"审核人",id:"序号",date:"时间",author:"作者",readings:"阅读数",status:"状态",actions:"操作",edit:"编辑",publish:"发布",draft:"草稿",delete:"删除",cancel:"取 消",confirm:"确 定"},errorLog:{tips:"请点击右上角bug小图标",description:"现在的管理后台基本都是spa的形式了，它增强了用户体验，但同时也会增加页面出问题的可能性，可能一个小小的疏忽就导致整个页面的死锁。好在 Vue 官网提供了一个方法来捕获处理异常，你可以在其中进行错误处理或者异常上报。",documentation:"文档介绍"},excel:{export:"导出",selectedExport:"导出已选择项",placeholder:"请输入文件名(默认excel-list)"},zip:{export:"导出",placeholder:"请输入文件名(默认file)"},pdf:{tips:"这里使用   window.print() 来实现下载pdf的功能"},theme:{change:"换肤",documentation:"换肤文档",tips:"Tips: 它区别于 navbar 上的 theme-pick, 是两种不同的换肤方法，各自有不同的应用场景，具体请参考文档。"},tagsView:{refresh:"刷新",close:"关闭",closeOthers:"关闭其它",closeAll:"关闭所有"}},S={route:{dashboard:"Panel de control",introduction:"Introducción",documentation:"Documentación",guide:"Guía",permission:"Permisos",rolePermission:"Permisos de rol",pagePermission:"Permisos de la página",directivePermission:"Permisos de la directiva",icons:"Iconos",components:"Componentes",componentIndex:"Introducción",tinymce:"Tinymce",markdown:"Markdown",jsonEditor:"Editor JSON",dndList:"Lista Dnd",splitPane:"Panel dividido",avatarUpload:"Subir avatar",dropzone:"Subir ficheros",sticky:"Sticky",countTo:"CountTo",componentMixin:"Mixin",backToTop:"Ir arriba",dragDialog:"Drag Dialog",dragSelect:"Drag Select",dragKanban:"Drag Kanban",charts:"Gráficos",keyboardChart:"Keyboard Chart",lineChart:"Gráfico de líneas",mixChart:"Mix Chart",example:"Ejemplo",nested:"Rutas anidadass",menu1:"Menu 1","menu1-1":"Menu 1-1","menu1-2":"Menu 1-2","menu1-2-1":"Menu 1-2-1","menu1-2-2":"Menu 1-2-2","menu1-3":"Menu 1-3",menu2:"Menu 2",Table:"Tabla",dynamicTable:"Tabla dinámica",dragTable:"Arrastrar tabla",inlineEditTable:"Editor",complexTable:"Complex Table",treeTable:"Tree Table",customTreeTable:"Custom TreeTable",tab:"Pestaña",form:"Formulario",createArticle:"Crear artículo",editArticle:"Editar artículo",articleList:"Listado de artículos",errorPages:"Páginas de error",page401:"401",page404:"404",errorLog:"Registro de errores",excel:"Excel",exportExcel:"Exportar a Excel",selectExcel:"Export seleccionado",mergeHeader:"Merge Header",uploadExcel:"Subir Excel",zip:"Zip",pdf:"PDF",exportZip:"Exportar a Zip",theme:"Tema",clipboardDemo:"Clipboard",i18n:"I18n",externalLink:"Enlace externo"},navbar:{logOut:"Salir",dashboard:"Panel de control",github:"Github",theme:"Tema",size:"Tamaño global"},login:{title:"Formulario de acceso",logIn:"Acceso",username:"Usuario",password:"Contraseña",any:"nada",thirdparty:"Conectar con",thirdpartyTips:"No se puede simular en local, así que combine su propia simulación de negocios. ! !"},documentation:{documentation:"Documentación",github:"Repositorio Github"},permission:{addRole:"Nuevo rol",editPermission:"Permiso de edición",roles:"Tus permisos",switchRoles:"Cambiar permisos",tips:"In some cases it is not suitable to use v-permission, such as element Tab component or el-table-column and other asynchronous rendering dom cases which can only be achieved by manually setting the v-if.",delete:"Borrar",confirm:"Confirmar",cancel:"Cancelar"},guide:{description:"The guide page is useful for some people who entered the project for the first time. You can briefly introduce the features of the project. Demo is based on ",button:"Ver guía"},components:{documentation:"Documentación",tinymceTips:"Rich text editor is a core part of management system, but at the same time is a place with lots of problems. In the process of selecting rich texts, I also walked a lot of detours. The common rich text editors in the market are basically used, and the finally chose Tinymce. See documentation for more detailed rich text editor comparisons and introductions.",dropzoneTips:"Because my business has special needs, and has to upload images to qiniu, so instead of a third party, I chose encapsulate it by myself. It is very simple, you can see the detail code in @/components/Dropzone.",stickyTips:"when the page is scrolled to the preset position will be sticky on the top.",backToTopTips1:"When the page is scrolled to the specified position, the Back to Top button appears in the lower right corner",backToTopTips2:"You can customize the style of the button, show / hide, height of appearance, height of the return. If you need a text prompt, you can use element-ui el-tooltip elements externally",imageUploadTips:"Since I was using only the vue@1 version, and it is not compatible with mockjs at the moment, I modified it myself, and if you are going to use it, it is better to use official version."},table:{dynamicTips1:"Fixed header, sorted by header order",dynamicTips2:"Not fixed header, sorted by click order",dragTips1:"Orden por defecto",dragTips2:"The after dragging order",title:"Título",importance:"Importancia",type:"Tipo",remark:"Remark",search:"Buscar",add:"Añadir",export:"Exportar",reviewer:"reviewer",id:"ID",date:"Fecha",author:"Autor",readings:"Lector",status:"Estado",actions:"Acciones",edit:"Editar",publish:"Publicar",draft:"Draft",delete:"Eliminar",cancel:"Cancelar",confirm:"Confirmar"},errorLog:{tips:"Please click the bug icon in the upper right corner",description:"Now the management system are basically the form of the spa, it enhances the user experience, but it also increases the possibility of page problems, a small negligence may lead to the entire page deadlock. Fortunately Vue provides a way to catch handling exceptions, where you can handle errors or report exceptions.",documentation:"Documento de introducción"},excel:{export:"Exportar",selectedExport:"Exportar seleccionados",placeholder:"Por favor escribe un nombre de fichero"},zip:{export:"Exportar",placeholder:"Por favor escribe un nombre de fichero"},pdf:{tips:"Here we use window.print() to implement the feature of downloading pdf."},theme:{change:"Cambiar tema",documentation:"Documentación del tema",tips:"Tips: It is different from the theme-pick on the navbar is two different skinning methods, each with different application scenarios. Refer to the documentation for details."},tagsView:{refresh:"Actualizar",close:"Cerrar",closeOthers:"Cerrar otros",closeAll:"Cerrar todos"}};a["default"].use(m["a"]);var _={en:Object(f["a"])({},C,v.a),zh:Object(f["a"])({},T,k.a),es:Object(f["a"])({},S,x.a)},D=new m["a"]({locale:g.a.get("language")||"zh",messages:_}),I=D,E=n("5c96"),P=n.n(E);n("0fae"),n("1f54");a["default"].use(P.a,{size:g.a.get("size")||"medium",i18n:function(e,t){return I.t(e,t)}}),p["a"].beforeEach(function(e,t,n){e.meta.title&&(document.title=e.meta.title),n()}),a["default"].config.productionTip=!1,new a["default"]({router:p["a"],store:d["a"],i18n:I,render:function(e){return e(u)}}).$mount("#app")},"5ea5":function(e,t,n){"use strict";var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[e.item.children?[""!=e.item.path&&" "!=e.item.path&&"-"!=e.item.path||e.item.IsButton?[e.item.IsButton?e._e():n("app-link",{key:e.item.path+"d",attrs:{to:e.item.path}},[n("el-menu-item",{key:e.item.path+"d",attrs:{index:e.isExternalLink(e.item.path)?"":e.item.path}},[n("i",{staticClass:"fa",class:e.item.iconCls}),n("template",{slot:"title"},[n("span",{staticClass:"title-name",attrs:{slot:"title"},slot:"title"},[e._v(e._s(e.item.name))])])],2)],1)]:n("el-submenu",{key:e.item.path,attrs:{index:e.item.id+"index"}},[n("template",{slot:"title"},[e.item.children&&e.item.children.length>0&&e.item.iconCls&&!e.item.IsButton?n("i",{staticClass:"fa",class:e.item.iconCls}):e._e(),n("span",{staticClass:"title-name",attrs:{slot:"title"},slot:"title"},[e._v(e._s(e.item.name))])]),e._l(e.item.children,function(t){return[t.IsHide||e.item.IsButton?e._e():[t.children&&t.children.length>0?n("sidebar",{key:t.path,attrs:{item:t,index:t.id}}):n("app-link",{key:t.path,attrs:{to:t.path}},[n("el-menu-item",{key:t.path,attrs:{index:e.isExternalLink(t.path)?"":t.path},on:{click:e.cop}},[n("i",{staticClass:"fa",class:t.iconCls}),n("template",{slot:"title"},[n("span",{staticClass:"title-name",attrs:{slot:"title"},slot:"title"},[e._v(e._s(t.name))])])],2)],1)]]})],2)]:[n("app-link",{key:e.item.path+"d",attrs:{to:e.item.path}},[n("el-menu-item",{key:e.item.path+"d",attrs:{index:e.isExternalLink(e.item.path)?"":e.item.path},on:{click:e.cop}},[n("i",{staticClass:"fa",class:e.item.iconCls}),n("template",{slot:"title"},[n("span",{staticClass:"title-name",attrs:{slot:"title"},slot:"title"},[e._v(e._s(e.item.name))])])],2)],1)]],2)},o=[],r=(n("cadf"),n("551c"),n("097d"),function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(e.type,e._b({tag:"component"},"component",e.linkProps(e.to),!1),[e._t("default")],2)}),i=[];n("6b54"),n("a745");function s(e){return/^(https?:|mailto:|tel:)/.test(e)}var c={props:{to:{type:String,required:!0}},computed:{isExternal:function(){return s(this.to)},type:function(){return this.isExternal?"a":"router-link"}},methods:{linkProps:function(e){return this.isExternal?{href:e,target:"_blank",style:"color:#fff;"}:{to:e}}}},l=c,u=n("2877"),d=Object(u["a"])(l,r,i,!1,null,null,null);d.options.__file="AppLink.vue";var p=d.exports,f={name:"Sidebar",components:{AppLink:p},props:{item:{type:Object,required:!0}},methods:{isExternalLink:function(e){return s(e)},cop:function(){this.$emit("collaFa","123")}}},m=f,h=Object(u["a"])(m,a,o,!1,null,null,null);h.options.__file="Sidebar.vue";t["a"]=h.exports},"627e":function(e,t,n){var a={"./views/403.vue":["00a5","chunk-c673e236"],"./views/404.vue":["8cdb"],"./views/APIDoc.vue":["3e70"],"./views/About.vue":["f820","chunk-2d22d746"],"./views/Blog/Blogs.vue":["7b19","chunk-40df6ae2"],"./views/Blog/Detail.vue":["ccf9","chunk-6e83591c","chunk-7287e918"],"./views/Department/Department.vue":["4ac3","chunk-735deb8e"],"./views/Form/Charts.vue":["8bd6","chunk-d726e0f8"],"./views/Form/Form.vue":["62bc","chunk-2d0cf4f3"],"./views/I18n/index.vue":["51f7","chunk-c75b8e6e"],"./views/Layout/Layout.vue":["32e93"],"./views/Login.vue":["a55b"],"./views/LoginCallbackView.vue":["86d7"],"./views/Logs/Index.vue":["9877","chunk-6e83591c","chunk-ef28925c"],"./views/Permission/Assign.vue":["5f67","chunk-47211100"],"./views/Permission/Module.vue":["1be3","chunk-c5ac0cca"],"./views/Permission/Permission.vue":["3c42","chunk-6f1c3bea"],"./views/Recursion/Menu_1/Menu_1_1/Menu_1_1_1.vue":["e1fce","chunk-77279526"],"./views/Recursion/Menu_1/Menu_1_1/Menu_1_1_2.vue":["3c96","chunk-2d0c4aa3"],"./views/Recursion/Menu_1/Menu_1_2.vue":["0790","chunk-2d0a4854"],"./views/System/My.vue":["c9a6","chunk-789b0e7e"],"./views/Task/QuartzJob.vue":["fa19","chunk-cae4df82"],"./views/TestShow/TestOne.vue":["dbaa","chunk-2d229214"],"./views/TestShow/TestTwo.vue":["d909","chunk-2d21f214"],"./views/Tibug/Bugs.vue":["9fa2","chunk-47dd42da"],"./views/User/Roles.vue":["dd68","chunk-bf843d8a"],"./views/User/Users.vue":["bfe3","chunk-479d738e"],"./views/WeChat/BindUser.vue":["c8c13","chunk-770e833a"],"./views/WeChat/Company.vue":["8a55","chunk-23e41f57"],"./views/WeChat/Manager.vue":["9c04","chunk-276b085c"],"./views/WeChat/Menu.vue":["0a92","chunk-4b6066be"],"./views/WeChat/PushLog.vue":["42e7","chunk-2d0c0c66"],"./views/WeChat/SendMessage.vue":["5b57","chunk-2d0d2f25"],"./views/WeChat/SubUser.vue":["aadd","chunk-2d213196"],"./views/WeChat/Template.vue":["6aec","chunk-2d0da5bf"],"./views/Welcome.vue":["eec5"]};function o(e){var t=a[e];return t?Promise.all(t.slice(1).map(n.e)).then(function(){var e=t[0];return n(e)}):Promise.resolve().then(function(){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t})}o.keys=function(){return Object.keys(a)},o.id="627e",e.exports=o},"64a9":function(e,t,n){},6964:function(e,t,n){"use strict";var a=n("ff0f"),o=n.n(a);o.a},"70c3":function(e,t,n){try{e.exports=function(e){return function(){return n("627e")("./views"+e+".vue")}}}catch(a){console.info("%c 如果使用 IIS 部署，请\n 1：修改api.js的base为绝对路径 \n 2：在根目录创建web.config文件，内容查看https://router.vuejs.org/zh/guide/essentials/history-mode.html \n 3：配置CORS跨域 \n ","color:blue")}},"7b4e":function(e,t,n){"use strict";var a=n("c86a"),o=n.n(a);o.a},"7e4f":function(e,t,n){"use strict";(function(e){n("cadf"),n("551c"),n("097d");e.antRouter="",e.IS_IDS4=!1}).call(this,n("c8ba"))},"86d7":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement;e._self._c;return e._m(0)},o=[function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("div",{attrs:{desktop:"12",tablet:"8"}},[n("dl",[n("dt",[e._v("Login successful")]),n("dt",[e._v("Your browser should be redirected soon")])])])])}],r=(n("a481"),n("e814")),i=n.n(r),s=n("f499"),c=n.n(s),l=(n("673e"),n("7f7f"),n("96cf"),n("3b8d")),u=n("3180"),d=n("4ec3"),p=n("a18c"),f={name:"logincallback-view",data:function(){return{}},created:function(){var e=Object(l["a"])(regeneratorRuntime.mark(function e(){var t,n,a,o;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,u["a"].signinRedirectCallback();case 3:return t=this,e.next=6,u["a"].getUser();case 6:n=e.sent,t.$store.commit("saveToken",n.access_token),a=new Date,o=new Date(a.setSeconds(a.getSeconds()+n.expires_in)),t.$store.commit("saveTokenExpire",o),window.localStorage.refreshtime=o,window.localStorage.expires_in=n.expires_in,t.$notify({type:"success",message:"成功获取令牌，项目初始化中...",duration:3e3}),n.uRealName=n.profile.name,n.uLoginName=n.profile.preferred_username,n.uID=n.profile.sub,window.localStorage.user=c()(n),n.uID>0&&t.GetNavigationBar(n.uID),e.next=24;break;case 21:e.prev=21,e.t0=e["catch"](0),this.$root.$emit("show-snackbar",{message:e.t0});case 24:case"end":return e.stop()}},e,this,[[0,21]])}));function t(){return e.apply(this,arguments)}return t}(),methods:{GetNavigationBar:function(e){var t=this,n={uid:e,t:new Date};Object(d["H"])(n).then(function(e){if(e.success){t.$message({message:"后台初始化成功",type:"success"});var n=JSON.parse(window.localStorage.user?window.localStorage.user:null);t.$notify({type:"success",message:"登录成功 \n 欢迎管理员：".concat(n.uRealName," ！Token 将在 ").concat(i()(window.localStorage.expires_in/60)," 分钟后过期！"),duration:6e3}),window.localStorage.router=c()(e.response.children);var a=e.response.children;a=Object(p["b"])(a),p["a"].$addRoutes(a),t.$router.replace(t.$route.query.redirect?t.$route.query.redirect:"/")}else t.$message({message:e.message,type:"error"})})}}},m=f,h=n("2877"),g=Object(h["a"])(m,a,o,!1,null,null,null);g.options.__file="LoginCallbackView.vue";t["default"]=g.exports},"8cdb":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("p",{staticClass:"page-container"},[e._v("没有找到你要的页面"),n("br"),e._v("你是不是迷路了?\n\n\n        "),n("router-link",{attrs:{to:"/"}},[e._v("点击返回首页 ")])],1)])},o=[],r=(n("b2b1"),n("2877")),i={},s=Object(r["a"])(i,a,o,!1,null,null,null);s.options.__file="404.vue";t["default"]=s.exports},"93b5":function(e,t,n){},9672:function(e,t,n){"use strict";var a=n("c0b7"),o=n.n(a);o.a},"9a50":function(e,t,n){},a18c:function(e,t,n){"use strict";n.d(t,"b",function(){return m}),n.d(t,"c",function(){return h});n("a481"),n("cadf"),n("551c"),n("097d");var a=n("2b0e"),o=n("8c4f"),r=n("a55b"),i=n("eec5"),s=n("3e70"),c=n("8cdb"),l=n("32e93"),u=n("86d7"),d=n("70c3");a["default"].use(o["a"]);var p=function(){return new o["a"]({mode:"hash",base:"/ui/",routes:[{path:"/404",component:c["default"],name:"NoPage",meta:{title:"NoPage",requireAuth:!1,NoTabPage:!0,NoNeedHome:!0},hidden:!0},{path:"/APIDoc",component:s["default"],name:"APIDoc",meta:{title:"APIDoc",requireAuth:!1},hidden:!0},{path:"/",component:i["default"],name:"首页",iconCls:"fa-home",meta:{title:"首页",requireAuth:!0}},{path:"/login",component:r["default"],name:"login",iconCls:"fa-address-card",meta:{title:"登录",NoTabPage:!0,NoNeedHome:!0},hidden:!0},{path:"/callback",name:"LoginCallbackView",component:u["default"],meta:{title:"登出",NoTabPage:!0},hidden:!0},{path:"*",hidden:!0,redirect:{path:"/404"}}]})},f=p();function m(e){var t=e.filter(function(e){if(e.path&&!e.IsButton)if("/"===e.path||"-"===e.path)e.component=l["default"];else try{e.component=d(e.path.replace("/:id",""))}catch(t){try{e.component=function(){return n("627e")("./views"+e.path.replace("/:id","")+".vue")}}catch(a){console.info("%c 当前路由 "+e.path.replace("/:id","")+".vue 不存在，因此如法导入组件，请检查接口数据和组件是否匹配，并重新登录，清空缓存!","color:red")}}return e.children&&e.children.length&&!e.IsButton&&(e.children=m(e.children)),!0});return t}function h(){var e=p();f.matcher=e.matcher}f.$addRoutes=function(e){var t=function e(t){return t["children"]?(t["children"]=t["children"].filter(e),!0):!t["IsButton"]||!1===t["IsButton"]};e=e.filter(t);f.addRoutes(e)},t["a"]=f},a55b:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"wrapper"},[n("ul",{staticClass:"bg-bubbles"},[e._l(10,function(e){return n("li",{key:e+"n"})}),e._l(5,function(e){return n("ol",{key:e+"m"})})],2),n("div",{staticClass:"bg bg-blur",staticStyle:{display:"none"}}),n("div",{staticStyle:{height:"10%"}}),n("el-form",{ref:"ruleForm2",staticClass:"demo-ruleForm login-container",attrs:{model:e.ruleForm2,rules:e.rules2,"label-position":"left","label-width":"0px"}},[n("h3",{staticClass:"title"},[e._v("系统登录")]),n("el-form-item",{attrs:{prop:"account"}},[n("el-input",{attrs:{type:"text","auto-complete":"off",placeholder:"账号"},model:{value:e.ruleForm2.account,callback:function(t){e.$set(e.ruleForm2,"account",t)},expression:"ruleForm2.account"}})],1),n("el-form-item",{attrs:{prop:"checkPass"}},[n("el-input",{attrs:{"auto-complete":"off","show-password":"",placeholder:"密码"},model:{value:e.ruleForm2.checkPass,callback:function(t){e.$set(e.ruleForm2,"checkPass",t)},expression:"ruleForm2.checkPass"}})],1),n("el-checkbox",{staticClass:"remember",attrs:{checked:""},model:{value:e.checked,callback:function(t){e.checked=t},expression:"checked"}},[e._v("记住密码")]),n("div",{staticClass:"count-test",staticStyle:{"margin-bottom":"20px"}},[n("el-radio-group",{on:{change:e.loginAccount},model:{value:e.account3,callback:function(t){e.account3=t},expression:"account3"}},[n("el-radio-button",{attrs:{label:"测试账号1"}}),n("el-radio-button",{attrs:{label:"测试账号2"}}),n("el-radio-button",{attrs:{label:"超级管理员"}})],1)],1),n("el-form-item",{staticStyle:{width:"100%"}},[n("el-button",{staticStyle:{width:"100%"},attrs:{type:"primary",loading:e.logining},nativeOn:{click:function(t){return t.preventDefault(),e.handleSubmit2(t)}}},[e._v("\n                "+e._s(e.loginStr)+"\n            ")])],1),n("el-form-item",{staticStyle:{width:"100%"}},[n("el-button",{staticStyle:{width:"100%"},attrs:{loading:e.loginingMock},nativeOn:{click:function(t){return t.preventDefault(),e.handleSubmitMock(t)}}},[e._v("Mock登录\n            ")])],1)],1)],1)},o=[],r=(n("a481"),n("f499")),i=n.n(r),s=(n("cadf"),n("551c"),n("097d"),n("4ec3")),c=n("a18c"),l={data:function(){return{instance:"",loginStr:"登录",logining:!1,loginingMock:!1,ruleForm2:{account:"test",checkPass:"test"},account3:"测试账号1",rules2:{account:[{required:!0,message:"请输入账号",trigger:"blur"}],checkPass:[{required:!0,message:"请输入密码",trigger:"blur"}]},checked:!0}},methods:{handleReset2:function(){this.$refs.ruleForm2.resetFields()},loginAccount:function(){"测试账号1"==this.account3?(this.ruleForm2.account="test",this.ruleForm2.checkPass="test"):"测试账号2"==this.account3?(this.ruleForm2.account="test2",this.ruleForm2.checkPass="test2"):(this.ruleForm2.account="blogadmin",this.ruleForm2.checkPass="blogadmin")},handleSubmitMock:function(e){var t=this,n=this;this.$refs.ruleForm2.validate(function(e){if(!e)return console.log("error submit!!"),!1;t.loginingMock=!0;var a={username:t.ruleForm2.account,password:t.ruleForm2.checkPass};Object(s["pb"])(a).then(function(e){t.loginingMock=!1,e&&e.code&&e.msg?n.$message({message:e.code+e.msg+"，用户名admin,密码123456",type:"error"}):(n.$message({message:"测试mock，请在main.js 中开启服务!",type:"error"}),console.info("%c 测试mock，请在main.js 中开启服务!","color:red"))})})},openAlert:function(e){this.instance=this.$notify({title:"提示",message:e,duration:0,position:"top-left"})},closeAlert:function(){this.instance.close()},handleSubmit2:function(e){var t=this,n=this;this.$refs.ruleForm2.validate(function(e){if(!e)return console.log("error submit!!"),!1;t.logining=!0;var a={name:t.ruleForm2.account,pass:t.ruleForm2.checkPass};n.loginStr="登录中...",Object(s["ob"])(a).then(function(e){if(e.success){var t=e.response.token;n.$store.commit("saveToken",t);var a=new Date,o=new Date(a.setSeconds(a.getSeconds()+e.response.expires_in));n.$store.commit("saveTokenExpire",o),window.localStorage.refreshtime=o,window.localStorage.expires_in=e.response.expires_in,n.$notify({type:"success",message:"成功获取令牌，项目初始化中...",duration:3e3}),n.loginStr="成功获取Token，等待服务器返回用户信息...",n.getUserInfoByToken(t)}else n.$message({message:e.msg,type:"error"}),n.logining=!1,n.loginStr="重新登录"}).catch(function(e){n.logining=!1,n.loginStr="重新登录"})})},getUserInfoByToken:function(e){var t=this,n={token:e};Object(s["Q"])(n).then(function(e){e.success?(t.loginStr="接收到用户数据，开始初始化路由树...",window.localStorage.user=i()(e.response),e.response.uID>0&&t.GetNavigationBar(e.response.uID)):t.$message({message:e.msg,type:"error"})})},GetNavigationBar:function(e){var t=this,n={uid:e,t:new Date};Object(s["H"])(n).then(function(e){if(t.logining=!1,e.success){t.$message({message:"后台初始化成功",type:"success"});var n=JSON.parse(window.localStorage.user?window.localStorage.user:null);t.$notify({type:"success",message:"登录成功 \n 欢迎管理员：".concat(n.uRealName," ！Token 将在 ").concat(window.localStorage.expires_in/60," 分钟后过期！"),duration:6e3}),window.localStorage.router=i()(e.response.children);var a=e.response.children;a=Object(c["b"])(a),c["a"].$addRoutes(a),t.$router.replace(t.$route.query.redirect?t.$route.query.redirect:"/")}else t.$message({message:e.msg,type:"error"})})}},mounted:function(){console.info("%c 本地缓存已清空!","color:green")}},u=l,d=(n("d6db"),n("2877")),p=Object(d["a"])(u,a,o,!1,null,null,null);p.options.__file="Login.vue";t["default"]=p.exports},a9e7:function(e,t,n){},b0a0:function(e,t,n){"use strict";var a=n("07bb"),o=n.n(a);o.a},b2b1:function(e,t,n){"use strict";var a=n("d3f3"),o=n.n(a);o.a},c0b7:function(e,t,n){},c0d6:function(e,t,n){"use strict";var a=n("2b0e"),o=n("2f62"),r=n("a78e"),i=n.n(r);a["default"].use(o["a"]),t["a"]=new o["a"].Store({state:{token:null,tokenExpire:null,tagsStoreList:[],language:i.a.get("language")||"en"},mutations:{saveToken:function(e,t){e.token=t,window.localStorage.setItem("Token",t)},saveTokenExpire:function(e,t){e.tokenExpire=t,window.localStorage.setItem("TokenExpire",t)},saveTagsData:function(e,t){e.tagsStoreList=t,sessionStorage.setItem("Tags",t)},SET_LANGUAGE:function(e,t){e.language=t,i.a.set("language",t)}},actions:{setLanguage:function(e,t){var n=e.commit;n("SET_LANGUAGE",t)}}})},c86a:function(e,t,n){},c8c1:function(e,t,n){"use strict";var a=n("93b5"),o=n.n(a);o.a},cdc6:function(e,t,n){"use strict";(function(e){n.d(t,"a",function(){return v});n("ac6a");var a,o=n("f499"),r=n.n(o),i=n("cebc"),s=(n("7f7f"),n("a18c")),c=n("4ec3"),l=n("c0d6"),u=n("3180");if(!a)if(g("router"))console.info("%c get navigation bar from localStorage succeed!","color:green"),a=g("router"),a=Object(s["b"])(a),s["a"].$addRoutes(a),e.antRouter=a;else{var d=window.localStorage.user?JSON.parse(window.localStorage.user):null;if(d&&d.uID>0){console.info(d.uID);var p={uid:d.uID};Object(c["H"])(p).then(function(e){e.success&&(console.info("%c get navigation bar from api succeed!","color:red"),a=e.response.children,h("router",a))})}}var f=l["a"];function m(t,n){a=Object(s["b"])(a),Object(s["c"])(),s["a"].$addRoutes(a),e.antRouter=a,n(Object(i["a"])({},t,{replace:!0}))}function h(e,t){localStorage.setItem(e,r()(t))}function g(e){return JSON.parse(window.localStorage.getItem(e))}s["a"].beforeEach(function(t,n,o){if(f.state.token||f.commit("saveToken",window.localStorage.Token),f.state.tokenExpire||f.commit("saveTokenExpire",window.localStorage.TokenExpire),Object(c["rb"])(),t.meta.requireAuth){new Date,new Date(Date.parse(window.localStorage.TokenExpire));f.state.token&&"undefined"!=f.state.token?(console.log(1),o()):(l["a"].commit("saveToken",""),l["a"].commit("saveTokenExpire",""),l["a"].commit("saveTagsData",""),window.localStorage.removeItem("user"),window.localStorage.removeItem("NavigationBar"),window.localStorage.removeItem("router"),e.IS_IDS4?u["a"].login():o({path:"/login",query:{redirect:t.fullPath}}))}else console.log(2),o();if(a)t.name&&"login"!=t.name&&(a=g("router"),e.antRouter=a);else if(g("router"))a=g("router"),m(t,o);else{var r=window.localStorage.user?JSON.parse(window.localStorage.user):null;if(r&&r.uID>0){var i={uid:r.uID};Object(c["H"])(i).then(function(e){console.log("router before each get navigation bar from api succeed!"),e.success&&(a=e.response.children,h("router",a),m(t,o))})}}});var b=[],v=function e(t,n){return n.forEach(function(n){if(t&&n.path){var a=t.toLowerCase();if(n.path&&n.path.toLowerCase()===a)return void(b=n.children);n.children&&e(a,n.children)}}),b}}).call(this,n("c8ba"))},cf05:function(e,t){e.exports="data:image/png;base64,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"},d38f:function(e,t,n){"use strict";var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-scrollbar",{ref:"scrollContainer",staticClass:"scroll-container",attrs:{vertical:!1},nativeOn:{wheel:function(t){return t.preventDefault(),e.handleScroll(t)}}},[e._t("default")],2)},o=[],r=(n("20d6"),n("cadf"),n("551c"),n("097d"),4),i={name:"ScrollPane",data:function(){return{left:0}},computed:{scrollWrapper:function(){return this.$refs.scrollContainer.$refs.wrap}},methods:{handleScroll:function(e){var t=e.wheelDelta||40*-e.deltaY,n=this.scrollWrapper;n.scrollLeft=n.scrollLeft+t/4},moveToTarget:function(e,t){var n=this.$refs.scrollContainer.$el,a=n.offsetWidth,o=this.scrollWrapper,i=null,s=null;if(t.length>0&&(i=t[0],s=t[t.length-1]),i===e)o.scrollLeft=0;else if(s===e)o.scrollLeft=o.scrollWidth-a;else{var c=t.findIndex(function(t){return t===e}),l=t[c-1],u=t[c+1],d=u.$el.offsetLeft+u.$el.offsetWidth+r,p=l.$el.offsetLeft-r;d>o.scrollLeft+a?o.scrollLeft=d-a:p<o.scrollLeft&&(o.scrollLeft=p)}}}},s=i,c=(n("d7ef"),n("2877")),l=Object(c["a"])(s,a,o,!1,null,null,null);l.options.__file="ScrollPane.vue";t["a"]=l.exports},d3f3:function(e,t,n){},d6db:function(e,t,n){"use strict";var a=n("a9e7"),o=n.n(a);o.a},d7ef:function(e,t,n){"use strict";var a=n("38cf"),o=n.n(a);o.a},dc82:function(e,t,n){"use strict";var a=n("9a50"),o=n.n(a);o.a},eec5:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticStyle:{"margin-top":"30px"}},[n("el-row",{staticClass:"panel-group"},[n("el-col",{staticClass:"card-panel-col",staticStyle:{float:"left",width:"calc(100% - 405px)",margin:"0"}},[n("el-card",{staticClass:"welcome-card activeuser note"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("span",[e._v("今日活跃用户")])]),n("div",{staticClass:"bg-color-sub",staticStyle:{background:"rgb(236, 245, 255) none repeat scroll 0% 0%"}},e._l(e.welcomeInitData.activeUsers,function(t,a){return n("div",{key:t.user+a,staticClass:"bg-blue-sub-item",style:e.getBck(a)},[n("el-badge",{staticClass:"item",attrs:{value:t.count>9999?"9999+":t.count,type:e.getTypeName(t.count)}},[n("label",{staticClass:"acc-user",attrs:{title:t.user}},[e._v(e._s(t.user))])])],1)}),0)])],1),n("div",{staticClass:"statistical-cus"},[n("el-col",{staticClass:"card-panel-col"},[n("div",{staticClass:"card-panel"},[n("div",{staticClass:"card-panel-description"},[n("div",{staticClass:"card-panel-text"},[e._v("今日活跃")]),n("span",{staticClass:"card-acuser-num",attrs:{"data-v-6723c96e":""}},[e._v(e._s(e.welcomeInitData.activeUserCount>9?e.welcomeInitData.activeUserCount:"0"+e.welcomeInitData.activeUserCount))])])])]),n("el-col",{staticClass:"card-panel-col"},[n("div",{staticClass:"card-panel"},[n("div",{staticClass:"card-panel-description"},[n("div",{staticClass:"card-panel-text"},[e._v("今日新增")]),n("span",{staticClass:"card-acuser-num",attrs:{"data-v-6723c96e":""}},[e._v(e._s(e.lineChartDataIDS4.today>9?e.lineChartDataIDS4.today:"0"+e.lineChartDataIDS4.today))])])])]),n("el-col",{staticClass:"card-panel-col"},[n("div",{staticClass:"card-panel extoday",on:{click:e.toLogs}},[n("div",{staticClass:"card-panel-description"},[n("div",{staticClass:"card-panel-text"},[e._v("今日异常")]),n("span",{staticClass:"card-panel-num",attrs:{"data-v-6723c96e":""}},[e._v(e._s(e.welcomeInitData.errorCount>9?e.welcomeInitData.errorCount:"0"+e.welcomeInitData.errorCount))])])])])],1)],1),n("el-row",{staticClass:"panel-group"},[n("el-col",{staticClass:"card-panel-col",staticStyle:{float:"left",width:"100%",margin:"0"}},[n("el-card",{staticClass:"welcome-card activeuser note"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("span",[e._v("本月活跃用户"),n("span",{staticStyle:{color:"#ccc","font-size":"14px"}},[e._v("（使用任务调度，1分钟统计一次）")])])]),n("div",{staticClass:"bg-color-sub",staticStyle:{background:"rgb(236, 245, 255) none repeat scroll 0% 0%"}},e._l(e.welcomeInitData.activeCount,function(t,a){return n("div",{key:t.user+a,staticClass:"bg-blue-sub-item-m",class:t.count>9999?"amazing":"",style:e.getBck(a)},[n("el-badge",{staticClass:"item",attrs:{value:t.count>999999?"999999+":t.count,type:e.getTypeName(t.count)}},[n("label",{staticClass:"acc-user",attrs:{title:t.user}},[e._v(e._s(t.user))])])],1)}),0)])],1)],1),n("el-card",{staticClass:"welcome-card note note50",staticStyle:{width:"calc(49% - 10px)","margin-right":"10px"}},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("span",[e._v("操作指南")])]),n("div",{staticClass:"text item"},[n("i",{staticClass:"el-icon-edit"}),e._v("、在vue.config.js中配置项目端口号，以及代理后端API项目域名。\n    ")]),n("div",{staticClass:"text item"},[n("i",{staticClass:"el-icon-edit"}),e._v("、在global.js中配置授权方案global.IS_IDS4。\n    ")]),n("div",{staticClass:"text item"},[n("i",{staticClass:"el-icon-edit"}),e._v("、动态添加页面以及权限配置，看右侧两个动图。\n    ")]),n("div",{staticClass:"text item"},[n("i",{staticClass:"el-icon-edit"}),e._v("、更多内容，查看官方文档：\n      "),n("a",{attrs:{href:"http://vueadmin.neters.club/.doc/",target:"_blank"}},[e._v("http://vueadmin.neters.club/.doc/")]),e._v("。\n    ")])]),n("el-card",{staticClass:"welcome-card note50",staticStyle:{width:"49%",margin:"0","font-size":"14px"}},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("span",{staticStyle:{"font-size":"16px"}},[e._v("服务器")])]),n("div",{staticClass:"text item"},[e._v("环境变量："+e._s(e.serverInfo.EnvironmentName))]),n("div",{staticClass:"text item"},[e._v("系统架构："+e._s(e.serverInfo.OSArchitecture))]),n("div",{staticClass:"text item"},[e._v("\n      ContentRootPath："+e._s(e.serverInfo.ContentRootPath)+"\n    ")]),n("div",{staticClass:"text item"},[e._v("WebRootPath："+e._s(e.serverInfo.WebRootPath))]),n("div",{staticClass:"text item"},[e._v("\n      .NET Core版本："+e._s(e.serverInfo.FrameworkDescription)+"\n    ")]),n("div",{staticClass:"text item"},[e._v("内存占用："+e._s(e.serverInfo.MemoryFootprint))]),n("div",{staticClass:"text item"},[e._v("启动时间："+e._s(e.serverInfo.WorkingTime))]),n("div",[n("br")])]),n("el-card",{staticClass:"welcome-card note",staticStyle:{width:"98%","margin-top":"20px"}},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("span",[e._v("30天用户注册曲线图")])]),n("el-col",{staticClass:"echarts-item",attrs:{span:24}},[n("ve-line",{attrs:{data:e.lineChartDataIDS4,extend:e.extend,settings:e.lineChartSettings7Day,"mark-point":e.lineChartMarkPoint}})],1)],1),n("el-card",{staticClass:"welcome-card",staticStyle:{"margin-top":"20px",width:"98%"}},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("span",[e._v("\n        访问日志\n        "),n("span",{staticStyle:{"font-size":"12px"}},[e._v("(Top 50 desc)")])])]),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%","font-size":"12px"},attrs:{data:e.welcomeInitData.logs,"highlight-current-row":"",border:""}},[n("el-table-column",{attrs:{prop:"User",label:"访问者",width:"150px",sortable:""}}),n("el-table-column",{attrs:{prop:"IP",label:"请求地址",width:"150px"}}),n("el-table-column",{attrs:{prop:"BeginTime",label:"请求时间",width:"150px"}}),n("el-table-column",{attrs:{prop:"API",label:"访问接口",width:""}}),n("el-table-column",{attrs:{prop:"RequestMethod",label:"Method",width:"100px"}}),n("el-table-column",{attrs:{prop:"OPTime",label:"响应时长",width:"100px"}}),n("el-table-column",{attrs:{prop:"RequestData",label:"参数",width:""}}),n("el-table-column",{attrs:{prop:"Agent",label:"Agent",width:"80","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[n("div",{staticStyle:{"text-decoration":"underline",cursor:"pointer"}},[e._v("\n            "+e._s(t.row.Agent)+"\n          ")])]}}])})],1),n("br")],1),n("el-card",{staticClass:"welcome-card",staticStyle:{"margin-top":"20px",width:"98%"}},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("span",[e._v("相关配置")])]),n("el-aside",[e._v("1、动态添加一个vue页面：")]),n("br"),n("div",{staticClass:"text item"},[n("i",{staticClass:"el-icon-edit"}),e._v("、更多内容，查看博客园文档：\n      "),n("a",{attrs:{href:"https://www.cnblogs.com/laozhang-is-phi/p/10643993.html#autoid-2-6-0",target:"_blank"}},[e._v("https://www.cnblogs.com/laozhang-is-phi/p/10643993.html#autoid-2-6-0")]),e._v("。\n    ")]),n("br"),n("hr"),n("br"),n("el-aside",[e._v("2、快速配置接口权限：")]),n("br"),n("div",{staticStyle:{height:"300px","overflow-y":"auto"}},[n("el-steps",{attrs:{direction:"vertical"}},[n("el-step",{attrs:{title:"步骤 1",description:"创建一个测试控制器 DemoController"}}),n("el-step",{attrs:{title:"步骤 2",description:"修改接口路由地址，带上 [action] ，比如，/api/[controller]/[action]，编译是否正常"}}),n("el-step",{attrs:{title:"步骤 3",description:"给需要加权限的路由api上，增加授权特性[[Authorize(Permissions.Name)]]"}}),n("el-step",{attrs:{title:"步骤 4",description:"测试 /api/demo/get 接口，是否已经被保护"}}),n("el-step",{attrs:{title:"步骤 5.1",description:"vueadmin 后台 配置权限：第一步：登录后台，新建api接口"}}),n("el-step",{attrs:{title:"步骤 5.2",description:"第二步：添加一个菜单，可以是一个查询按钮，也可以是一个路由页面"}}),n("el-step",{attrs:{title:"步骤 5.3",description:"第三步：权限分配！勾选角色和刚刚的菜单"}}),n("el-step",{attrs:{title:"步骤 6",description:"如果后端netcore资源服务器有缓存，记得清理"}}),n("el-step",{attrs:{title:"步骤 7",description:"重新登录Admin管理后台，访问接口，查看是否有权限"}})],1)],1),n("br")],1)],1)},o=[],r=n("55ad"),i=r["a"],s=(n("c8c1"),n("7b4e"),n("2877")),c=Object(s["a"])(i,a,o,!1,null,"11b2dad1",null);c.options.__file="Welcome.vue";t["default"]=c.exports},ff0f:function(e,t,n){}});
//# sourceMappingURL=app.d6fdef78.js.map