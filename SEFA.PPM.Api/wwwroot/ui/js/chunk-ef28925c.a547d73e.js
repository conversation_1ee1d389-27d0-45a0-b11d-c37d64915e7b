(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ef28925c"],{2183:function(t,e,n){"use strict";n.d(e,"a",function(){return r});var r=function(){function t(){}return t.write=function(e){return""+e+t.RecordSeparator},t.parse=function(e){if(e[e.length-1]!==t.RecordSeparator)throw new Error("Message is incomplete.");var n=e.split(t.RecordSeparator);return n.pop(),n},t.RecordSeparatorCode=30,t.RecordSeparator=String.fromCharCode(t.RecordSeparatorCode),t}()},"2b85":function(t,e,n){},"2ced":function(t,e,n){"use strict";(function(t){n.d(e,"a",function(){return i});var r=n("2183"),o=n("c9f7"),i=function(){function e(){}return e.prototype.writeHandshakeRequest=function(t){return r["a"].write(JSON.stringify(t))},e.prototype.parseHandshakeResponse=function(e){var n,i,s;if(Object(o["f"])(e)||"undefined"!==typeof t&&e instanceof t){var a=new Uint8Array(e),c=a.indexOf(r["a"].RecordSeparatorCode);if(-1===c)throw new Error("Message is incomplete.");var u=c+1;i=String.fromCharCode.apply(null,a.slice(0,u)),s=a.byteLength>u?a.slice(u).buffer:null}else{var l=e;c=l.indexOf(r["a"].RecordSeparator);if(-1===c)throw new Error("Message is incomplete.");u=c+1;i=l.substring(0,u),s=l.length>u?l.substring(u):null}var h=r["a"].parse(i),p=JSON.parse(h[0]);if(p.type)throw new Error("Expected a handshake response from the server.");return n=p,[s,n]},e}()}).call(this,n("b639").Buffer)},"386d":function(t,e,n){"use strict";var r=n("cb7c"),o=n("83a1"),i=n("5f1b");n("214f")("search",1,function(t,e,n,s){return[function(n){var r=t(this),o=void 0==n?void 0:n[e];return void 0!==o?o.call(n,r):new RegExp(n)[e](String(r))},function(t){var e=s(n,t,this);if(e.done)return e.value;var a=r(t),c=String(this),u=a.lastIndex;o(u,0)||(a.lastIndex=0);var l=i(a,c);return o(a.lastIndex,u)||(a.lastIndex=u),null===l?-1:l.index}]})},"3aae":function(t,e,n){"use strict";(function(t){n.d(e,"a",function(){return h});var r,o=n("c5fb"),i=n("83fd"),s=n("7835"),a=n("c9f7"),c=function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),u=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n],e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t};if("undefined"===typeof XMLHttpRequest){var l=require;r=l("request")}var h=function(e){function n(t){var n=e.call(this)||this;if("undefined"===typeof r)throw new Error("The 'request' module could not be loaded.");return n.logger=t,n.cookieJar=r.jar(),n.request=r.defaults({jar:n.cookieJar}),n}return c(n,e),n.prototype.send=function(e){var n=this;return new Promise(function(r,c){var l;l=Object(a["f"])(e.content)?t.from(e.content):e.content||"";var h=n.request(e.url,{body:l,encoding:"arraybuffer"===e.responseType?null:"utf8",headers:u({"X-Requested-With":"XMLHttpRequest"},e.headers),method:e.method,timeout:e.timeout},function(t,a,u){if(e.abortSignal&&(e.abortSignal.onabort=null),t)return"ETIMEDOUT"===t.code&&(n.logger.log(s["a"].Warning,"Timeout from HTTP request."),c(new o["c"])),n.logger.log(s["a"].Warning,"Error from HTTP request. "+t),void c(t);a.statusCode>=200&&a.statusCode<300?r(new i["b"](a.statusCode,a.statusMessage||"",u)):c(new o["b"](a.statusMessage||"",a.statusCode||0))});e.abortSignal&&(e.abortSignal.onabort=function(){h.abort(),c(new o["a"])})})},n.prototype.getCookieString=function(t){return this.cookieJar.getCookieString(t)},n}(i["a"])}).call(this,n("b639").Buffer)},"3b2b":function(t,e,n){var r=n("7726"),o=n("5dbc"),i=n("86cc").f,s=n("9093").f,a=n("aae3"),c=n("0bfb"),u=r.RegExp,l=u,h=u.prototype,p=/a/g,f=/a/g,g=new u(p)!==p;if(n("9e1e")&&(!g||n("79e5")(function(){return f[n("2b4c")("match")]=!1,u(p)!=p||u(f)==f||"/a/i"!=u(p,"i")}))){u=function(t,e){var n=this instanceof u,r=a(t),i=void 0===e;return!n&&r&&t.constructor===u&&i?t:o(g?new l(r&&!i?t.source:t,e):l((r=t instanceof u)?t.source:t,r&&i?c.call(t):e),n?this:h,u)};for(var d=function(t){t in u||i(u,t,{configurable:!0,get:function(){return l[t]},set:function(e){l[t]=e}})},v=s(l),b=0;v.length>b;)d(v[b++]);h.constructor=u,u.prototype=h,n("2aba")(r,"RegExp",u)}n("7a56")("RegExp")},"482f":function(t,e,n){"use strict";var r=n("2b85"),o=n.n(r);o.a},4917:function(t,e,n){"use strict";var r=n("cb7c"),o=n("9def"),i=n("0390"),s=n("5f1b");n("214f")("match",1,function(t,e,n,a){return[function(n){var r=t(this),o=void 0==n?void 0:n[e];return void 0!==o?o.call(n,r):new RegExp(n)[e](String(r))},function(t){var e=a(n,t,this);if(e.done)return e.value;var c=r(t),u=String(this);if(!c.global)return s(c,u);var l=c.unicode;c.lastIndex=0;var h,p=[],f=0;while(null!==(h=s(c,u))){var g=String(h[0]);p[f]=g,""===g&&(c.lastIndex=i(u,o(c.lastIndex),l)),f++}return 0===f?null:p}]})},7835:function(t,e,n){"use strict";var r;n.d(e,"a",function(){return r}),function(t){t[t["Trace"]=0]="Trace",t[t["Debug"]=1]="Debug",t[t["Information"]=2]="Information",t[t["Warning"]=3]="Warning",t[t["Error"]=4]="Error",t[t["Critical"]=5]="Critical",t[t["None"]=6]="None"}(r||(r={}))},"7b74":function(t,e,n){"use strict";n.d(e,"a",function(){return r});var r=function(){function t(){}return t.prototype.log=function(t,e){},t.instance=new t,t}()},"83a1":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},"83fd":function(t,e,n){"use strict";n.d(e,"b",function(){return o}),n.d(e,"a",function(){return i});var r=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n],e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},o=function(){function t(t,e,n){this.statusCode=t,this.statusText=e,this.content=n}return t}(),i=function(){function t(){}return t.prototype.get=function(t,e){return this.send(r({},e,{method:"GET",url:t}))},t.prototype.post=function(t,e){return this.send(r({},e,{method:"POST",url:t}))},t.prototype.delete=function(t,e){return this.send(r({},e,{method:"DELETE",url:t}))},t.prototype.getCookieString=function(t){return""},t}()},9877:function(t,e,n){"use strict";n.r(e);var r,o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("section",[n("div",{staticStyle:{display:"none1"}},[n("el-form",{ref:"form",staticStyle:{margin:"20px",width:"60%","min-width":"600px"},attrs:{"label-width":"80px"},on:{submit:function(e){return e.preventDefault(),t.onSubmit(e)}}},[n("el-form-item",{attrs:{label:"用户名"}},[n("el-input",{model:{value:t.userName,callback:function(e){t.userName=e},expression:"userName"}})],1),n("el-form-item",{attrs:{label:"密码"}},[n("el-input",{model:{value:t.userMessage,callback:function(e){t.userMessage=e},expression:"userMessage"}})],1)],1),t._l(t.messages,function(e,r){return n("ul",{key:r+"itemMessage"},[n("li",[n("b",[t._v("Name: ")]),t._v(t._s(e.user))]),n("li",[n("b",[t._v("Message: ")]),t._v(t._s(e.message))])])}),n("el-button",{attrs:{type:"primary"},on:{click:t.submitCard}},[t._v("登录")]),n("el-button",{attrs:{type:"primary"},on:{click:t.getLogs}},[t._v("查询")])],2),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData}},[n("el-table-column",{attrs:{type:"expand"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-form",{staticClass:"demo-table-expand",attrs:{"label-position":"left",inline:""}},[n("el-form-item",{attrs:{label:"Datetime"}},[n("span",[t._v(t._s(e.row.datetime))])]),n("el-form-item",{attrs:{label:"Content"}},[n("span",{domProps:{innerHTML:t._s(e.row.content)}})])],1)]}}])}),n("el-table-column",{attrs:{label:"Datetime",prop:"datetime"}}),n("el-table-column",{attrs:{label:"Content"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",{class:e.row.logColor,domProps:{innerHTML:t._s(e.row.content)}})]}}])})],1)],1)},i=[],s=n("a6dc"),a=n("4ec3"),c=n("c5fb"),u=n("83fd"),l=n("3aae"),h=n("7835"),p=function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),f=function(t){function e(e){var n=t.call(this)||this;return n.logger=e,n}return p(e,t),e.prototype.send=function(t){var e=this;return t.abortSignal&&t.abortSignal.aborted?Promise.reject(new c["a"]):t.method?t.url?new Promise(function(n,r){var o=new XMLHttpRequest;o.open(t.method,t.url,!0),o.withCredentials=!0,o.setRequestHeader("X-Requested-With","XMLHttpRequest"),o.setRequestHeader("Content-Type","text/plain;charset=UTF-8");var i=t.headers;i&&Object.keys(i).forEach(function(t){o.setRequestHeader(t,i[t])}),t.responseType&&(o.responseType=t.responseType),t.abortSignal&&(t.abortSignal.onabort=function(){o.abort(),r(new c["a"])}),t.timeout&&(o.timeout=t.timeout),o.onload=function(){t.abortSignal&&(t.abortSignal.onabort=null),o.status>=200&&o.status<300?n(new u["b"](o.status,o.statusText,o.response||o.responseText)):r(new c["b"](o.statusText,o.status))},o.onerror=function(){e.logger.log(h["a"].Warning,"Error from HTTP request. "+o.status+": "+o.statusText+"."),r(new c["b"](o.statusText,o.status))},o.ontimeout=function(){e.logger.log(h["a"].Warning,"Timeout from HTTP request."),r(new c["c"])},o.send(t.content||"")}):Promise.reject(new Error("No url defined.")):Promise.reject(new Error("No method defined."))},e}(u["a"]),g=function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),d=function(t){function e(e){var n=t.call(this)||this;return"undefined"!==typeof XMLHttpRequest?n.httpClient=new f(e):n.httpClient=new l["a"](e),n}return g(e,t),e.prototype.send=function(t){return t.abortSignal&&t.abortSignal.aborted?Promise.reject(new c["a"]):t.method?t.url?this.httpClient.send(t):Promise.reject(new Error("No url defined.")):Promise.reject(new Error("No method defined."))},e.prototype.getCookieString=function(t){return this.httpClient.getCookieString(t)},e}(u["a"]),v=n("2ced");(function(t){t[t["Invocation"]=1]="Invocation",t[t["StreamItem"]=2]="StreamItem",t[t["Completion"]=3]="Completion",t[t["StreamInvocation"]=4]="StreamInvocation",t[t["CancelInvocation"]=5]="CancelInvocation",t[t["Ping"]=6]="Ping",t[t["Close"]=7]="Close"})(r||(r={}));var b,y=n("c9f7"),m=function(t,e,n,r){return new(n||(n=Promise))(function(o,i){function s(t){try{c(r.next(t))}catch(e){i(e)}}function a(t){try{c(r["throw"](t))}catch(e){i(e)}}function c(t){t.done?o(t.value):new n(function(e){e(t.value)}).then(s,a)}c((r=r.apply(t,e||[])).next())})},w=function(t,e){var n,r,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(t){return function(e){return c([t,e])}}function c(i){if(n)throw new TypeError("Generator is already executing.");while(s)try{if(n=1,r&&(o=2&i[0]?r["return"]:i[0]?r["throw"]||((o=r["return"])&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,r=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(o=s.trys,!(o=o.length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=e.call(t,s)}catch(a){i=[6,a],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}},S=3e4,k=15e3;(function(t){t[t["Disconnected"]=0]="Disconnected",t[t["Connected"]=1]="Connected"})(b||(b={}));var C,E,T=function(){function t(t,e,n){var o=this;y["a"].isRequired(t,"connection"),y["a"].isRequired(e,"logger"),y["a"].isRequired(n,"protocol"),this.serverTimeoutInMilliseconds=S,this.keepAliveIntervalInMilliseconds=k,this.logger=e,this.protocol=n,this.connection=t,this.handshakeProtocol=new v["a"],this.connection.onreceive=function(t){return o.processIncomingData(t)},this.connection.onclose=function(t){return o.connectionClosed(t)},this.callbacks={},this.methods={},this.closedCallbacks=[],this.id=0,this.receivedHandshakeResponse=!1,this.connectionState=b.Disconnected,this.cachedPingMessage=this.protocol.writeMessage({type:r.Ping})}return t.create=function(e,n,r){return new t(e,n,r)},Object.defineProperty(t.prototype,"state",{get:function(){return this.connectionState},enumerable:!0,configurable:!0}),t.prototype.start=function(){return m(this,void 0,void 0,function(){var t,e,n=this;return w(this,function(r){switch(r.label){case 0:return t={protocol:this.protocol.name,version:this.protocol.version},this.logger.log(h["a"].Debug,"Starting HubConnection."),this.receivedHandshakeResponse=!1,e=new Promise(function(t,e){n.handshakeResolver=t,n.handshakeRejecter=e}),[4,this.connection.start(this.protocol.transferFormat)];case 1:return r.sent(),this.logger.log(h["a"].Debug,"Sending handshake request."),[4,this.sendMessage(this.handshakeProtocol.writeHandshakeRequest(t))];case 2:return r.sent(),this.logger.log(h["a"].Information,"Using HubProtocol '"+this.protocol.name+"'."),this.cleanupTimeout(),this.resetTimeoutPeriod(),this.resetKeepAliveInterval(),[4,e];case 3:return r.sent(),this.connectionState=b.Connected,[2]}})})},t.prototype.stop=function(){return this.logger.log(h["a"].Debug,"Stopping HubConnection."),this.cleanupTimeout(),this.cleanupPingTimer(),this.connection.stop()},t.prototype.stream=function(t){for(var e=this,n=[],o=1;o<arguments.length;o++)n[o-1]=arguments[o];var i,s=this.createStreamInvocation(t,n),a=new y["c"];a.cancelCallback=function(){var t=e.createCancelInvocation(s.invocationId),n=e.protocol.writeMessage(t);return delete e.callbacks[s.invocationId],i.then(function(){return e.sendMessage(n)})},this.callbacks[s.invocationId]=function(t,e){e?a.error(e):t&&(t.type===r.Completion?t.error?a.error(new Error(t.error)):a.complete():a.next(t.item))};var c=this.protocol.writeMessage(s);return i=this.sendMessage(c).catch(function(t){a.error(t),delete e.callbacks[s.invocationId]}),a},t.prototype.sendMessage=function(t){return this.resetKeepAliveInterval(),this.connection.send(t)},t.prototype.send=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];var r=this.createInvocation(t,e,!0),o=this.protocol.writeMessage(r);return this.sendMessage(o)},t.prototype.invoke=function(t){for(var e=this,n=[],o=1;o<arguments.length;o++)n[o-1]=arguments[o];var i=this.createInvocation(t,n,!1),s=new Promise(function(t,n){e.callbacks[i.invocationId]=function(e,o){o?n(o):e&&(e.type===r.Completion?e.error?n(new Error(e.error)):t(e.result):n(new Error("Unexpected message type: "+e.type)))};var o=e.protocol.writeMessage(i);e.sendMessage(o).catch(function(t){n(t),delete e.callbacks[i.invocationId]})});return s},t.prototype.on=function(t,e){t&&e&&(t=t.toLowerCase(),this.methods[t]||(this.methods[t]=[]),-1===this.methods[t].indexOf(e)&&this.methods[t].push(e))},t.prototype.off=function(t,e){if(t){t=t.toLowerCase();var n=this.methods[t];if(n)if(e){var r=n.indexOf(e);-1!==r&&(n.splice(r,1),0===n.length&&delete this.methods[t])}else delete this.methods[t]}},t.prototype.onclose=function(t){t&&this.closedCallbacks.push(t)},t.prototype.processIncomingData=function(t){if(this.cleanupTimeout(),this.receivedHandshakeResponse||(t=this.processHandshakeResponse(t),this.receivedHandshakeResponse=!0),t)for(var e=this.protocol.parseMessages(t,this.logger),n=0,o=e;n<o.length;n++){var i=o[n];switch(i.type){case r.Invocation:this.invokeClientMethod(i);break;case r.StreamItem:case r.Completion:var s=this.callbacks[i.invocationId];null!=s&&(i.type===r.Completion&&delete this.callbacks[i.invocationId],s(i));break;case r.Ping:break;case r.Close:this.logger.log(h["a"].Information,"Close message received from server."),this.connection.stop(i.error?new Error("Server returned an error on close: "+i.error):void 0);break;default:this.logger.log(h["a"].Warning,"Invalid message type: "+i.type+".");break}}this.resetTimeoutPeriod()},t.prototype.processHandshakeResponse=function(t){var e,n,r;try{e=this.handshakeProtocol.parseHandshakeResponse(t),r=e[0],n=e[1]}catch(s){var o="Error parsing handshake response: "+s;this.logger.log(h["a"].Error,o);var i=new Error(o);throw this.connection.stop(i),this.handshakeRejecter(i),i}if(n.error){o="Server returned handshake error: "+n.error;throw this.logger.log(h["a"].Error,o),this.handshakeRejecter(o),this.connection.stop(new Error(o)),new Error(o)}return this.logger.log(h["a"].Debug,"Server handshake complete."),this.handshakeResolver(),r},t.prototype.resetKeepAliveInterval=function(){var t=this;this.cleanupPingTimer(),this.pingServerHandle=setTimeout(function(){return m(t,void 0,void 0,function(){return w(this,function(t){switch(t.label){case 0:if(this.connectionState!==b.Connected)return[3,4];t.label=1;case 1:return t.trys.push([1,3,,4]),[4,this.sendMessage(this.cachedPingMessage)];case 2:return t.sent(),[3,4];case 3:return t.sent(),this.cleanupPingTimer(),[3,4];case 4:return[2]}})})},this.keepAliveIntervalInMilliseconds)},t.prototype.resetTimeoutPeriod=function(){var t=this;this.connection.features&&this.connection.features.inherentKeepAlive||(this.timeoutHandle=setTimeout(function(){return t.serverTimeout()},this.serverTimeoutInMilliseconds))},t.prototype.serverTimeout=function(){this.connection.stop(new Error("Server timeout elapsed without receiving a message from the server."))},t.prototype.invokeClientMethod=function(t){var e=this,n=this.methods[t.target.toLowerCase()];if(n){if(n.forEach(function(n){return n.apply(e,t.arguments)}),t.invocationId){var r="Server requested a response, which is not supported in this version of the client.";this.logger.log(h["a"].Error,r),this.connection.stop(new Error(r))}}else this.logger.log(h["a"].Warning,"No client method with the name '"+t.target+"' found.")},t.prototype.connectionClosed=function(t){var e=this,n=this.callbacks;this.callbacks={},this.connectionState=b.Disconnected,this.handshakeRejecter&&this.handshakeRejecter(t),Object.keys(n).forEach(function(e){var r=n[e];r(null,t||new Error("Invocation canceled due to connection being closed."))}),this.cleanupTimeout(),this.cleanupPingTimer(),this.closedCallbacks.forEach(function(n){return n.apply(e,[t])})},t.prototype.cleanupPingTimer=function(){this.pingServerHandle&&clearTimeout(this.pingServerHandle)},t.prototype.cleanupTimeout=function(){this.timeoutHandle&&clearTimeout(this.timeoutHandle)},t.prototype.createInvocation=function(t,e,n){if(n)return{arguments:e,target:t,type:r.Invocation};var o=this.id;return this.id++,{arguments:e,invocationId:o.toString(),target:t,type:r.Invocation}},t.prototype.createStreamInvocation=function(t,e){var n=this.id;return this.id++,{arguments:e,invocationId:n.toString(),target:t,type:r.StreamInvocation}},t.prototype.createCancelInvocation=function(t){return{invocationId:t,type:r.CancelInvocation}},t}();(function(t){t[t["None"]=0]="None",t[t["WebSockets"]=1]="WebSockets",t[t["ServerSentEvents"]=2]="ServerSentEvents",t[t["LongPolling"]=4]="LongPolling"})(C||(C={})),function(t){t[t["Text"]=1]="Text",t[t["Binary"]=2]="Binary"}(E||(E={}));var I=function(){function t(){this.isAborted=!1,this.onabort=null}return t.prototype.abort=function(){this.isAborted||(this.isAborted=!0,this.onabort&&this.onabort())},Object.defineProperty(t.prototype,"signal",{get:function(){return this},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"aborted",{get:function(){return this.isAborted},enumerable:!0,configurable:!0}),t}(),P=function(t,e,n,r){return new(n||(n=Promise))(function(o,i){function s(t){try{c(r.next(t))}catch(e){i(e)}}function a(t){try{c(r["throw"](t))}catch(e){i(e)}}function c(t){t.done?o(t.value):new n(function(e){e(t.value)}).then(s,a)}c((r=r.apply(t,e||[])).next())})},x=function(t,e){var n,r,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(t){return function(e){return c([t,e])}}function c(i){if(n)throw new TypeError("Generator is already executing.");while(s)try{if(n=1,r&&(o=2&i[0]?r["return"]:i[0]?r["throw"]||((o=r["return"])&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,r=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(o=s.trys,!(o=o.length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=e.call(t,s)}catch(a){i=[6,a],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}},M=function(){function t(t,e,n,r){this.httpClient=t,this.accessTokenFactory=e,this.logger=n,this.pollAbort=new I,this.logMessageContent=r,this.running=!1,this.onreceive=null,this.onclose=null}return Object.defineProperty(t.prototype,"pollAborted",{get:function(){return this.pollAbort.aborted},enumerable:!0,configurable:!0}),t.prototype.connect=function(t,e){return P(this,void 0,void 0,function(){var n,r,o,i;return x(this,function(s){switch(s.label){case 0:if(y["a"].isRequired(t,"url"),y["a"].isRequired(e,"transferFormat"),y["a"].isIn(e,E,"transferFormat"),this.url=t,this.logger.log(h["a"].Trace,"(LongPolling transport) Connecting."),e===E.Binary&&"undefined"!==typeof XMLHttpRequest&&"string"!==typeof(new XMLHttpRequest).responseType)throw new Error("Binary protocols over XmlHttpRequest not implementing advanced features are not supported.");return n={abortSignal:this.pollAbort.signal,headers:{},timeout:1e5},e===E.Binary&&(n.responseType="arraybuffer"),[4,this.getAccessToken()];case 1:return r=s.sent(),this.updateHeaderToken(n,r),o=t+"&_="+Date.now(),this.logger.log(h["a"].Trace,"(LongPolling transport) polling: "+o+"."),[4,this.httpClient.get(o,n)];case 2:return i=s.sent(),200!==i.statusCode?(this.logger.log(h["a"].Error,"(LongPolling transport) Unexpected response code: "+i.statusCode+"."),this.closeError=new c["b"](i.statusText||"",i.statusCode),this.running=!1):this.running=!0,this.receiving=this.poll(this.url,n),[2]}})})},t.prototype.getAccessToken=function(){return P(this,void 0,void 0,function(){return x(this,function(t){switch(t.label){case 0:return this.accessTokenFactory?[4,this.accessTokenFactory()]:[3,2];case 1:return[2,t.sent()];case 2:return[2,null]}})})},t.prototype.updateHeaderToken=function(t,e){t.headers||(t.headers={}),e?t.headers["Authorization"]="Bearer "+e:t.headers["Authorization"]&&delete t.headers["Authorization"]},t.prototype.poll=function(t,e){return P(this,void 0,void 0,function(){var n,r,o,i;return x(this,function(s){switch(s.label){case 0:s.trys.push([0,,8,9]),s.label=1;case 1:return this.running?[4,this.getAccessToken()]:[3,7];case 2:n=s.sent(),this.updateHeaderToken(e,n),s.label=3;case 3:return s.trys.push([3,5,,6]),r=t+"&_="+Date.now(),this.logger.log(h["a"].Trace,"(LongPolling transport) polling: "+r+"."),[4,this.httpClient.get(r,e)];case 4:return o=s.sent(),204===o.statusCode?(this.logger.log(h["a"].Information,"(LongPolling transport) Poll terminated by server."),this.running=!1):200!==o.statusCode?(this.logger.log(h["a"].Error,"(LongPolling transport) Unexpected response code: "+o.statusCode+"."),this.closeError=new c["b"](o.statusText||"",o.statusCode),this.running=!1):o.content?(this.logger.log(h["a"].Trace,"(LongPolling transport) data received. "+Object(y["e"])(o.content,this.logMessageContent)+"."),this.onreceive&&this.onreceive(o.content)):this.logger.log(h["a"].Trace,"(LongPolling transport) Poll timed out, reissuing."),[3,6];case 5:return i=s.sent(),this.running?i instanceof c["c"]?this.logger.log(h["a"].Trace,"(LongPolling transport) Poll timed out, reissuing."):(this.closeError=i,this.running=!1):this.logger.log(h["a"].Trace,"(LongPolling transport) Poll errored after shutdown: "+i.message),[3,6];case 6:return[3,1];case 7:return[3,9];case 8:return this.logger.log(h["a"].Trace,"(LongPolling transport) Polling complete."),this.pollAborted||this.raiseOnClose(),[7];case 9:return[2]}})})},t.prototype.send=function(t){return P(this,void 0,void 0,function(){return x(this,function(e){return this.running?[2,Object(y["g"])(this.logger,"LongPolling",this.httpClient,this.url,this.accessTokenFactory,t,this.logMessageContent)]:[2,Promise.reject(new Error("Cannot send until the transport is connected"))]})})},t.prototype.stop=function(){return P(this,void 0,void 0,function(){var t,e;return x(this,function(n){switch(n.label){case 0:this.logger.log(h["a"].Trace,"(LongPolling transport) Stopping polling."),this.running=!1,this.pollAbort.abort(),n.label=1;case 1:return n.trys.push([1,,5,6]),[4,this.receiving];case 2:return n.sent(),this.logger.log(h["a"].Trace,"(LongPolling transport) sending DELETE request to "+this.url+"."),t={headers:{}},[4,this.getAccessToken()];case 3:return e=n.sent(),this.updateHeaderToken(t,e),[4,this.httpClient.delete(this.url,t)];case 4:return n.sent(),this.logger.log(h["a"].Trace,"(LongPolling transport) DELETE request sent."),[3,6];case 5:return this.logger.log(h["a"].Trace,"(LongPolling transport) Stop finished."),this.raiseOnClose(),[7];case 6:return[2]}})})},t.prototype.raiseOnClose=function(){if(this.onclose){var t="(LongPolling transport) Firing onclose event.";this.closeError&&(t+=" Error: "+this.closeError),this.logger.log(h["a"].Trace,t),this.onclose(this.closeError)}},t}(),R=function(t,e,n,r){return new(n||(n=Promise))(function(o,i){function s(t){try{c(r.next(t))}catch(e){i(e)}}function a(t){try{c(r["throw"](t))}catch(e){i(e)}}function c(t){t.done?o(t.value):new n(function(e){e(t.value)}).then(s,a)}c((r=r.apply(t,e||[])).next())})},O=function(t,e){var n,r,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(t){return function(e){return c([t,e])}}function c(i){if(n)throw new TypeError("Generator is already executing.");while(s)try{if(n=1,r&&(o=2&i[0]?r["return"]:i[0]?r["throw"]||((o=r["return"])&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,r=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(o=s.trys,!(o=o.length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=e.call(t,s)}catch(a){i=[6,a],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}},_=function(){function t(t,e,n,r,o){this.httpClient=t,this.accessTokenFactory=e,this.logger=n,this.logMessageContent=r,this.eventSourceConstructor=o,this.onreceive=null,this.onclose=null}return t.prototype.connect=function(t,e){return R(this,void 0,void 0,function(){var n,r=this;return O(this,function(o){switch(o.label){case 0:return y["a"].isRequired(t,"url"),y["a"].isRequired(e,"transferFormat"),y["a"].isIn(e,E,"transferFormat"),this.logger.log(h["a"].Trace,"(SSE transport) Connecting."),this.url=t,this.accessTokenFactory?[4,this.accessTokenFactory()]:[3,2];case 1:n=o.sent(),n&&(t+=(t.indexOf("?")<0?"?":"&")+"access_token="+encodeURIComponent(n)),o.label=2;case 2:return[2,new Promise(function(n,o){var i=!1;if(e===E.Text){var s;if("undefined"!==typeof window)s=new r.eventSourceConstructor(t,{withCredentials:!0});else{var a=r.httpClient.getCookieString(t);s=new r.eventSourceConstructor(t,{withCredentials:!0,headers:{Cookie:a}})}try{s.onmessage=function(t){if(r.onreceive)try{r.logger.log(h["a"].Trace,"(SSE transport) data received. "+Object(y["e"])(t.data,r.logMessageContent)+"."),r.onreceive(t.data)}catch(e){return void r.close(e)}},s.onerror=function(t){var e=new Error(t.data||"Error occurred");i?r.close(e):o(e)},s.onopen=function(){r.logger.log(h["a"].Information,"SSE connected to "+r.url),r.eventSource=s,i=!0,n()}}catch(c){return void o(c)}}else o(new Error("The Server-Sent Events transport only supports the 'Text' transfer format"))})]}})})},t.prototype.send=function(t){return R(this,void 0,void 0,function(){return O(this,function(e){return this.eventSource?[2,Object(y["g"])(this.logger,"SSE",this.httpClient,this.url,this.accessTokenFactory,t,this.logMessageContent)]:[2,Promise.reject(new Error("Cannot send until the transport is connected"))]})})},t.prototype.stop=function(){return this.close(),Promise.resolve()},t.prototype.close=function(t){this.eventSource&&(this.eventSource.close(),this.eventSource=void 0,this.onclose&&this.onclose(t))},t}(),j=function(t,e,n,r){return new(n||(n=Promise))(function(o,i){function s(t){try{c(r.next(t))}catch(e){i(e)}}function a(t){try{c(r["throw"](t))}catch(e){i(e)}}function c(t){t.done?o(t.value):new n(function(e){e(t.value)}).then(s,a)}c((r=r.apply(t,e||[])).next())})},L=function(t,e){var n,r,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(t){return function(e){return c([t,e])}}function c(i){if(n)throw new TypeError("Generator is already executing.");while(s)try{if(n=1,r&&(o=2&i[0]?r["return"]:i[0]?r["throw"]||((o=r["return"])&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,r=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(o=s.trys,!(o=o.length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=e.call(t,s)}catch(a){i=[6,a],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}},D=function(){function t(t,e,n,r,o){this.logger=n,this.accessTokenFactory=e,this.logMessageContent=r,this.webSocketConstructor=o,this.httpClient=t,this.onreceive=null,this.onclose=null}return t.prototype.connect=function(t,e){return j(this,void 0,void 0,function(){var n,r=this;return L(this,function(o){switch(o.label){case 0:return y["a"].isRequired(t,"url"),y["a"].isRequired(e,"transferFormat"),y["a"].isIn(e,E,"transferFormat"),this.logger.log(h["a"].Trace,"(WebSockets transport) Connecting."),this.accessTokenFactory?[4,this.accessTokenFactory()]:[3,2];case 1:n=o.sent(),n&&(t+=(t.indexOf("?")<0?"?":"&")+"access_token="+encodeURIComponent(n)),o.label=2;case 2:return[2,new Promise(function(n,o){var i;t=t.replace(/^http/,"ws");var s=r.httpClient.getCookieString(t);"undefined"===typeof window&&s&&(i=new r.webSocketConstructor(t,void 0,{headers:{Cookie:""+s}})),i||(i=new r.webSocketConstructor(t)),e===E.Binary&&(i.binaryType="arraybuffer"),i.onopen=function(e){r.logger.log(h["a"].Information,"WebSocket connected to "+t+"."),r.webSocket=i,n()},i.onerror=function(t){var e=null;"undefined"!==typeof ErrorEvent&&t instanceof ErrorEvent&&(e=t.error),o(e)},i.onmessage=function(t){r.logger.log(h["a"].Trace,"(WebSockets transport) data received. "+Object(y["e"])(t.data,r.logMessageContent)+"."),r.onreceive&&r.onreceive(t.data)},i.onclose=function(t){return r.close(t)}})]}})})},t.prototype.send=function(t){return this.webSocket&&this.webSocket.readyState===this.webSocketConstructor.OPEN?(this.logger.log(h["a"].Trace,"(WebSockets transport) sending data. "+Object(y["e"])(t,this.logMessageContent)+"."),this.webSocket.send(t),Promise.resolve()):Promise.reject("WebSocket is not in the OPEN state")},t.prototype.stop=function(){return this.webSocket&&(this.webSocket.onclose=function(){},this.webSocket.onmessage=function(){},this.webSocket.onerror=function(){},this.webSocket.close(),this.webSocket=void 0,this.close(void 0)),Promise.resolve()},t.prototype.close=function(t){this.logger.log(h["a"].Trace,"(WebSockets transport) socket closed."),this.onclose&&(!t||!1!==t.wasClean&&1e3===t.code?this.onclose():this.onclose(new Error("WebSocket closed with status code: "+t.code+" ("+t.reason+").")))},t}(),q=function(t,e,n,r){return new(n||(n=Promise))(function(o,i){function s(t){try{c(r.next(t))}catch(e){i(e)}}function a(t){try{c(r["throw"](t))}catch(e){i(e)}}function c(t){t.done?o(t.value):new n(function(e){e(t.value)}).then(s,a)}c((r=r.apply(t,e||[])).next())})},H=function(t,e){var n,r,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(t){return function(e){return c([t,e])}}function c(i){if(n)throw new TypeError("Generator is already executing.");while(s)try{if(n=1,r&&(o=2&i[0]?r["return"]:i[0]?r["throw"]||((o=r["return"])&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,r=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(o=s.trys,!(o=o.length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=e.call(t,s)}catch(a){i=[6,a],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}},N=100,A=null,F=null;if("undefined"===typeof window){var W=require;A=W("ws"),F=W("eventsource")}var U=function(){function t(t,e){void 0===e&&(e={}),this.features={},y["a"].isRequired(t,"url"),this.logger=Object(y["d"])(e.logger),this.baseUrl=this.resolveUrl(t),e=e||{},e.logMessageContent=e.logMessageContent||!1;var n="undefined"===typeof window;n||"undefined"===typeof WebSocket||e.WebSocket?n&&!e.WebSocket&&A&&(e.WebSocket=A):e.WebSocket=WebSocket,n||"undefined"===typeof EventSource||e.EventSource?n&&!e.EventSource&&"undefined"!==typeof F&&(e.EventSource=F):e.EventSource=EventSource,this.httpClient=e.httpClient||new d(this.logger),this.connectionState=2,this.options=e,this.onreceive=null,this.onclose=null}return t.prototype.start=function(t){return t=t||E.Binary,y["a"].isIn(t,E,"transferFormat"),this.logger.log(h["a"].Debug,"Starting connection with transfer format '"+E[t]+"'."),2!==this.connectionState?Promise.reject(new Error("Cannot start a connection that is not in the 'Disconnected' state.")):(this.connectionState=0,this.startPromise=this.startInternal(t),this.startPromise)},t.prototype.send=function(t){if(1!==this.connectionState)throw new Error("Cannot send data if the connection is not in the 'Connected' State.");return this.transport.send(t)},t.prototype.stop=function(t){return q(this,void 0,void 0,function(){return H(this,function(e){switch(e.label){case 0:this.connectionState=2,this.stopError=t,e.label=1;case 1:return e.trys.push([1,3,,4]),[4,this.startPromise];case 2:return e.sent(),[3,4];case 3:return e.sent(),[3,4];case 4:return this.transport?[4,this.transport.stop()]:[3,6];case 5:e.sent(),this.transport=void 0,e.label=6;case 6:return[2]}})})},t.prototype.startInternal=function(t){return q(this,void 0,void 0,function(){var e,n,r,o,i,s,a,c=this;return H(this,function(u){switch(u.label){case 0:e=this.baseUrl,this.accessTokenFactory=this.options.accessTokenFactory,u.label=1;case 1:return u.trys.push([1,12,,13]),this.options.skipNegotiation?this.options.transport!==C.WebSockets?[3,3]:(this.transport=this.constructTransport(C.WebSockets),[4,this.transport.connect(e,t)]):[3,5];case 2:return u.sent(),[3,4];case 3:throw Error("Negotiation can only be skipped when using the WebSocket transport directly.");case 4:return[3,11];case 5:n=null,r=0,o=function(){var t;return H(this,function(o){switch(o.label){case 0:return[4,i.getNegotiationResponse(e)];case 1:if(n=o.sent(),2===i.connectionState)return[2,{value:void 0}];if(n.error)throw Error(n.error);if(n.ProtocolVersion)throw Error("Detected a connection attempt to an ASP.NET SignalR Server. This client only supports connecting to an ASP.NET Core SignalR Server. See https://aka.ms/signalr-core-differences for details.");return n.url&&(e=n.url),n.accessToken&&(t=n.accessToken,i.accessTokenFactory=function(){return t}),r++,[2]}})},i=this,u.label=6;case 6:return[5,o()];case 7:if(s=u.sent(),"object"===typeof s)return[2,s.value];u.label=8;case 8:if(n.url&&r<N)return[3,6];u.label=9;case 9:if(r===N&&n.url)throw Error("Negotiate redirection limit exceeded.");return[4,this.createTransport(e,this.options.transport,n,t)];case 10:u.sent(),u.label=11;case 11:return this.transport instanceof M&&(this.features.inherentKeepAlive=!0),this.transport.onreceive=this.onreceive,this.transport.onclose=function(t){return c.stopConnection(t)},this.changeState(0,1),[3,13];case 12:throw a=u.sent(),this.logger.log(h["a"].Error,"Failed to start the connection: "+a),this.connectionState=2,this.transport=void 0,a;case 13:return[2]}})})},t.prototype.getNegotiationResponse=function(t){return q(this,void 0,void 0,function(){var e,n,r,o,i,s;return H(this,function(a){switch(a.label){case 0:return this.accessTokenFactory?[4,this.accessTokenFactory()]:[3,2];case 1:r=a.sent(),r&&(e={},e["Authorization"]="Bearer "+r,n=e),a.label=2;case 2:o=this.resolveNegotiateUrl(t),this.logger.log(h["a"].Debug,"Sending negotiation request: "+o+"."),a.label=3;case 3:return a.trys.push([3,5,,6]),[4,this.httpClient.post(o,{content:"",headers:n})];case 4:if(i=a.sent(),200!==i.statusCode)throw Error("Unexpected status code returned from negotiate "+i.statusCode);return[2,JSON.parse(i.content)];case 5:throw s=a.sent(),this.logger.log(h["a"].Error,"Failed to complete negotiation with the server: "+s),s;case 6:return[2]}})})},t.prototype.createConnectUrl=function(t,e){return e?t+(-1===t.indexOf("?")?"?":"&")+"id="+e:t},t.prototype.createTransport=function(t,e,n,r){return q(this,void 0,void 0,function(){var o,i,s,a,c,u,l;return H(this,function(p){switch(p.label){case 0:return o=this.createConnectUrl(t,n.connectionId),this.isITransport(e)?(this.logger.log(h["a"].Debug,"Connection was provided an instance of ITransport, using that directly."),this.transport=e,[4,this.transport.connect(o,r)]):[3,2];case 1:return p.sent(),this.changeState(0,1),[2];case 2:i=n.availableTransports||[],s=0,a=i,p.label=3;case 3:return s<a.length?(c=a[s],this.connectionState=0,u=this.resolveTransport(c,e,r),"number"!==typeof u?[3,8]:(this.transport=this.constructTransport(u),n.connectionId?[3,5]:[4,this.getNegotiationResponse(t)])):[3,9];case 4:n=p.sent(),o=this.createConnectUrl(t,n.connectionId),p.label=5;case 5:return p.trys.push([5,7,,8]),[4,this.transport.connect(o,r)];case 6:return p.sent(),this.changeState(0,1),[2];case 7:return l=p.sent(),this.logger.log(h["a"].Error,"Failed to start the transport '"+C[u]+"': "+l),this.connectionState=2,n.connectionId=void 0,[3,8];case 8:return s++,[3,3];case 9:throw new Error("Unable to initialize any of the available transports.")}})})},t.prototype.constructTransport=function(t){switch(t){case C.WebSockets:if(!this.options.WebSocket)throw new Error("'WebSocket' is not supported in your environment.");return new D(this.httpClient,this.accessTokenFactory,this.logger,this.options.logMessageContent||!1,this.options.WebSocket);case C.ServerSentEvents:if(!this.options.EventSource)throw new Error("'EventSource' is not supported in your environment.");return new _(this.httpClient,this.accessTokenFactory,this.logger,this.options.logMessageContent||!1,this.options.EventSource);case C.LongPolling:return new M(this.httpClient,this.accessTokenFactory,this.logger,this.options.logMessageContent||!1);default:throw new Error("Unknown transport: "+t+".")}},t.prototype.resolveTransport=function(t,e,n){var r=C[t.transport];if(null===r||void 0===r)this.logger.log(h["a"].Debug,"Skipping transport '"+t.transport+"' because it is not supported by this client.");else{var o=t.transferFormats.map(function(t){return E[t]});if(B(e,r))if(o.indexOf(n)>=0){if((r!==C.WebSockets||this.options.WebSocket)&&(r!==C.ServerSentEvents||this.options.EventSource))return this.logger.log(h["a"].Debug,"Selecting transport '"+C[r]+"'."),r;this.logger.log(h["a"].Debug,"Skipping transport '"+C[r]+"' because it is not supported in your environment.'")}else this.logger.log(h["a"].Debug,"Skipping transport '"+C[r]+"' because it does not support the requested transfer format '"+E[n]+"'.");else this.logger.log(h["a"].Debug,"Skipping transport '"+C[r]+"' because it was disabled by the client.")}return null},t.prototype.isITransport=function(t){return t&&"object"===typeof t&&"connect"in t},t.prototype.changeState=function(t,e){return this.connectionState===t&&(this.connectionState=e,!0)},t.prototype.stopConnection=function(t){this.transport=void 0,t=this.stopError||t,t?this.logger.log(h["a"].Error,"Connection disconnected with error '"+t+"'."):this.logger.log(h["a"].Information,"Connection disconnected."),this.connectionState=2,this.onclose&&this.onclose(t)},t.prototype.resolveUrl=function(t){if(0===t.lastIndexOf("https://",0)||0===t.lastIndexOf("http://",0))return t;if("undefined"===typeof window||!window||!window.document)throw new Error("Cannot resolve '"+t+"'.");var e=window.document.createElement("a");return e.href=t,this.logger.log(h["a"].Information,"Normalizing '"+t+"' to '"+e.href+"'."),e.href},t.prototype.resolveNegotiateUrl=function(t){var e=t.indexOf("?"),n=t.substring(0,-1===e?t.length:e);return"/"!==n[n.length-1]&&(n+="/"),n+="negotiate",n+=-1===e?"":t.substring(e),n},t}();function B(t,e){return!t||0!==(e&t)}var J=n("7b74"),X=n("2183"),G="json",z=function(){function t(){this.name=G,this.version=1,this.transferFormat=E.Text}return t.prototype.parseMessages=function(t,e){if("string"!==typeof t)throw new Error("Invalid input for JSON hub protocol. Expected a string.");if(!t)return[];null===e&&(e=J["a"].instance);for(var n=X["a"].parse(t),o=[],i=0,s=n;i<s.length;i++){var a=s[i],c=JSON.parse(a);if("number"!==typeof c.type)throw new Error("Invalid payload.");switch(c.type){case r.Invocation:this.isInvocationMessage(c);break;case r.StreamItem:this.isStreamItemMessage(c);break;case r.Completion:this.isCompletionMessage(c);break;case r.Ping:break;case r.Close:break;default:e.log(h["a"].Information,"Unknown message type '"+c.type+"' ignored.");continue}o.push(c)}return o},t.prototype.writeMessage=function(t){return X["a"].write(JSON.stringify(t))},t.prototype.isInvocationMessage=function(t){this.assertNotEmptyString(t.target,"Invalid payload for Invocation message."),void 0!==t.invocationId&&this.assertNotEmptyString(t.invocationId,"Invalid payload for Invocation message.")},t.prototype.isStreamItemMessage=function(t){if(this.assertNotEmptyString(t.invocationId,"Invalid payload for StreamItem message."),void 0===t.item)throw new Error("Invalid payload for StreamItem message.")},t.prototype.isCompletionMessage=function(t){if(t.result&&t.error)throw new Error("Invalid payload for Completion message.");!t.result&&t.error&&this.assertNotEmptyString(t.error,"Invalid payload for Completion message."),this.assertNotEmptyString(t.invocationId,"Invalid payload for Completion message.")},t.prototype.assertNotEmptyString=function(t,e){if("string"!==typeof t||""===t)throw new Error(e)},t}(),K=function(){function t(){}return t.prototype.configureLogging=function(t){return y["a"].isRequired(t,"logging"),Y(t)?this.logger=t:this.logger=new y["b"](t),this},t.prototype.withUrl=function(t,e){return y["a"].isRequired(t,"url"),this.url=t,this.httpConnectionOptions="object"===typeof e?e:{transport:e},this},t.prototype.withHubProtocol=function(t){return y["a"].isRequired(t,"protocol"),this.protocol=t,this},t.prototype.build=function(){var t=this.httpConnectionOptions||{};if(void 0===t.logger&&(t.logger=this.logger),!this.url)throw new Error("The 'HubConnectionBuilder.withUrl' method must be called before building the connection.");var e=new U(this.url,t);return T.create(e,this.logger||J["a"].instance,this.protocol||new z)},t}();function Y(t){return void 0!==t.log}var $={data:function(){return{filters:{LinkUrl:""},listLoading:!0,tableData:[],userName:"Tom",userMessage:"123",connection:"",messages:[],t:""}},methods:{formattdDetail:function(t,e){return t.tdDetail?t.tdDetail.substring(0,20):"N/A"},formatCreateTime:function(t,e){return t.tdCreatetime&&""!=t.tdCreatetime?s["a"].formatDate.format(new Date(t.tdCreatetime),"yyyy-MM-dd"):""},handleCurrentChange:function(t){this.page=t,this.getRoles()},getRoles:function(){var t=this,e={page:this.page,key:this.filters.LinkUrl};this.listLoading=!0,Object(a["F"])(e).then(function(e){t.connection.start().then(function(){t.connection.invoke("GetLatestCount",1).catch(function(t){return console.error(t)})})})},submitCard:function(){this.userName&&this.userMessage&&this.connection.invoke("SendMessage",this.userName,this.userMessage).catch(function(t){return console.error(t)})},getLogs:function(){this.listLoading=!0,this.connection.invoke("GetLatestCount",1).catch(function(t){return console.error(t)})}},created:function(){var t=this;t.connection=(new K).withUrl("".concat(a["a"],"/api2/chatHub")).configureLogging(h["a"].Information).build(),t.connection.on("ReceiveMessage",function(e,n){t.messages.push({user:e,message:n})}),t.connection.on("ReceiveUpdate",function(e){console.info("update success!"),t.listLoading=!1,t.tableData=e,window.clearInterval(this.t)})},mounted:function(){this.getRoles()},beforeDestroy:function(){window.clearInterval(this.t),this.connection.stop()}},Q=$,V=(n("482f"),n("2877")),Z=Object(V["a"])(Q,o,i,!1,null,"6e805334",null);Z.options.__file="Index.vue";e["default"]=Z.exports},a6dc:function(t,e,n){"use strict";var r=n("e814"),o=n.n(r),i=(n("a481"),n("386d"),n("4917"),n("3b2b"),/([yMdhsm])(\1*)/g),s="yyyy-MM-dd";function a(t,e){e-=(t+"").length;for(var n=0;n<e;n++)t="0"+t;return t}e["a"]={getQueryStringByName:function(t){var e=new RegExp("(^|&)"+t+"=([^&]*)(&|$)","i"),n=window.location.search.substr(1).match(e),r="";return null!=n&&(r=n[2]),e=null,n=null,null==r||""==r||"undefined"==r?"":r},formatDate:{format:function(t,e){return e=e||s,e.replace(i,function(e){switch(e.charAt(0)){case"y":return a(t.getFullYear(),e.length);case"M":return a(t.getMonth()+1,e.length);case"d":return a(t.getDate(),e.length);case"w":return t.getDay()+1;case"h":return a(t.getHours(),e.length);case"m":return a(t.getMinutes(),e.length);case"s":return a(t.getSeconds(),e.length)}})},parse:function(t,e){var n=e.match(i),r=t.match(/(\d)+/g);if(n.length==r.length){for(var s=new Date(1970,0,1),a=0;a<n.length;a++){var c=o()(r[a]),u=n[a];switch(u.charAt(0)){case"y":s.setFullYear(c);break;case"M":s.setMonth(c-1);break;case"d":s.setDate(c);break;case"h":s.setHours(c);break;case"m":s.setMinutes(c);break;case"s":s.setSeconds(c);break}}return s}return null}},isEmt:{format:function(t){return"undefined"==typeof t||null==t||""==t}}}},aae3:function(t,e,n){var r=n("d3f4"),o=n("2d95"),i=n("2b4c")("match");t.exports=function(t){var e;return r(t)&&(void 0!==(e=t[i])?!!e:"RegExp"==o(t))}},c5fb:function(t,e,n){"use strict";n.d(e,"b",function(){return o}),n.d(e,"c",function(){return i}),n.d(e,"a",function(){return s});var r=function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),o=function(t){function e(e,n){var r=this.constructor,o=this,i=r.prototype;return o=t.call(this,e)||this,o.statusCode=n,o.__proto__=i,o}return r(e,t),e}(Error),i=function(t){function e(e){var n=this.constructor;void 0===e&&(e="A timeout occurred.");var r=this,o=n.prototype;return r=t.call(this,e)||this,r.__proto__=o,r}return r(e,t),e}(Error),s=function(t){function e(e){var n=this.constructor;void 0===e&&(e="An abort occurred.");var r=this,o=n.prototype;return r=t.call(this,e)||this,r.__proto__=o,r}return r(e,t),e}(Error)},c9f7:function(t,e,n){"use strict";n.d(e,"a",function(){return a}),n.d(e,"e",function(){return c}),n.d(e,"f",function(){return l}),n.d(e,"g",function(){return h}),n.d(e,"d",function(){return p}),n.d(e,"c",function(){return f}),n.d(e,"b",function(){return d});var r=n("7835"),o=n("7b74"),i=function(t,e,n,r){return new(n||(n=Promise))(function(o,i){function s(t){try{c(r.next(t))}catch(e){i(e)}}function a(t){try{c(r["throw"](t))}catch(e){i(e)}}function c(t){t.done?o(t.value):new n(function(e){e(t.value)}).then(s,a)}c((r=r.apply(t,e||[])).next())})},s=function(t,e){var n,r,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(t){return function(e){return c([t,e])}}function c(i){if(n)throw new TypeError("Generator is already executing.");while(s)try{if(n=1,r&&(o=2&i[0]?r["return"]:i[0]?r["throw"]||((o=r["return"])&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,r=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(o=s.trys,!(o=o.length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=e.call(t,s)}catch(a){i=[6,a],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}},a=function(){function t(){}return t.isRequired=function(t,e){if(null===t||void 0===t)throw new Error("The '"+e+"' argument is required.")},t.isIn=function(t,e,n){if(!(t in e))throw new Error("Unknown "+n+" value: "+t+".")},t}();function c(t,e){var n="";return l(t)?(n="Binary data of length "+t.byteLength,e&&(n+=". Content: '"+u(t)+"'")):"string"===typeof t&&(n="String data of length "+t.length,e&&(n+=". Content: '"+t+"'")),n}function u(t){var e=new Uint8Array(t),n="";return e.forEach(function(t){var e=t<16?"0":"";n+="0x"+e+t.toString(16)+" "}),n.substr(0,n.length-1)}function l(t){return t&&"undefined"!==typeof ArrayBuffer&&(t instanceof ArrayBuffer||t.constructor&&"ArrayBuffer"===t.constructor.name)}function h(t,e,n,o,a,u,h){return i(this,void 0,void 0,function(){var i,p,f,g,d;return s(this,function(s){switch(s.label){case 0:return a?[4,a()]:[3,2];case 1:f=s.sent(),f&&(i={},i["Authorization"]="Bearer "+f,p=i),s.label=2;case 2:return t.log(r["a"].Trace,"("+e+" transport) sending data. "+c(u,h)+"."),g=l(u)?"arraybuffer":"text",[4,n.post(o,{content:u,headers:p,responseType:g})];case 3:return d=s.sent(),t.log(r["a"].Trace,"("+e+" transport) request complete. Response status: "+d.statusCode+"."),[2]}})})}function p(t){return void 0===t?new d(r["a"].Information):null===t?o["a"].instance:t.log?t:new d(t)}var f=function(){function t(){this.observers=[]}return t.prototype.next=function(t){for(var e=0,n=this.observers;e<n.length;e++){var r=n[e];r.next(t)}},t.prototype.error=function(t){for(var e=0,n=this.observers;e<n.length;e++){var r=n[e];r.error&&r.error(t)}},t.prototype.complete=function(){for(var t=0,e=this.observers;t<e.length;t++){var n=e[t];n.complete&&n.complete()}},t.prototype.subscribe=function(t){return this.observers.push(t),new g(this,t)},t}(),g=function(){function t(t,e){this.subject=t,this.observer=e}return t.prototype.dispose=function(){var t=this.subject.observers.indexOf(this.observer);t>-1&&this.subject.observers.splice(t,1),0===this.subject.observers.length&&this.subject.cancelCallback&&this.subject.cancelCallback().catch(function(t){})},t}(),d=function(){function t(t){this.minimumLogLevel=t}return t.prototype.log=function(t,e){if(t>=this.minimumLogLevel)switch(t){case r["a"].Critical:case r["a"].Error:console.error("["+(new Date).toISOString()+"] "+r["a"][t]+": "+e);break;case r["a"].Warning:console.warn("["+(new Date).toISOString()+"] "+r["a"][t]+": "+e);break;case r["a"].Information:console.info("["+(new Date).toISOString()+"] "+r["a"][t]+": "+e);break;default:console.log("["+(new Date).toISOString()+"] "+r["a"][t]+": "+e);break}},t}()}}]);
//# sourceMappingURL=chunk-ef28925c.a547d73e.js.map