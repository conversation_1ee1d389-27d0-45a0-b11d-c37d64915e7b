using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using System.Linq.Expressions;

namespace SEFA.PPMApi.Controllers
{
    [Route("ppm/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class PoProducedRequirementController : BaseApiController
    {
        /// <summary>
        /// PoProducedRequirement
        /// </summary>
        private readonly IPoProducedRequirementServices _poProducedRequirementServices;

        public PoProducedRequirementController(IPoProducedRequirementServices PoProducedRequirementServices)
        {
            _poProducedRequirementServices = PoProducedRequirementServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<PoProducedRequirementEntity>>> GetList([FromBody] PoProducedRequirementRequestModel reqModel)
        {
            var data = await _poProducedRequirementServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<PoProducedRequirementEntity>>> GetPageList([FromBody] PoProducedRequirementRequestModel reqModel)
        {
            Expression<Func<PoProducedRequirementEntity, bool>> whereExpression = a => true;
            var data = await _poProducedRequirementServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<PoProducedRequirementEntity>> GetEntity(string id)
        {
            var data = await _poProducedRequirementServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] PoProducedRequirementEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _poProducedRequirementServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _poProducedRequirementServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class PoProducedRequirementRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}