using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices.Interface.WMS;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.Models.Interface.WMS;
using SEFA.PPM.Model.ViewModels.Interface.WMS;
using SEFA.PPM.Model.ViewModels.WMS;
using SEFA.PPM.Services.Interface.WMS;

namespace SEFA.PPM.Api.Controllers.Interface.WMS
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [AllowAnonymous]
    public class CallMaterialSheetController : BaseApiController
    {
        private readonly ICallMaterialSheetServices _callMaterialSheetServices;

        public CallMaterialSheetController(ICallMaterialSheetServices callMaterialSheetServices)
        {
            _callMaterialSheetServices = callMaterialSheetServices;
        }

        /// <summary>
        /// 新增叫料单
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> AddCallMaterialSheet([FromBody] CallMaterialSheetRequestModel request)
        {
            var result = await _callMaterialSheetServices.AddCallMaterialSheet(request);
            return result;
        }

        /// <summary>
        /// 批量新增叫料单
        /// </summary>
        /// <param name="requests">叫料单请求模型列表</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<MessageModel<string>> AddCallMaterialSheetBatch([FromBody] List<CallMaterialSheetRequestModel> requests)
        {
            var result = await _callMaterialSheetServices.AddCallMaterialSheetBatch(requests);
            return result;
        }

        /// <summary>
        /// 重新叫料
        /// </summary>
        /// <param name="sheetId">叫料单ID</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<string>> ReCallMaterialSheet(string sheetId)
        {
            var result = await _callMaterialSheetServices.ReCallMaterialSheet(sheetId);
            return result;
        }

        /// <summary>
        /// 根据产线Code获取工单列表
        /// </summary>
        /// <param name="lineCode"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<List<ProductionOrderEntity>>> GetProductionOrderListByLine(
            string lineCode)
        {
            var result = new MessageModel<List<ProductionOrderEntity>>();
            try
            {
                var data = await _callMaterialSheetServices.GetProductionOrderListByLine(lineCode);
                if (data != null)
                {
                    result = Success(data, "工单列表获取成功");
                }
                else
                {
                    result = Failed<List<ProductionOrderEntity>>("未找到相关工单信息");
                }
            }
            catch (Exception ex)
            {
                result = Failed<List<ProductionOrderEntity>>($"获取工单列表失败：{ex.Message}");
            }
            
            return result;
        }

        /// <summary>
        /// 根据工单ID获取叫料单预览列表（不插入数据库）
        /// </summary>
        /// <param name="productionOrderId">工单ID</param>
        /// <returns>叫料单预览列表</returns>
        [HttpGet]
        public async Task<MessageModel<List<CallMaterialSheetPreviewModel>>> GetCallMaterialDetailsByOrder(
            string productionOrderId)
        {
            var result = await _callMaterialSheetServices.GetCallMaterialSheetsPreviewByOrder(productionOrderId);
            return result;
        }

        /// <summary>
        /// 修改叫料单
        /// </summary>
        /// <param name="request">叫料单请求模型</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<MessageModel<string>> UpdateCallMaterialSheet(
            [FromBody] CallMaterialSheetRequestModel request)
        {
            var result = await _callMaterialSheetServices.UpdateCallMaterialSheet(request);
            if (result)
            {
                return Success<string>("叫料单修改成功");
            }

            return Failed("叫料单修改失败");
        }

        /// <summary>
        /// 删除叫料单（逻辑删除）
        /// </summary>
        /// <param name="ids">叫料单ID列表</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<MessageModel<string>> DeleteCallMaterialSheet([FromBody] string[] ids)
        {
            var result = await _callMaterialSheetServices.DeleteCallMaterialSheet(ids);
            if (result)
            {
                return Success<string>("叫料单删除成功");
            }

            return Failed("叫料单删除失败");
        }

        /// <summary>
        /// 分页查询叫料单
        /// </summary>
        /// <param name="request">查询条件</param>
        /// <returns>分页结果</returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<CallMaterialHeaderView>>> GetPageList(
            [FromBody] CallMaterialHeaderRequestModel request)
        {
            var data = await _callMaterialSheetServices.GetPageList(request);
            return Success(data, "叫料单查询成功");
        }

        /// <summary>
        /// 获取叫料单详情
        /// </summary>
        /// <param name="id">叫料单ID</param>
        /// <returns>叫料单详情</returns>
        [HttpGet("{id}")]
        public async Task<MessageModel<CallMaterialSheetViewModel>> GetCallMaterialSheetById(string id)
        {
            var data = await _callMaterialSheetServices.GetCallMaterialSheetById(id);
            if (data != null)
            {
                return Success(data, "叫料单详情获取成功");
            }

            return Failed<CallMaterialSheetViewModel>("叫料单详情不存在");
        }

        /// <summary>
        /// 根据叫料单ID获取明细列表
        /// </summary>
        /// <param name="sheetId">叫料单ID</param>
        /// <returns>明细列表</returns>
        [HttpGet("{sheetId}/details")]
        public async Task<MessageModel<CallMaterialSheetViewModel>> GetCallMaterialDetailsBySheetId(string sheetId)
        {
            var data = await _callMaterialSheetServices.GetCallMaterialSheetById(sheetId);
            if (data != null)
            {
                return Success(data, "叫料单明细获取成功");
            }

            return Failed<CallMaterialSheetViewModel>("叫料单不存在");
        }
    }
}