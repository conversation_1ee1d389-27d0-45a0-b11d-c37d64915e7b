using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.Api.Controllers.MKM
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [AllowAnonymous]
    public class StorageInventoryController : BaseApiController
    {
        /// <summary>
        /// StorageInventory
        /// </summary>
        private readonly IStorageInventoryServices _storageInventoryServices;
    
        public StorageInventoryController(IStorageInventoryServices StorageInventoryServices)
        {
            _storageInventoryServices = StorageInventoryServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<StorageInventoryEntity>>> GetList([FromBody] StorageInventoryRequestModel reqModel)
        {
            var data = await _storageInventoryServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<StorageInventoryEntity>>> GetPageList([FromBody] StorageInventoryRequestModel reqModel)
        {
            var data = await _storageInventoryServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<StorageInventoryEntity>> GetEntity(string id)
        {
            var data = await _storageInventoryServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] StorageInventoryEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _storageInventoryServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _storageInventoryServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] StorageInventoryEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _storageInventoryServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _storageInventoryServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    //public class StorageInventoryRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}