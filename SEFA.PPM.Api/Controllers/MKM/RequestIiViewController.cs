using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class RequestIiViewController : BaseApiController
    {
        /// <summary>
        /// RequestIiView
        /// </summary>
        private readonly IRequestIiViewServices _requestIiViewServices;

        public RequestIiViewController(IRequestIiViewServices RequestIiViewServices)
        {
            _requestIiViewServices = RequestIiViewServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<RequestIiViewEntity>>> GetList([FromBody] RequestIiViewRequestModel reqModel)
        {
            var data = await _requestIiViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }





        [HttpGet("{id}")]
        public async Task<MessageModel<RequestIiViewEntity>> GetEntity(string id)
        {
            var data = await _requestIiViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] RequestIiViewEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _requestIiViewServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _requestIiViewServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] RequestIiViewEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _requestIiViewServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        /// <summary>
        /// 删除请料记录
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> DeleteRecord(string[] ids)
        {
            var data = await _requestIiViewServices.DeleteRecord(ids);
            if (data.success)
            {
                return Success("", data.msg);
            }
            else
            {
                return Failed(data.msg);
            }
        }


        #region 请料

        /// <summary>
        /// 请料记录查询
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<RequestIiViewEntity>>> GetPageList([FromBody] RequestIiViewRequestModel reqModel)
        {
            var data = await _requestIiViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 请料详细查询
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<RequestIiiViewEntity>>> GetRequestInfo(RequestIiiViewRequestModel reqModel)
        {
            var data = await _requestIiViewServices.GetRequestInfo(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 统计
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<RequestDqtyViewEntity>>> GetRequestDetailList([FromBody] RequestIiViewRequestModel reqModel)
        {
            var data = await _requestIiViewServices.GetDetailList(reqModel);
            return Success(data, "获取成功");
        }

        #endregion

        #region 拉料

        /// <summary>
        /// 请料记录查询
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<PullIiViewEntity>>> GetPageList_Pull([FromBody] PullIiViewRequestModel reqModel)
        {
            var data = await _requestIiViewServices.GetPageList_PULL(reqModel);
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 请料详细查询
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<PullIiiViewEntity>>> GetRequestInfo_Pull(PullIiiViewRequestModel reqModel)
        {
            var data = await _requestIiViewServices.GetRequestInfo_PULL(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 统计
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<PullDqtyViewEntity>>> GetRequestDetailList_Pull([FromBody] PullIiViewRequestModel reqModel)
        {
            var data = await _requestIiViewServices.GetDetailList_PULL(reqModel);
            return Success(data, "获取成功");
        }

        #endregion
    }
}