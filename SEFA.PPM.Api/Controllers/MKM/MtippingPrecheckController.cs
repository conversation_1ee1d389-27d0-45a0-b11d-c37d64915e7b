using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.PPM.Controllers;
using SEFA.PPM.Model.Models;
using SEFA.PTM.Model.ViewModels;

namespace SEFA.MKMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class MtippingPrecheckController : BaseApiController
    {
        /// <summary>
        /// MtippingPrecheck
        /// </summary>
        private readonly IMtippingPrecheckServices _mtippingPrecheckServices;

        public MtippingPrecheckController(IMtippingPrecheckServices MtippingPrecheckServices)
        {
            _mtippingPrecheckServices = MtippingPrecheckServices;
        }


        #region 函数

        [HttpPost]
        public async Task<MessageModel<string>> ScanContainerCode([FromBody] string containerCode)
        {
            return await _mtippingPrecheckServices.ScanContainerCode(containerCode);
        }

        [HttpPost]
        public async Task<MessageModel<List<MtippingPrecheckEntity>>> GetList([FromBody] MtippingPrecheckRequestModel reqModel)
        {
            var data = await _mtippingPrecheckServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<MtippingPrecheckEntity>>> GetPageList([FromBody] MtippingPrecheckRequestModel reqModel)
        {
            var data = await _mtippingPrecheckServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }


        #endregion


        #region 函数VIEW

        [HttpPost]
        public async Task<MessageModel<List<MtippingPrecheckViewEntity>>> GetListView([FromBody] MtippingPrecheckViewRequestModel reqModel)
        {
            var data = await _mtippingPrecheckServices.GetListView(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<MtippingPrecheckViewEntity>>> GetPageListView([FromBody] MtippingPrecheckViewRequestModel reqModel)
        {
            var data = await _mtippingPrecheckServices.GetPageListView(reqModel);
            return Success(data, "获取成功");
        }


        [HttpPost]
        public async Task<MessageModel<List<TPrecheckDetailViewEntity>>> GetListDetial([FromBody] MtippingPrecheckViewRequestModel reqModel)
        {
            var data = await _mtippingPrecheckServices.GetListDetial(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取检查完成数量/总数量
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
		[HttpPost]
        public async Task<MessageModel<string>> GetCount([FromBody] MtippingPrecheckViewRequestModel reqModel)
        {
            return await _mtippingPrecheckServices.GetCount(reqModel);
        }

        /// <summary>
        /// 备料复检操作：通过key值判断执行不同逻辑 key=1开始备料复检,key=2扫描库存
        /// Body{\"BatchId\":\"\",\"TraceCode\":\"\"}key=1时只用传BatchId，key=2时两个都要传
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> Precheck([FromBody] ConsolPoRequestModel reqModel)
        {
            return await _mtippingPrecheckServices.Precheck(reqModel);
        }

        [HttpPost]
        public async Task<MessageModel<string>> ScanTraceCode(ConsolPoRequestModel model)
        {
            return await _mtippingPrecheckServices.ScanTraceCode(model.TrayCode);
        }

        #endregion

        [HttpGet("{id}")]
        public async Task<MessageModel<MtippingPrecheckEntity>> GetEntity(string id)
        {
            var data = await _mtippingPrecheckServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] MtippingPrecheckEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _mtippingPrecheckServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _mtippingPrecheckServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] MtippingPrecheckEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _mtippingPrecheckServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _mtippingPrecheckServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class MtippingPrecheckRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}