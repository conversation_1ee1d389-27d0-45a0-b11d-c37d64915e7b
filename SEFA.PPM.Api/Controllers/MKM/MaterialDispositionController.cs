using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.PPM.Controllers;
using System.Linq.Expressions;

namespace SEFA.MKMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class MaterialDispositionController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IMaterialDispositionServices _materialDispositionServices;

        public MaterialDispositionController(IMaterialDispositionServices MaterialDispositionServices)
        {
            _materialDispositionServices = MaterialDispositionServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<MaterialDispositionEntity>>> GetList(string key = "")
        {
            Expression<Func<MaterialDispositionEntity, bool>> whereExpression = a => true;
            var data = await _materialDispositionServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<MaterialDispositionEntity>>> GetPageList([FromBody] MaterialDispositionRequestModel reqModel)
        {

            Expression<Func<MaterialDispositionEntity, bool>> whereExpression = a => true;
            var data = await _materialDispositionServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<MaterialDispositionEntity>> GetEntity(string id)
        {
            var data = await _materialDispositionServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] MaterialDispositionEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _materialDispositionServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _materialDispositionServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _materialDispositionServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    public class MaterialDispositionRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }
}