using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.PPM.Controllers;
using System.Linq.Expressions;

namespace SEFA.MKMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class MaterialLotController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IMaterialLotServices _materialLotServices;

        public MaterialLotController(IMaterialLotServices MaterialLotServices)
        {
            _materialLotServices = MaterialLotServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<MaterialLotEntity>>> GetList(string key = "")
        {
            Expression<Func<MaterialLotEntity, bool>> whereExpression = a => true;
            var data = await _materialLotServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<MaterialLotEntity>>> GetPageList([FromBody] MaterialLotRequestModel reqModel)
        {

            Expression<Func<MaterialLotEntity, bool>> whereExpression = a => true;
            var data = await _materialLotServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<MaterialLotEntity>> GetEntity(string id)
        {
            var data = await _materialLotServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] MaterialLotEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _materialLotServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _materialLotServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<List<MaterialLotEntity>>> GetListByLotID(string lotID)
        {
            Expression<Func<MaterialLotEntity, bool>> whereExpression = a => true;
            var data = await _materialLotServices.GetListByLotID(lotID);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _materialLotServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    public class MaterialLotRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }
}