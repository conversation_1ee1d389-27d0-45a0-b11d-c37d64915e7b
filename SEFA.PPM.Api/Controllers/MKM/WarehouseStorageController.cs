using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.PPM.Controllers;

namespace SEFA.MKM.Api.Controllers
{
    [Route("materail/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class WarehouseStorageController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IWarehouseStorageServices _warehouseStorageServices;

        public WarehouseStorageController(IWarehouseStorageServices WarehouseStorageServices)
        {
            _warehouseStorageServices = WarehouseStorageServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<WarehouseStorageEntity>>> GetList([FromBody] WarehouseStorageRequestModel reqModel)
        {
            var data = await _warehouseStorageServices.GetList(reqModel);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<WarehouseStorageEntity>>> GetPageList([FromBody] WarehouseStorageRequestModel reqModel)
        {
            var data = await _warehouseStorageServices.GetPageList(reqModel);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<WarehouseStorageEntity>> GetEntity(string id)
        {
            var data = await _warehouseStorageServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] WarehouseStorageEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _warehouseStorageServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _warehouseStorageServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }

}