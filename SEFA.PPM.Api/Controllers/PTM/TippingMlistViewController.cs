using SEFA.PPM.IServices;
using SEFA.Base.Model;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.PPM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.PPM.Services.PTM;
using SEFA.PTM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.PTM;
using SEFA.DFM.Model.ViewModels;
using SEFA.PPM.Services;
using SEFA.MKM.Model.Models.MKM;
using static SEFA.PPM.Services.TippingMlistViewServices;
using SEFA.PPM.Model.Models.Interface;
using SEFA.PPM.Model.Models.PTM;

namespace SEFA.PPMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class TippingMlistViewController : BaseApiController
    {
        /// <summary>
        /// TipingMlistView
        /// </summary>
        private readonly ITippingMlistViewServices _tipingMlistViewServices;

        public TippingMlistViewController(ITippingMlistViewServices TipingMlistViewServices)
        {
            _tipingMlistViewServices = TipingMlistViewServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<TippingMlistViewEntity>>> GetList([FromBody] TippingMlistViewRequestModel reqModel)
        {
            var data = await _tipingMlistViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<TippingMlistViewEntity>>> GetPageList([FromBody] TippingMlistViewRequestModel reqModel)
        {
            var data = await _tipingMlistViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> GetTippingCount([FromBody] TippingSclistRequestModel reqModel)
        {
            return await _tipingMlistViewServices.GetTippingCount(reqModel);
        }

		[HttpPost]
		public async Task<MessageModel<SortModel>> GetTippingStatus([FromBody] TippingMlistViewRequestModel reqModel)
		{
			return await _tipingMlistViewServices.GetTippingStatus(reqModel);
		}

		[HttpGet("{id}")]
        public async Task<MessageModel<TippingMlistViewEntity>> GetEntity(string id)
        {
            var data = await _tipingMlistViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] TippingMlistViewEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _tipingMlistViewServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }

        /// <summary>
        /// 开始投料
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> StartTipping([FromBody] TippingMlistViewModel reqModel)
        {
            return await _tipingMlistViewServices.StartTipping(reqModel);
        }

		[HttpPost]
		public async Task<MessageModel<string>> ComplateTipping(TippingMlistViewModel reqModel)
		{
			return await _tipingMlistViewServices.ComplateTipping(reqModel);
		}


		/// <summary>
		/// 扫描库存投料
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		[HttpPost]
        public async Task<MessageModel<string>> Tipping([FromBody] TippingSclistRequestModel reqModel)
        {
            return await _tipingMlistViewServices.Tipping(reqModel);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> CheckTippingType([FromBody] string equipmentId)
        {
            return await _tipingMlistViewServices.CheckTippingType(equipmentId);
        }

        [HttpPost]
        public async Task<MessageModel<string>> DockScan([FromBody] DockScanModel reqModel)
        {
            return await _tipingMlistViewServices.DockScan(reqModel);
        }

        /// <summary>
        /// 获取下拉选
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <returns></returns>
        [HttpGet("{equipmentId}")]
        public async Task<MessageModel<List<DestinationSelect>>> GetTippingTransferSelectList(string equipmentId)
        {
            return await _tipingMlistViewServices.GetTippingTransferSelectList(equipmentId);
        }

        /// <summary>
        /// 获取已经选中的数据
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <returns></returns>
        [HttpGet("{equipmentId}")]
        public async Task<MessageModel<DestinationSelect>> GetTippingTransferSelect(string equipmentId)
        {
            return await _tipingMlistViewServices.GetTippingTransferSelect(equipmentId);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveSelect([FromBody] DestinationSelect reqModel)
        {
            return await _tipingMlistViewServices.SaveSelect(reqModel);
        }

        /// <summary>
        /// 获取Function属性值
        /// </summary>
        /// <param name="equipmentId">02405072-0370-0836-163e-0370f6000000</param>
        /// <param name="functionCode">Tipping</param>
        /// <param name="propertyName">TippingType、TipOnDock</param>
        /// <returns></returns>
        [HttpGet("{equipmentId}/{functionCode}/{propertyName}")]
        public async Task<MessageModel<string>> GetFunctionPropertyValue(string equipmentId, string functionCode, string propertyName)
        {
            return await _tipingMlistViewServices.GetFunctionPropertyValue(equipmentId, functionCode, propertyName);
        }

		[HttpPost]
		public async Task<MessageModel<PoProducedExecutionModel>> GetRunOrderFromSampleEquipment([FromBody] string sampleEquipment)
		{
			return await _tipingMlistViewServices.GetRunOrderFromSampleEquipment(sampleEquipment);
		}

		[HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _tipingMlistViewServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class TipingMlistViewRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}