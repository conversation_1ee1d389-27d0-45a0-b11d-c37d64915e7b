using SEFA.PPM.IServices;
using SEFA.Base.Model;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.PPM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.PPMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class ProduceLocationViewController : BaseApiController
    {
        /// <summary>
        /// ProduceLocationView
        /// </summary>
        private readonly IProduceLocationViewServices _produceLocationViewServices;
    
        public ProduceLocationViewController(IProduceLocationViewServices ProduceLocationViewServices)
        {
            _produceLocationViewServices = ProduceLocationViewServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<ProduceLocationViewEntity>>> GetList([FromBody] ProduceLocationViewRequestModel reqModel)
        {
            var data = await _produceLocationViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<ProduceLocationViewEntity>>> GetPageList([FromBody] ProduceLocationViewRequestModel reqModel)
        {
            var data = await _produceLocationViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<ProduceLocationViewEntity>> GetEntity(string id)
        {
            var data = await _produceLocationViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        //[HttpPost]
        //public async Task<MessageModel<string>> SaveForm([FromBody] ProduceLocationViewEntity request)
        //{
        //    var data = new MessageModel<string>();
        //    data.success = await _produceLocationViewServices.SaveForm(request);
        //    if (data.success)
        //    {
        //        return Success("", "添加成功");
        //    }
        //    else
        //    {
        //        return Failed("添加失败");
        //    }
        //}


        //[HttpPost]
        //public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        //{
        //    var data = new MessageModel<string>();
        //    data.success = await _produceLocationViewServices.DeleteByIds(ids);
        //    if (data.success)
        //    {
        //        return Success("", "删除成功");
        //    }
        //    else
        //    {
        //        return Failed( "删除失败");
        //    }
        //}
    }
    //public class ProduceLocationViewRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}