using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.IServices.PTM;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.PTM;
using System.Collections.Generic;
using System.Linq.Expressions;

namespace SEFA.PPM.Api.Controllers.PTM
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class DcsController : BaseApiController
    {
        /// <summary>
        /// BaseProperty
        /// </summary>
        private readonly IDcsServices _dcsServices;

        public DcsController( IDcsServices dcsServices)
        {
            _dcsServices = dcsServices;
        }

        /// <summary>
        /// 下发DCS 预约批次 
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SendBatch(List<SendBatchModel> list)
        {
            return await _dcsServices.SendBatch(list);
        }

        /// <summary>
        /// 下发DCS 物料开始投料比对反馈
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel> ChargeConfirm(ChargeConfirmModel model)
        {
            return await _dcsServices.ChargeConfirm(model);
        }


        /// <summary>
        /// 发送DCS POT物料投料开始结束信号
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel> PotCharge(ChargeModel model)
        {
            return await _dcsServices.PotCharge(model);
        }

        /// <summary>
        /// 发送DCS 取样开始结束信号
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel> SampleStartComplete(SampleModel model)
        {
            return await _dcsServices.SampleStartComplete(model);
        }

        /// <summary>
        /// 发送DCS 取样结果信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel> SampleResult(SampleResultModel model)
        {
            return await _dcsServices.SampleResult(model);
        }


        /// <summary>
        /// 接收工单状态变化
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel> BatchStateChange(BatchStateModel model)
        {
            return await _dcsServices.BatchStateChange(model);
        }

        /// <summary>
        /// 接收Unit状态变化
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel> UnitStateChange(UnitStateModel model)
        {
            return await _dcsServices.UnitStateChange(model);
        }

        /// <summary>
        /// 接收Operation状态变化
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel> OperationStateChange(OperationStateModel model)
        {
            return await _dcsServices.OperationStateChange(model);
        }

        /// <summary>
        /// 接收DCS投料请求
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel> ChargeRequest(ChargeModel model)
        {
            return await _dcsServices.ChargeRequest(model);
        }

        /// <summary>
        /// 接收DCS投料完成信号
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel> ChargeComplete(ChargeModel model)
        {
            return await _dcsServices.ChargeComplete(model);
        }

        /// <summary>
        /// 接收DCS储罐原料加料完成信号
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel> StorageTankMaterialCharge(StorageTankChargeModel model)
        {
            return await _dcsServices.StorageTankMaterialCharge(model);
        }

        /// <summary>
        /// 接收DCS工单转料完成信号
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel> MaterialTransfer(MaterialTransferModel model)
        {
            return await _dcsServices.MaterialTransfer(model);
        }

        /// <summary>
        /// 接收DCS取样请求
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel> SampleRequest(SampleRequestModel model)
        {
            return await _dcsServices.SampleRequest(model);
        }

        /// <summary>
        /// 接收DCS取样请求
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<DcsMsgModel>>> GetPoDcsInfo(BatchStateModel model)
        {
            var data = await _dcsServices.GetPoDcsInfo(model);
            return Success(data);
        }

        /// <summary>
        /// 扫描投料口获取投料请求记录
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<DcsChargeRequestEntity>> ScanInputMaterialEquipmentCode(DcsChargeRequestModel model)
        {
            return await _dcsServices.ScanInputMaterialEquipmentCode(model);
        }

        /// <summary>
        /// 扫描物料标签
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel> ScanMaterialBarCode(DcsChargeRequestModel model)
        {
            return await _dcsServices.ScanMaterialBarCode(model);
        }

        /// <summary>
        /// POT物料投料开始
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel> PotFeedingStart(DcsChargeRequestModel model)
        {
            return await _dcsServices.PotFeedingStart(model);
        }

        /// <summary>
        /// POT物料投料结束
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel> PotFeedingEnd(DcsChargeRequestModel model)
        {
            return await _dcsServices.PotFeedingEnd(model);
        }

        /// <summary>
        /// 录入物料投料数量
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel> InputConsumeQty(PoMaterialConsumeEntity model)
        {
            return await _dcsServices.InputConsumeQty(model);
        }
    }
       
   
}