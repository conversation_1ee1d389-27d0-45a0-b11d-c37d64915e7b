using SEFA.Base.Model;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.PPM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.PPM.IServices.PTM;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels.PTM;

namespace SEFA.PPM.Api.Controllers.PTM
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class TippingSclistController : BaseApiController
    {
        /// <summary>
        /// TippingSclist
        /// </summary>
        private readonly ITippingSclistServices _tippingSclistServices;

        public TippingSclistController(ITippingSclistServices TippingSclistServices)
        {
            _tippingSclistServices = TippingSclistServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<TippingSclistEntity>>> GetList([FromBody] TippingSclistRequestModel reqModel)
        {
            var data = await _tippingSclistServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<TippingSclistEntity>>> GetPageList([FromBody] TippingSclistRequestModel reqModel)
        {
            var data = await _tippingSclistServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<TippingSclistEntity>> GetEntity(string id)
        {
            var data = await _tippingSclistServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] TippingSclistEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _tippingSclistServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] TippingSclistEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _tippingSclistServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _tippingSclistServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class TippingSclistRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}