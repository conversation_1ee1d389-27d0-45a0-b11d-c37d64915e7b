using SEFA.Base.Model;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.PPM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.PPM.IServices.PTM;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels.PTM;

namespace SEFA.PPM.Api.Controllers.PTM
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class LogsheetImageController : BaseApiController
    {
        /// <summary>
        /// LogsheetImage
        /// </summary>
        private readonly ILogsheetImageServices _logsheetImageServices;

        public LogsheetImageController(ILogsheetImageServices LogsheetImageServices)
        {
            _logsheetImageServices = LogsheetImageServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<LogsheetImageEntity>>> GetList([FromBody] LogsheetImageRequestModel reqModel)
        {
            var data = await _logsheetImageServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<LogsheetImageEntity>>> GetPageList([FromBody] LogsheetImageRequestModel reqModel)
        {
            var data = await _logsheetImageServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<LogsheetImageEntity>> GetEntity(string id)
        {
            var data = await _logsheetImageServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] LogsheetImageEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _logsheetImageServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _logsheetImageServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] LogsheetImageEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _logsheetImageServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _logsheetImageServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class LogsheetImageRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}