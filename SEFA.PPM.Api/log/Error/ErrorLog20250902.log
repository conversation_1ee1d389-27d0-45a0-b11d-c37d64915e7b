2025-09-02 09:58:58.145 +08:00 [ERR] 
2025-09-02 09:58:58.944 +08:00 [ERR] Object reference not set to an instance of an object.
【自定义错误】：Object reference not set to an instance of an object. 
【异常类型】：NullReferenceException 
【异常信息】：Object reference not set to an instance of an object. 
【堆栈调用】：   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetPageList(CallMaterialHeaderRequestModel request) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\Interface\WMS\CallMaterialSheetController.cs:line 150
   at lambda_method735(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
System.NullReferenceException: Object reference not set to an instance of an object.
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetPageList(CallMaterialHeaderRequestModel request) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\Interface\WMS\CallMaterialSheetController.cs:line 150
   at lambda_method735(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
