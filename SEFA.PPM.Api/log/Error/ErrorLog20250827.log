2025-08-27 15:50:00.977 +08:00 [ERR] Conflicting method/path combination "POST api/CallMaterialSheet" for actions - SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.AddCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetProductionOrderListByLine (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetCallMaterialDetailsByOrder (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.UpdateCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.DeleteCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetPageList (SEFA.PPM.Api). Actions require a unique method/path combination for Swagger/OpenAPI 3.0. Use ConflictingActionsResolver as a workaround
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Conflicting method/path combination "POST api/CallMaterialSheet" for actions - SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.AddCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetProductionOrderListByLine (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetCallMaterialDetailsByOrder (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.UpdateCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.DeleteCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetPageList (SEFA.PPM.Api). Actions require a unique method/path combination for Swagger/OpenAPI 3.0. Use ConflictingActionsResolver as a workaround
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerDocumentWithoutFilters(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at SEFA.Base.Extensions.Middlewares.SwaggerAuthMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.IpLogMiddleware.InvokeAsync(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.SignalRSendMiddleware.InvokeAsync(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.RecordAccessLogsMiddleware.InvokeAsync(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.RequRespLogMiddleware.InvokeAsync(HttpContext context)
2025-08-27 15:50:13.919 +08:00 [ERR] Conflicting method/path combination "POST api/CallMaterialSheet" for actions - SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.AddCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetProductionOrderListByLine (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetCallMaterialDetailsByOrder (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.UpdateCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.DeleteCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetPageList (SEFA.PPM.Api). Actions require a unique method/path combination for Swagger/OpenAPI 3.0. Use ConflictingActionsResolver as a workaround
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Conflicting method/path combination "POST api/CallMaterialSheet" for actions - SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.AddCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetProductionOrderListByLine (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetCallMaterialDetailsByOrder (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.UpdateCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.DeleteCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetPageList (SEFA.PPM.Api). Actions require a unique method/path combination for Swagger/OpenAPI 3.0. Use ConflictingActionsResolver as a workaround
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerDocumentWithoutFilters(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at SEFA.Base.Extensions.Middlewares.SwaggerAuthMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.IpLogMiddleware.InvokeAsync(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.SignalRSendMiddleware.InvokeAsync(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.RecordAccessLogsMiddleware.InvokeAsync(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.RequRespLogMiddleware.InvokeAsync(HttpContext context)
2025-08-27 15:51:31.821 +08:00 [ERR] Conflicting method/path combination "POST api/CallMaterialSheet" for actions - SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.AddCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetProductionOrderListByLine (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetCallMaterialDetailsByOrder (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.UpdateCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.DeleteCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetPageList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.Add (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetPageList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetPageListWithDetails (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetListWithDetails (SEFA.PPM.Api). Actions require a unique method/path combination for Swagger/OpenAPI 3.0. Use ConflictingActionsResolver as a workaround
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Conflicting method/path combination "POST api/CallMaterialSheet" for actions - SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.AddCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetProductionOrderListByLine (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetCallMaterialDetailsByOrder (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.UpdateCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.DeleteCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetPageList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.Add (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetPageList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetPageListWithDetails (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetListWithDetails (SEFA.PPM.Api). Actions require a unique method/path combination for Swagger/OpenAPI 3.0. Use ConflictingActionsResolver as a workaround
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerDocumentWithoutFilters(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at SEFA.Base.Extensions.Middlewares.SwaggerAuthMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.IpLogMiddleware.InvokeAsync(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.SignalRSendMiddleware.InvokeAsync(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.RecordAccessLogsMiddleware.InvokeAsync(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.RequRespLogMiddleware.InvokeAsync(HttpContext context)
2025-08-27 15:57:11.090 +08:00 [ERR] Conflicting method/path combination "POST api/CallMaterialSheet" for actions - SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.AddCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetProductionOrderListByLine (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetCallMaterialDetailsByOrder (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.UpdateCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.DeleteCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetPageList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.Add (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetPageList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetPageListWithDetails (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetListWithDetails (SEFA.PPM.Api). Actions require a unique method/path combination for Swagger/OpenAPI 3.0. Use ConflictingActionsResolver as a workaround
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Conflicting method/path combination "POST api/CallMaterialSheet" for actions - SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.AddCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetProductionOrderListByLine (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetCallMaterialDetailsByOrder (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.UpdateCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.DeleteCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetPageList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.Add (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetPageList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetPageListWithDetails (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetListWithDetails (SEFA.PPM.Api). Actions require a unique method/path combination for Swagger/OpenAPI 3.0. Use ConflictingActionsResolver as a workaround
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerDocumentWithoutFilters(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at SEFA.Base.Extensions.Middlewares.SwaggerAuthMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.IpLogMiddleware.InvokeAsync(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.SignalRSendMiddleware.InvokeAsync(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.RecordAccessLogsMiddleware.InvokeAsync(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.RequRespLogMiddleware.InvokeAsync(HttpContext context)
2025-08-27 15:59:30.960 +08:00 [ERR] Conflicting method/path combination "POST api/CallMaterialSheet" for actions - SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.AddCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetProductionOrderListByLine (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetCallMaterialDetailsByOrder (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.UpdateCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.DeleteCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetPageList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.Add (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetPageList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetPageListWithDetails (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetListWithDetails (SEFA.PPM.Api). Actions require a unique method/path combination for Swagger/OpenAPI 3.0. Use ConflictingActionsResolver as a workaround
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Conflicting method/path combination "POST api/CallMaterialSheet" for actions - SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.AddCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetProductionOrderListByLine (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetCallMaterialDetailsByOrder (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.UpdateCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.DeleteCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetPageList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.Add (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetPageList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetPageListWithDetails (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetListWithDetails (SEFA.PPM.Api). Actions require a unique method/path combination for Swagger/OpenAPI 3.0. Use ConflictingActionsResolver as a workaround
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerDocumentWithoutFilters(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at SEFA.Base.Extensions.Middlewares.SwaggerAuthMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.IpLogMiddleware.InvokeAsync(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.SignalRSendMiddleware.InvokeAsync(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.RecordAccessLogsMiddleware.InvokeAsync(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.RequRespLogMiddleware.InvokeAsync(HttpContext context)
2025-08-27 15:59:40.056 +08:00 [ERR] Conflicting method/path combination "POST api/CallMaterialSheet" for actions - SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.AddCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetProductionOrderListByLine (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetCallMaterialDetailsByOrder (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.UpdateCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.DeleteCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetPageList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.Add (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetPageList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetPageListWithDetails (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetListWithDetails (SEFA.PPM.Api). Actions require a unique method/path combination for Swagger/OpenAPI 3.0. Use ConflictingActionsResolver as a workaround
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Conflicting method/path combination "POST api/CallMaterialSheet" for actions - SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.AddCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetProductionOrderListByLine (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetCallMaterialDetailsByOrder (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.UpdateCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.DeleteCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetPageList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.Add (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetPageList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetPageListWithDetails (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetListWithDetails (SEFA.PPM.Api). Actions require a unique method/path combination for Swagger/OpenAPI 3.0. Use ConflictingActionsResolver as a workaround
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerDocumentWithoutFilters(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at SEFA.Base.Extensions.Middlewares.SwaggerAuthMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.IpLogMiddleware.InvokeAsync(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.SignalRSendMiddleware.InvokeAsync(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.RecordAccessLogsMiddleware.InvokeAsync(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.RequRespLogMiddleware.InvokeAsync(HttpContext context)
2025-08-27 15:59:54.772 +08:00 [ERR] Conflicting method/path combination "POST api/CallMaterialSheet" for actions - SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.AddCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetProductionOrderListByLine (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetCallMaterialDetailsByOrder (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.UpdateCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.DeleteCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetPageList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.Add (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetPageList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetPageListWithDetails (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetListWithDetails (SEFA.PPM.Api). Actions require a unique method/path combination for Swagger/OpenAPI 3.0. Use ConflictingActionsResolver as a workaround
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Conflicting method/path combination "POST api/CallMaterialSheet" for actions - SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.AddCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetProductionOrderListByLine (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetCallMaterialDetailsByOrder (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.UpdateCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.DeleteCallMaterialSheet (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.CallMaterialSheetController.GetPageList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.Add (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetPageList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetList (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetPageListWithDetails (SEFA.PPM.Api),SEFA.PPM.Api.Controllers.Interface.WMS.DistributionMaterialRequestController.GetListWithDetails (SEFA.PPM.Api). Actions require a unique method/path combination for Swagger/OpenAPI 3.0. Use ConflictingActionsResolver as a workaround
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerDocumentWithoutFilters(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at SEFA.Base.Extensions.Middlewares.SwaggerAuthMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.IpLogMiddleware.InvokeAsync(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.SignalRSendMiddleware.InvokeAsync(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.RecordAccessLogsMiddleware.InvokeAsync(HttpContext context)
   at SEFA.Base.Extensions.Middlewares.RequRespLogMiddleware.InvokeAsync(HttpContext context)
2025-08-27 16:16:37.915 +08:00 [ERR] 
2025-08-27 16:16:38.094 +08:00 [ERR] 
2025-08-27 17:21:20.442 +08:00 [ERR] 
2025-08-27 17:21:20.557 +08:00 [ERR] 
2025-08-27 17:38:19.370 +08:00 [ERR] 
2025-08-27 17:38:19.490 +08:00 [ERR] 
2025-08-27 17:44:36.528 +08:00 [ERR] 
2025-08-27 17:44:36.647 +08:00 [ERR] 
2025-08-27 18:09:46.447 +08:00 [ERR] 
2025-08-27 18:09:46.561 +08:00 [ERR] 
2025-08-27 18:11:07.121 +08:00 [ERR] 
2025-08-27 18:11:07.232 +08:00 [ERR] 
2025-08-27 18:18:16.183 +08:00 [ERR] 
2025-08-27 18:18:16.290 +08:00 [ERR] 
