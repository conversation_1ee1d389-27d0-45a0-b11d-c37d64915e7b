using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.MKM.Model.Models.MKM
{
	public class Select
	{
		public string key { get; set; }

		public string value { get; set; }

		public bool isSelect { get; set; }
	}

	public class DestinationSelect : Select
	{
		public string equipmentId { get; set; }

		public string equipmentActionId { get; set; }
	}

	public class BatchSelect : Select
	{
		public string segmentCode { get; set; }

		public string segmentName { get; set; }

		public int number { get; set; }
	}
}
