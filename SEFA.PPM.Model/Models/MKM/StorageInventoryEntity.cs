using System;
using System.Linq;
using System.Text;
using SqlSugar;
using SEFA.Base.Model.BASE;

namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///仓库库存实时
    ///</summary>
    [SugarTable("MKM_B_STORAGE_INVENTORY")]
    public class StorageInventoryEntity : EntityBase
    {
        public StorageInventoryEntity()
        {
        }

        /// <summary>
        /// Desc:仓库编码
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "STORAGE_CODE")]
        public string StorageCode { get; set; }

        /// <summary>
        /// Desc:仓库ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "STORAGE_ID")]
        public string StorageId { get; set; }

        /// <summary>
        /// Desc:物料ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_ID")]
        public string MaterialId { get; set; }

        /// <summary>
        /// Desc:物料编码
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_CODE")]
        public string MaterialCode { get; set; }

        /// <summary>
        /// Desc:物料名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_NAME")]
        public string MaterialName { get; set; }

        /// <summary>
        /// Desc:物料版本ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_VERSION_ID")]
        public string MaterialVersionId { get; set; }

        /// <summary>
        /// Desc:物料版本号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_VERSION_CODE")]
        public string MaterialVersionCode { get; set; }

        /// <summary>
        /// Desc:单位ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "UNIT_ID")]
        public string UnitId { get; set; }

        /// <summary>
        /// Desc:单位
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "UNIT")]
        public string Unit { get; set; }

        /// <summary>
        /// Desc:数量
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "QUANTITY")]
        public decimal? Quantity { get; set; }

        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }
    }
}