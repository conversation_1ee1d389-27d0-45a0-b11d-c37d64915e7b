using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.MKM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("MKM_M_CONTAINER_GROUP")] 
    public class ContainerGroupEntity : EntityBase
    {
        public ContainerGroupEntity()
        {
        }
           /// <summary>
           /// Desc:名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="NAME")]
        public string Name { get; set; }
           /// <summary>
           /// Desc:类型
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TYPE")]
        public string Type { get; set; }
           /// <summary>
           /// Desc:最大重复循环次数
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MAX_CYCLE_COUNT")]
        public string MaxCycleCount { get; set; }
           /// <summary>
           /// Desc:最大单次使用时间
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MAX_DURATION_BEFORE_CLEAN")]
        public string MaxDurationBeforeClean { get; set; }
           /// <summary>
           /// Desc:容器分类ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="CLASS_ID")]
        public string ClassId { get; set; }

    }
}