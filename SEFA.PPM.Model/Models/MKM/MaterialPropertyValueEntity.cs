using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_MATERIAL_PROPERTY_VALUE")] 
    public class MaterialPropertyValueEntity : EntityBase
    {
        public MaterialPropertyValueEntity()
        {
        }
           /// <summary>
           /// Desc:物料ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_ID")]
        public string MaterialId { get; set; }
           /// <summary>
           /// Desc:分类ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="CLASS_ID")]
        public string ClassId { get; set; }
           /// <summary>
           /// Desc:属性编码
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PROPERTY_CODE")]
        public string PropertyCode { get; set; }
           /// <summary>
           /// Desc:属性值
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PROPERTY_VALUE")]
        public string PropertyValue { get; set; }
           /// <summary>
           /// Desc:是否删除
           /// Default:0
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="REMARK")]
        public string Remark { get; set; }
    }
}