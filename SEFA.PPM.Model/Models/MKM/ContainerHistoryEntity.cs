using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.MKM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("MKM_L_CONTAINER_HISTORY")] 
    public class ContainerHistoryEntity : EntityBase
    {
        public ContainerHistoryEntity()
        {
        }
           /// <summary>
           /// Desc:容器ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="CONTAINER_ID")]
        public string ContainerId { get; set; }
           /// <summary>
           /// Desc:操作类型
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="TYPE")]
        public string Type { get; set; }
           /// <summary>
           /// Desc:关联节点ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="EQUIPMENT_ID")]
        public string EquipmentId { get; set; }
           /// <summary>
           /// Desc:库存节点ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="EQUIPMENT_REQUIREMENT_ID")]
        public string EquipmentRequirementId { get; set; }
           /// <summary>
           /// Desc:容器状态
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="STATE")]
        public string State { get; set; }
           /// <summary>
           /// Desc:操作记录
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="COMMENT")]
        public string Comment { get; set; }
           /// <summary>
           /// Desc:工单ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PRODUCT_ORDER_ID")]
        public string ProductOrderId { get; set; }
           /// <summary>
           /// Desc:工单批次ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="BATCH_ID")]
        public string BatchId { get; set; }
           /// <summary>
           /// Desc:物料ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_ID")]
        public string MaterialId { get; set; }
           /// <summary>
           /// Desc:子批次ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="SUBLOT_ID")]
        public string SublotId { get; set; }
           /// <summary>
           /// Desc:数量
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="QUANTITY")]
        public string Quantity { get; set; }
           /// <summary>
           /// Desc:数量单位ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="QUANTITY_UOM_ID")]
        public string QuantityUomId { get; set; }
           /// <summary>
           /// Desc:批次用量需求ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="BATCH_CONSUMED_REQUIREMENT_ID")]
        public string BatchConsumedRequirementId { get; set; }
           /// <summary>
           /// Desc:工单用量需求ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="CONSUMED_REQUIREMENT_ID")]
        public string ConsumedRequirementId { get; set; }
           /// <summary>
           /// Desc:工单执行ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PRODUCTION_EXECUTION_ID")]
        public string ProductionExecutionId { get; set; }
           /// <summary>
           /// Desc:批次执行状态
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="STATUS")]
        public string Status { get; set; }
           /// <summary>
           /// Desc:容器编号
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="CONTAINER_CODE")]
        public string ContainerCode { get; set; }
           /// <summary>
           /// Desc:物料产出记录ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_PRODUCED_ACTUAL_ID")]
        public string MaterialProducedActualId { get; set; }
           /// <summary>
           /// Desc:物料消耗记录ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_CONSUMED_ACTUAL_ID")]
        public string MaterialConsumedActualId { get; set; }
           /// <summary>
           /// Desc:批次ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="LOT_ID")]
        public string LotId { get; set; }
           /// <summary>
           /// Desc:有效期
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="EXPIRATION_DATE")]
        public DateTime? ExpirationDate { get; set; }

    }
}