using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("V_MKM_PACK_TECHNOLOGY_VIEW")] 
    public class PackTechnologyViewEntity : EntityBase
    {
        public PackTechnologyViewEntity()
        {
        }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PRODUCTION_ORDER_NO")]
        public string ProductionOrderNo { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="CODE")]
        public string Code { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_NAME")]
        public string MaterialName { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PLAN_QTY")]
        public decimal PlanQty { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="UNIT_NAME")]
        public string UnitName { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="LINE_NAME")]
        public string LineName { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PLAN_START_TIME")]
        public DateTime? PlanStartTime { get; set; }

    }
}