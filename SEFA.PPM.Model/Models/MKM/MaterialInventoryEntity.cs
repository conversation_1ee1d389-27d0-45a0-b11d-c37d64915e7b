using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.MKM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("MKM_B_MATERIAL_INVENTORY")]
    public class MaterialInventoryEntity : EntityBase
    {
        public MaterialInventoryEntity()
        {
        }
        /// <summary>
        /// Desc:物料批次ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "LOT_ID")]
        public string LotId { get; set; }
        /// <summary>
        /// Desc:物料子批次ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SUBLOT_ID")]
        public string SublotId { get; set; }
        /// <summary>
        /// Desc:数量
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "QUANTITY")]
        public decimal Quantity { get; set; }
        /// <summary>
        /// Desc:数量单位
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "QUANTITY_UOM_ID")]
        public string QuantityUomId { get; set; }
        /// <summary>
        /// Desc:存储位置
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "STORAGE_LOCATION")]
        public string StorageLocation { get; set; }
        /// <summary>
        /// Desc:存储区ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "EQUIPMENT_ID")]
        public string EquipmentId { get; set; }
        /// <summary>
        /// Desc:是否预检查
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "IS_PRECHECKED")]
        public string IsPrechecked { get; set; }
        /// <summary>
        /// Desc:容器ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CONTAINER_ID")]
        public string ContainerId { get; set; }
        /// <summary>
        /// Desc:产品工单ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PRODUCTION_REQUEST_ID")]
        public string ProductionRequestId { get; set; }
        /// <summary>
        /// Desc:顺序号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SORT_ORDER")]
        public int? SortOrder { get; set; }
        /// <summary>
        /// Desc:PPM_B_BATCHPPM表批次ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "BATCH_ID")]
        public string BatchId { get; set; }
        /// <summary>
        /// Desc:状态
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "INVENTORY_TYPE")]
        public string InventoryType { get; set; }
        /// <summary>
        /// Desc:复称状态
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "IS_WEIGHING_CHECK")]
        public string IsWeighingCheck { get; set; }
        /// <summary>
        /// Desc:备注字段
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REMARK")]
        public string Remark { get; set; }
        /// <summary>
        /// Desc:桶号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "BUCKETNUM")]
        public string Bucketnum { get; set; }
        /// <summary>
        /// Desc:备料标签选择的日期
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CREATETIME")]
        public DateTime? Createtime { get; set; }
        /// <summary>
        /// Desc:是否是喉头（1代表是喉头）
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "Is_Thorat")]
        public string IsThorat { get; set; }
        
        /// <summary>
        /// Desc:批次消耗需求ID
        /// Default:
        /// Nullable:false
        /// </summary>
        [SugarColumn(ColumnName = "BATCH_CONSUME_REQUIREMENT_ID")]
        public string BatchConsumeRequirementId { get; set; }
        
        /// <summary>
        /// 成本中心
        /// </summary>
        [SugarColumn(ColumnName = "PLANT")]
        public string Plant { get; set; }
        
        /// <summary>
        /// 密度
        /// </summary>
        [SugarColumn(ColumnName = "DENSITY", ColumnDataType = "decimal(18,6)")]
        public decimal? Density { get; set; }

        /// <summary>
        /// Coa含量%
        /// </summary>
        [SugarColumn(ColumnName = "COA_CONTENT", ColumnDataType = "decimal(18,6)")]
        public decimal? CoAContent { get; set; }

    }
}