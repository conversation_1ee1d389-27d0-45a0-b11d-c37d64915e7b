using System;
using System.Linq;
using System.Text;
using SqlSugar;
using SEFA.Base.Model.BASE;

namespace SEFA.MKM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("MM_M_WAREHOUSE_STORAGE")]
    public class WarehouseStorageEntity : EntityBase
    {
        public WarehouseStorageEntity()
        {
        }

        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SAP_HOUSE")]
        public string SapHouse { get; set; }

        /// <summary>
        /// Desc:
        /// Default:NULL
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "WAREHOUSEID")]
        public string Warehouseid { get; set; }

        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "WAREHOUSENAME")]
        public string Warehousename { get; set; }

        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATERIALCODE")]
        public string Materialcode { get; set; }

        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATERIALNAME")]
        public string Materialname { get; set; }
        
        [SugarColumn(ColumnName = "MATERIALVERSIONID")]
        public string MaterialVersionId { get; set; }
        
        [SugarColumn(ColumnName = "MATERIALVERSION")]
        public string MaterialVersion { get; set; }

        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "NUM")]
        public decimal? Num { get; set; }

        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "UNIT")]
        public string Unit { get; set; }

        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "WAREHOUSECODE")]
        public string Warehousecode { get; set; }
    }
}