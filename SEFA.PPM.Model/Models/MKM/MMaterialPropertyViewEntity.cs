using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.MKM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("V_DFM_M_MATERIAL_PROPERTY_VIEW")]
    public class MMaterialPropertyViewEntity : EntityBase
    {
        public MMaterialPropertyViewEntity()
        {
        }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_ID")]
        public string MaterialId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_TYPE")]
        public string MaterialType { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PROPERTY_VALUE")]
        public string PropertyValue { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PROPERTY_CODE")]
        public string PropertyCode { get; set; }

    }
}