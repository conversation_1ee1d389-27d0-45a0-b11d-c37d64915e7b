using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.MKM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("MKM_B_MATERIAL_SUB_LOT")]
    public class MaterialSubLotEntity : EntityBase
    {
        public MaterialSubLotEntity()
        {
        }
        /// <summary>
        /// Desc:子批次号
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "SUB_LOT_ID")]
        public string SubLotId { get; set; }
        /// <summary>
        /// Desc:类型
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "TYPE")]
        public string Type { get; set; }
        /// <summary>
        /// Desc:状态(1:B 上锁 2:Q 3:U 解锁)
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "EXTERNAL_STATUS")]
        public string ExternalStatus { get; set; }
        /// <summary>
        /// Desc:处置ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "DISPOSITION_ID")]
        public string DispositionId { get; set; }
        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "COMMENT")]
        public string Comment { get; set; }
    }
}