using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.MKM.Model.Models
{
    ///<summary>
    ///根据容器获取当前的所有物料数据
    ///</summary>
    
    [SugarTable("V_MKM_BATCH_PALLET_SELECTM_VIEW")] 
    public class BatchPalletSelectmViewEntity : EntityBase
    {
        public BatchPalletSelectmViewEntity()
        {
        }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_ID")]
        public string MaterialId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="I_QUANTITY")]
        public decimal? IQuantity { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="CONTAINER_ID")]
        public string ContainerId { get; set; }

    }
}