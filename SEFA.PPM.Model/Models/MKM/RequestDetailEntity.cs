using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("MKM_M_REQUEST_DETAIL")] 
    public class RequestDetailEntity : EntityBase
    {
        public RequestDetailEntity()
        {
        }
           /// <summary>
           /// Desc:请料ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="INVENTORY_REQUEST_ID")]
        public string InventoryRequestId { get; set; }
           /// <summary>
           /// Desc:数量
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="QUANTITY")]
        public decimal Quantity { get; set; }
           /// <summary>
           /// Desc:状态
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="STATUS")]
        public string Status { get; set; }
           /// <summary>
           /// Desc:单包规格
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="BAG_WEIGHT")]
        public decimal BagWeight { get; set; }
           /// <summary>
           /// Desc:整柜包数
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PALLET_NUMBER")]
        public int PalletNumber { get; set; }
           /// <summary>
           /// Desc:需求柜数
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PALLET")]
        public int Pallet { get; set; }
           /// <summary>
           /// Desc:收货数量
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="ACTUAL_QUANTITY")]
        public decimal ActualQuantity { get; set; }
           /// <summary>
           /// Desc:计划到货时间()
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PLANNED_TIME")]
        public DateTime? PlannedTime { get; set; }
           /// <summary>
           /// Desc:收货时间（实际）
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ACTUAL_TIME")]
        public DateTime? ActualTime { get; set; }
           /// <summary>
           /// Desc:批次号
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="LOTNO")]
        public string Lotno { get; set; }
           /// <summary>
           /// Desc:MFG3/PKG3
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="TYPE")]
        public string Type { get; set; }
           /// <summary>
           /// Desc:批次号
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="BatchNo")]
        public string Batchno { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="REMARK")]
        public string Remark { get; set; }
    }
}