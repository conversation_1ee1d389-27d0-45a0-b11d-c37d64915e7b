using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using Magicodes.ExporterAndImporter.Core;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    public class DowntimetgtExcelDto
    {
        public DowntimetgtExcelDto()
        {
        }
        /// <summary>
        /// Desc:生产线
        /// Default:
        /// Nullable:False
        /// </summary>
        [ImporterHeader(Name = "生产线")]
        [ExporterHeader(DisplayName = "生产线")]
        public string LineName { get; set; }
       /* /// <summary>
        /// Desc:对应物理模型区域
        /// Default:
        /// Nullable:False
        /// </summary>
        [ImporterHeader(Name = "模型区域")]
        [ExporterHeader(DisplayName = "模型区域")]
        public string ModelRefName { get; set; }*/
        /// <summary>
        /// Desc:年
        /// Default:
        /// Nullable:False
        /// </summary>
        [ImporterHeader(Name = "年份")]
        [ExporterHeader(DisplayName = "年份")]
        public int Year { get; set; }
        /// <summary>
        /// Desc:目标值
        /// Default:
        /// Nullable:False
        /// </summary>
        [ImporterHeader(Name = "目标值")]
        [ExporterHeader(DisplayName = "目标值")]
        public decimal Tgt { get; set; }
        /// <summary>
        /// Desc:单位
        /// Default:
        /// Nullable:False
        /// </summary>
        [ImporterHeader(Name = "单位")]
        [ExporterHeader(DisplayName = "单位")]
        public string UnitName { get; set; }
  

    }
}