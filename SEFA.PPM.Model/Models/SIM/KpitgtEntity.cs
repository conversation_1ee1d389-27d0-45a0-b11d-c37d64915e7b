using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("SIM_B_KPITGT")] 
    public class KpitgtEntity : EntityBase
    {
        public KpitgtEntity()
        {
        }
           /// <summary>
           /// Desc:数据名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DATA_NAME")]
        public string DataName { get; set; }
           /// <summary>
           /// Desc:数据类型
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DATE_TYPE")]
        public string DateType { get; set; }
           /// <summary>
           /// Desc:对应物理模型区域
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MODEL_REF")]
        public string ModelRef { get; set; }
           /// <summary>
           /// Desc:年
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="YEAR")]
        public int Year { get; set; }
           /// <summary>
           /// Desc:月
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MONTH")]
        public int Month { get; set; }
           /// <summary>
           /// Desc:目标值
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TGT")]
        public decimal Tgt { get; set; }
           /// <summary>
           /// Desc:单位
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="UNIT")]
        public string Unit { get; set; }

    }
}