using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("SIM_B_IMTABLE_OEE")] 
    public class ImtableOeeEntity : EntityBase
    {
        public ImtableOeeEntity()
        {
        }
           /// <summary>
           /// Desc:年
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="YEAR")]
        public int Year { get; set; }
           /// <summary>
           /// Desc:月
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MONTH")]
        public int Month { get; set; }
           /// <summary>
           /// Desc:日
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DAY")]
        public int Day { get; set; }
           /// <summary>
           /// Desc:生产线
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="LINE_ID")]
        public string LineId { get; set; }
           /// <summary>
           /// Desc:工作中心
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="WORK_CENTER")]
        public string WorkCenter { get; set; }
           /// <summary>
           /// Desc:成本中心
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="COST_CENTER")]
        public string CostCenter { get; set; }
           /// <summary>
           /// Desc:总运行时间
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TOTAL_RUNTIME")]
        public decimal TotalRuntime { get; set; }
           /// <summary>
           /// Desc:非计划停机时间
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TOTAL_UNPLANNEDTIME")]
        public decimal TotalUnplannedtime { get; set; }
           /// <summary>
           /// Desc:计划停机时间
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TOTAL_PLANNEDTIME")]
        public decimal TotalPlannedtime { get; set; }
           /// <summary>
           /// Desc:总产量（支）
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TOTAL_PRODUCTION")]
        public decimal TotalProduction { get; set; }
           /// <summary>
           /// Desc:标准运行速度
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="STD_SPEED")]
        public decimal StdSpeed { get; set; }
           /// <summary>
           /// Desc:总产量标准时间
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="STD_PRODUCTION_TIME")]
        public decimal StdProductionTime { get; set; }
           /// <summary>
           /// Desc:实际产量（支）
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="ACTUAL_PRODUCTION")]
        public decimal ActualProduction { get; set; }
           /// <summary>
           /// Desc:当月自然日数量
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="NATURLADAY_NUM")]
        public decimal NaturladayNum { get; set; }
           /// <summary>
           /// Desc:当月工作日数量
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="WORKINGDAY_NUM")]
        public decimal WorkingdayNum { get; set; }

    }
}