using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
	///<summary>
	///
	///</summary>

	[SugarTable("SIM_B_IMTABLE_COKADJUSTMENT")]
	public class ImtableCokadjustmentEntity : EntityBase
	{
		public ImtableCokadjustmentEntity()
		{
		}
		/// <summary>
		/// Desc:年
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "YEAR")]
		public int Year { get; set; }
		/// <summary>
		/// Desc:月
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "MONTH")]
		public int Month { get; set; }
		/// <summary>
		/// Desc:日
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "DAY")]
		public int Day { get; set; }
		/// <summary>
		/// Desc:生产线
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "LINE_ID")]
		public string LineId { get; set; }
		/// <summary>
		/// Desc:工作中心
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "WORK_CENTER")]
		public string WorkCenter { get; set; }
		/// <summary>
		/// Desc:配方
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "FORMULA")]
		public string Formula { get; set; }
		/// <summary>
		/// Desc:酱料系列
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "FORMULA_GROUP")]
		public string FormulaGroup { get; set; }
		/// <summary>
		/// Desc:检验总缸数
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "QA_SUM")]
		public decimal QaSum { get; set; }
		/// <summary>
		/// Desc:调节缸数
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "ADJUSTMENT_SUM")]
		public decimal AdjustmentSum { get; set; }
		/// <summary>
		/// Desc:放行缸数
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "RELEASED_SUM")]
		public decimal ReleasedSum { get; set; }
		/// <summary>
		/// Desc:返缸数
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "REWORK_SUM")]
		public decimal ReworkSum { get; set; }
		/// <summary>
		/// Desc:工单Id
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "PO_ID")]
		public string PoId { get; set; }
		/// <summary>
		/// Desc:酱料类别
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "PRODUCTION_CATEGORY")]
		public string ProductionCategory { get; set; }
	}
}