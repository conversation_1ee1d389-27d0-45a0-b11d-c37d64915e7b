using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("SIM_B_SAFETYTGT")] 
    public class SafetytgtEntity : EntityBase
    {
        public SafetytgtEntity()
        {
        }
           /// <summary>
           /// Desc:安全事件类型
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="EVENT_TYPE")]
        public string EventType { get; set; }
           /// <summary>
           /// Desc:对应物理模型区域
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MODEL_REF")]
        public string ModelRef { get; set; }
        /// <summary>
        /// Desc:评论
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "COMMENTS")]
        public string Comments { get; set; }
        /// <summary>
        /// Desc:日期
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName="DATE_OF_OCCURRENCE")]
        public DateTime DateOfOccurrence { get; set; }

    }
}