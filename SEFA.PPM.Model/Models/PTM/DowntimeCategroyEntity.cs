using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace SEFA.PPM.Model.Models
{
	///<summary>
	///
	///</summary>

	[SugarTable("PTM_M_DOWNTIME_CATEGROY")]
	public class DowntimeCategroyEntity : EntityBase
	{
		public DowntimeCategroyEntity()
		{
		}
		/// <summary>
		/// Desc:描述
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "DESCRIPTION")]
		public string Description { get; set; }
		/// <summary>
		/// Desc:状态
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "STATUS")]
		public string Status { get; set; }

	}

	public class DownTimeReasonTree 
	{
		public string ID { get; set; }

		public string ParentId { get; set; }

		public string Description { get; set; }

		public string Status { get; set; }

		public string Type { get; set; }

		public bool? IsRelevant { get; set; }

		public List<DownTimeReasonTree> Children { get; set; }
	}

}