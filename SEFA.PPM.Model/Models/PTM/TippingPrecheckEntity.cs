using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;

namespace SEFA.PPM.Model.Models.PTM
{
    ///<summary>
    ///生产执行-投料前检查-新
    ///</summary>

    [SugarTable("V_PTM_TIPPING_PRECHECK")]
    public class TippingPrecheckEntity : EntityBase
    {
        public TippingPrecheckEntity()
        {
        }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        public long Sequence { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "SequenceTotal")]
        public int Sequencetotal { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "NAME")]
        public string Name { get; set; }
        ///// <summary>
        ///// Desc:
        ///// Default:
        ///// Nullable:True
        ///// </summary>
        //[SugarColumn(ColumnName = "AUTOMATION_ID")]
        //public string AutomationId { get; set; }
        ///// <summary>
        ///// Desc:
        ///// Default:
        ///// Nullable:True
        ///// </summary>
        //[SugarColumn(ColumnName = "COMMENT")]
        //public string Comment { get; set; }
        ///// <summary>
        ///// Desc:
        ///// Default:
        ///// Nullable:False
        ///// </summary>
        //[SugarColumn(ColumnName = "STATUS")]
        //public string Status { get; set; }
        ///// <summary>
        ///// Desc:
        ///// Default:
        ///// Nullable:False
        ///// </summary>
        //[SugarColumn(ColumnName = "CLASS")]
        //public string Class { get; set; }
        ///// <summary>
        ///// Desc:
        ///// Default:
        ///// Nullable:True
        ///// </summary>
        //[SugarColumn(ColumnName = "GROUP_ID")]
        //public string GroupId { get; set; }
        ///// <summary>
        ///// Desc:
        ///// Default:
        ///// Nullable:True
        ///// </summary>
        //[SugarColumn(ColumnName = "SORT_ORDER")]
        //public int? SortOrder { get; set; }
        ///// <summary>
        ///// Desc:
        ///// Default:
        ///// Nullable:True
        ///// </summary>
        //[SugarColumn(ColumnName = "TARE_WEIGHT")]
        //public int? TareWeight { get; set; }
        ///// <summary>
        ///// Desc:
        ///// Default:
        ///// Nullable:True
        ///// </summary>
        //[SugarColumn(ColumnName = "MAX_WEIGHT")]
        //public int? MaxWeight { get; set; }
        ///// <summary>
        ///// Desc:
        ///// Default:
        ///// Nullable:True
        ///// </summary>
        //[SugarColumn(ColumnName = "WEIGHT_UOM_ID")]
        //public string WeightUomId { get; set; }
        ///// <summary>
        ///// Desc:
        ///// Default:
        ///// Nullable:True
        ///// </summary>
        //[SugarColumn(ColumnName = "LAST_CLEANED_AT_UTC")]
        //public DateTime? LastCleanedAtUtc { get; set; }
        ///// <summary>
        ///// Desc:
        ///// Default:
        ///// Nullable:True
        ///// </summary>
        //[SugarColumn(ColumnName = "LAST_CLEANED_USER")]
        //public string LastCleanedUser { get; set; }
        ///// <summary>
        ///// Desc:
        ///// Default:
        ///// Nullable:True
        ///// </summary>
        //[SugarColumn(ColumnName = "CLEANING_COUNT")]
        //public int? CleaningCount { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PRODUCTION_BATCH_ID")]
        public string ProductionBatchId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PRODUCTION_REQUEST_ID")]
        public string ProductionRequestId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "EQUIPMENT_ID")]
        public string EquipmentId { get; set; }
        ///// <summary>
        ///// Desc:
        ///// Default:
        ///// Nullable:True
        ///// </summary>
        //[SugarColumn(ColumnName = "CYCLE_COUNT")]
        //public int? CycleCount { get; set; }
        ///// <summary>
        ///// Desc:
        ///// Default:
        ///// Nullable:True
        ///// </summary>
        //[SugarColumn(ColumnName = "MAX_VOLUME")]
        //public int? MaxVolume { get; set; }
        ///// <summary>
        ///// Desc:
        ///// Default:
        ///// Nullable:True
        ///// </summary>
        //[SugarColumn(ColumnName = "VOLUME_UOM_ID")]
        //public string VolumeUomId { get; set; }
        ///// <summary>
        ///// Desc:
        ///// Default:
        ///// Nullable:True
        ///// </summary>
        //[SugarColumn(ColumnName = "PARENT_CONTAINER_ID")]
        //public string ParentContainerId { get; set; }
        ///// <summary>
        ///// Desc:
        ///// Default:
        ///// Nullable:True
        ///// </summary>
        //[SugarColumn(ColumnName = "LAST_MATERIAL_DEFINITION_ID")]
        //public string LastMaterialDefinitionId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "LINE_ID")]
        public string LineId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "LINE_CODE")]
        public string LineCode { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "LINE_NAME")]
        public string LineName { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PRODUCTION_ORDER_ID")]
        public string ProductionOrderId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PRODUCTION_ORDER_NO")]
        public string ProductionOrderNo { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "Formula_NO")]
        public string FormulaNo { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MACHINE")]
        public string Machine { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "NUMBER")]
        public string Number { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "BATCH_CODE")]
        public string BatchCode { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_ID")]
        public string MaterialId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_CODE")]
        public string MaterialCode { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_DESCRIPTION")]
        public string MaterialDescription { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PREP_STATUS")]
        public string PrepStatus { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REVIEWUSERID")]
        public string Reviewuserid { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REVIEWTIME")]
        public DateTime? Reviewtime { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PreProcessText")]
        public string Preprocesstext { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PLAN_START_TIME")]
        public DateTime? PlanStartTime { get; set; }
    }
}