using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models.PTM
{
	///<summary>
	///
	///</summary>

	[SugarTable("PTM_B_DOWNTIME")]
	public class DowntimeEntity : EntityBase,ICloneable
	{
		public DowntimeEntity()
		{
		}
		/// <summary>
		/// Desc:设备ID
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "EQUIPMENT_ID")]
		public string EquipmentId { get; set; }
		/// <summary>
		/// Desc:事件开始时间
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "START_TIME_UTC")]
		public DateTime? StartTimeUtc { get; set; }
		/// <summary>
		/// Desc:事件结束时间
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "END_TIME_UTC")]
		public DateTime? EndTimeUtc { get; set; }
		/// <summary>
		/// Desc:工单执行ID
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "PO_EXECUTION_ID")]
		public string PoExecutionId { get; set; }
		/// <summary>
		/// Desc:原因ID
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "REASON_ID")]
		public string ReasonId { get; set; }
		/// <summary>
		/// Desc:是否运行时
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "IS_RUNTIME")]
		public int IsRuntime { get; set; }
		/// <summary>
		/// Desc:是否停机
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "IS_BREAKDOWN")]
		public int IsBreakdown { get; set; }
		/// <summary>
		/// Desc:PLC代码
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "PLC_CODE")]
		public string PlcCode { get; set; }
		/// <summary>
		/// Desc:评论
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "COMMENT")]
		public string Comment { get; set; }
		/// <summary>
		/// Desc:工单ID
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "ORDER_ID")]
		public string OrderId { get; set; }
		/// <summary>
		/// Desc:是否瓶颈事件
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "IS_BOTTLENECK_EVENT")]
		public string IsBottleneckEvent { get; set; }
		/// <summary>
		/// Desc:班次ID
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "SHIFT_ID")]
		public string ShiftId { get; set; }
		/// <summary>
		/// Desc:人员数量
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "CREW_SIZE")]
		public string CrewSize { get; set; }
		/// <summary>
		/// Desc:来源类型
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "SOURCE_TYPE")]
		public string SourceType { get; set; }

		public object Clone()
		{
			return this.MemberwiseClone();
		}
	}
}