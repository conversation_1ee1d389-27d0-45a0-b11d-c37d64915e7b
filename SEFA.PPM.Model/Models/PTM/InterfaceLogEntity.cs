using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;

namespace SEFA.PPM.Model.Models.PTM
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("PTM_L_INTERFACE_LOG")]
    public class InterfaceLogEntity : EntityBase
    {
        public InterfaceLogEntity()
        {
        }
        /// <summary>
        /// Desc:工单执行ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "EXECUTION_ID")]
        public string ExecutionId { get; set; }
        /// <summary>
        /// Desc:工单号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "ORDER_NO")]
        public string OrderNo { get; set; }
        /// <summary>
        /// Desc:批号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "BATCH_NO")]
        public string BatchNo { get; set; }
        /// <summary>
        /// Desc:接口名
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "NAME")]
        public string Name { get; set; }
        /// <summary>
        /// Desc:接口描述
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "DESCRIPTION")]
        public string Description { get; set; }
        /// <summary>
        /// Desc:日志内容
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CONTENT")]
        public string Content { get; set; }
        /// <summary>
        /// Desc:明细
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "DETAIL")]
        public string Detail { get; set; }

    }
}