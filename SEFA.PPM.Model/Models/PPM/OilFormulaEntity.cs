using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("PPM_M_OIL_FORMULA")] 
    public class OilFormulaEntity : EntityBase
    {
        public OilFormulaEntity()
        {
        }
           /// <summary>
           /// Desc:配方物料编码
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="FORMULA_MATERIAL_CODE")]
        public string FormulaMaterialCode { get; set; }
           /// <summary>
           /// Desc:油物料编码
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="OIL_MATERIAL_CODE")]
        public string OilMaterialCode { get; set; }
           /// <summary>
           /// Desc:添加比例
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="RATE")]
        public decimal Rate { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="REMARK")]
        public string Remark { get; set; }

        /// <summary>
        /// Desc:配方物料名称
        /// Default:
        /// Nullable:False
        /// </summary>

        [SugarColumn(IsIgnore = true)]
        public string FormulaMaterialName { get; set; }
        /// <summary>
        /// Desc:油物料名称
        /// Default:
        /// Nullable:False
        /// </summary>

        [SugarColumn(IsIgnore = true)]
        public string OilMaterialName { get; set; }

        /// <summary>
        /// Desc:配方
        /// Default:
        /// Nullable:False
        /// </summary>

        [SugarColumn(IsIgnore = true)]
        public string SapFormula { get; set; }
        
    }
}