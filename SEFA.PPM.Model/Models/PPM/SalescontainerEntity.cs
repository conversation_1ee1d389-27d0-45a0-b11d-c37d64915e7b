using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.PPM.Model.Models.PPM;

namespace SEFA.PPM.Model.Models
{
	///<summary>
	///
	///</summary>

	[SugarTable("PPM_I_SalesContainer")]
	public class SalescontainerEntity : EntityBase
	{
		public SalescontainerEntity()
		{
		}
		/// <summary>
		/// Desc:销售容器
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "SALES_CONTAINER")]
		public string SalesContainer { get; set; }
		/// <summary>
		/// Desc:描述
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "DESCRIPTION")]
		public string Description { get; set; }
		/// <summary>
		/// Desc:删除标识
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "DELETED")]
		public int Deleted { get; set; }
        /// <summary>
        /// Desc:规格群组
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "ITEM_CODE")]
        public string ItemCode { get; set; }
    }

	public class SalescontainerCheckEntity
	{
		public string ID { get; set; }
		public string SalesContainer { get; set; }
		public string Description { get; set; }
		public bool IsChecked { get; set; }

	}
}