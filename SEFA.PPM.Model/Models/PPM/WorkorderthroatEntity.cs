using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("PPM_B_WORKORDERTHROAT")] 
    public class WorkorderthroatEntity : EntityBase
    {
        public WorkorderthroatEntity()
        {
        }
        /// <summary>
        /// Desc:出库仓位
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MAT_EQUIPMENTCODE")]
        public string MatEquipmentcode { get; set; }

        /// <summary>
        /// Desc:工单ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName="ORDER_ID")]
        public string OrderId { get; set; }
           /// <summary>
           /// Desc:工单代码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ORDER_CODE")]
        public string OrderCode { get; set; }
           /// <summary>
           /// Desc:喉头ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MAT_ID")]
        public string MatId { get; set; }
           /// <summary>
           /// Desc:喉头name
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MAT_NAME")]
        public string MatName { get; set; }
           /// <summary>
           /// Desc:喉头编码
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="THROAT_CODE")]
        public string ThroatCode { get; set; }
           /// <summary>
           /// Desc:喉头批号
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName= "THROATFROMORDER")]
        public string Throatformorder { get; set; }

        /// <summary>
        /// Desc:追溯码
        /// Default:
        /// Nullable:TRUE
        /// </summary>
        [SugarColumn(ColumnName = "SSCC")]
        public string SSCC { get; set; }
        /// <summary>
        /// Desc:喉头添加重量
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName="INWEIGHT")]
        public decimal Inweight { get; set; }
           /// <summary>
           /// Desc:喉头添加比例
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="RATE")]
        public decimal Rate { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MAT_STATUS")]
        public int MatStatus { get; set; }

  
        /// <summary>
        /// Desc:SAP配方
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string SapFormula { get; set; }
        /// <summary>
        /// Desc:标准最大比例
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName="STANDARD_RATE")]
        public decimal? StandardRate { get; set;}

        [SugarColumn(IsIgnore = true)]
        public DateTime PlanDate { get; set; }

        [SugarColumn(IsIgnore = true)]
        public string LineCode { get; set; }

        [SugarColumn(IsIgnore = true)]
        public decimal PlanQty { get; set; }

        [SugarColumn(IsIgnore = true)]
        public string PoSapFormula { get; set; }

        [SugarColumn(IsIgnore = true)]
        public string ProductionOrderNo { get; set; }
        
    }
}