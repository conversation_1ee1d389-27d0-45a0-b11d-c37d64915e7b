using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
	///<summary>
	///
	///</summary>

	[SugarTable("PPM_B_PO_PRODUCED_EXECUTION")]
	public class PoProducedExecutionEntity : EntityBase
	{
		public PoProducedExecutionEntity()
		{
		}
		/// <summary>
		/// Desc:资源ID
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "SAP_EQUIPMENT_ID")]
		public string SapEquipmentId { get; set; }
		/// <summary>
		/// Desc:产线ID
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "LINE_ID")]
		public string LineId { get; set; }
		/// <summary>
		/// Desc:订单ID
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "PRODUCTION_ORDER_ID")]
		public string ProductionOrderId { get; set; }
		/// <summary>
		/// Desc:订单工序需求ID
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "PO_SEGMENT_REQUIREMENT_ID")]
		public string PoSegmentRequirementId { get; set; }
		/// <summary>
		/// Desc:订单工序生产需求ID
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "PO_PRODUCED_REQUIREMENT_ID")]
		public string PoProducedRequirementId { get; set; }
		/// <summary>
		/// Desc:批次ID
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "BATCH_ID")]
		public string BatchId { get; set; }
		/// <summary>
		/// Desc:执行设备ID
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "RUN_EQUIPMENT_ID")]
		public string RunEquipmentId { get; set; }
		/// <summary>
		/// Desc:物料ID
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "MATERIAL_ID")]
		public string MaterialId { get; set; }
		/// <summary>
		/// Desc:物料版本ID
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "MATERIAL_VERSION_ID")]
		public string MaterialVersionId { get; set; }
		/// <summary>
		/// Desc:物料编码
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "MATERIAL_CODE")]
		public string MaterialCode { get; set; }
		/// <summary>
		/// Desc:物料描述
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "MATERIAL_DESCRIPTION")]
		public string MaterialDescription { get; set; }
		/// <summary>
		/// Desc:目标数量
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "TARGET_QUANTITY")]
		public decimal? TargetQuantity { get; set; }
		/// <summary>
		/// Desc:计量单位ID
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "UNIT_ID")]
		public string UnitId { get; set; }
		/// <summary>
		/// Desc:状态
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "STATUS")]
		public string Status { get; set; }
		/// <summary>
		/// Desc:删除标识
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "DELETED")]
		public int Deleted { get; set; }
		/// <summary>
		/// Desc:开始时间
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "START_TIME")]
		public DateTime? StartTime { get; set; }
		/// <summary>
		/// Desc:结束时间
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "END_TIME")]
		public DateTime? EndTime { get; set; }
		/// <summary>
		/// Desc:意见
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "COMMENTS")]
		public string Comments { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "LOT_ID")]
		public string LotId { get; set; }
		/// <summary>
		/// Desc:人员数量
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "CREW_SIZE")]
		public string CrewSize { get; set; }
	}

	public class GetBatchCodeModel
	{
		public string productionId { get; set; }

        public string LotCode { get; set; }

        public string equipmentCode { get; set; }

		public DateTime productionDate { get; set; }

		public string LineCode { get; set; }
	}

	public class PoProducedExecutionModel : PoProducedExecutionEntity
	{
		public string ProductionOrder { get; set; }

		public string Gc { get; set; }

		public string Formula { get; set; }

	}
}