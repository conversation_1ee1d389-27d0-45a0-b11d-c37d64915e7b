using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("PPM_B_PO_MATERIAL_REMARK")] 
    public class PoMaterialRemarkEntity : EntityBase
    {
        public PoMaterialRemarkEntity()
        {
        }
           /// <summary>
           /// Desc:工单ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PRODUCTTION_ORDER_ID")]
        public string ProducttionOrderId { get; set; }
           /// <summary>
           /// Desc:物料编码
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_CODE")]
        public string MaterialCode { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="REMARK")]
        public string Remark { get; set; }
        /// <summary>
        /// 物料名称
        /// </summary>

        [SugarColumn(IsIgnore = true)]
        public string MtrName { get; set; }

    }
}