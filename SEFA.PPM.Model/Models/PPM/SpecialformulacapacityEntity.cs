using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("PPM_M_SPECIALFORMULACAPACITY")] 
    public class SpecialformulacapacityEntity : EntityBase
    {
        public SpecialformulacapacityEntity()
        {
        }
           /// <summary>
           /// Desc:产线ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="LINE_ID")]
        public string LineId { get; set; }
           /// <summary>
           /// Desc:产线名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="LINE_NAME")]
        public string LineName { get; set; }
           /// <summary>
           /// Desc:配方ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MAT_ID")]
        public string MatId { get; set; }
           /// <summary>
           /// Desc:配方名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MAT_NAME")]
        public string MatName { get; set; }
           /// <summary>
           /// Desc:瓶型ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="CONTAINER_ID")]
        public string ContainerId { get; set; }
           /// <summary>
           /// Desc:瓶型名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="CONTAINER_NAME")]
        public string ContainerName { get; set; }
           /// <summary>
           /// Desc:煮制重量
           /// Default:
           /// Nullable:true
           /// </summary>
           [SugarColumn(ColumnName="WEIGHT")]
        public decimal? Weight { get; set; }
           /// <summary>
           /// Desc:目标产线
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TARGETLINE")]
        public string Targetline { get; set; }
           /// <summary>
           /// Desc:目标缸重
           /// Default:
           /// Nullable:true
           /// </summary>
           [SugarColumn(ColumnName="TARGETWEIGHT")]
        public decimal? Targetweight { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

        /// <summary>
        /// Desc:目标产线名称
        /// Default:
        /// Nullable:False
        /// </summary>

        [SugarColumn(IsIgnore = true)]
        public string TargetlineName { get; set; }

        /// <summary>
        /// Desc:配方编码
        /// Default:
        /// Nullable:False
        /// </summary>

        [SugarColumn(IsIgnore = true)]
        public string SapFormula { get; set; }

        /// <summary>
        /// Desc:容器代码
        /// Default:
        /// Nullable:False
        /// </summary>

        [SugarColumn(IsIgnore = true)]
        public string ContainerCode { get; set; }

        /// <summary>
        /// Desc:物料代码
        /// Default:
        /// Nullable:False
        /// </summary>

        [SugarColumn(IsIgnore = true)]
        public string MtrCode { get; set; }

    }
}