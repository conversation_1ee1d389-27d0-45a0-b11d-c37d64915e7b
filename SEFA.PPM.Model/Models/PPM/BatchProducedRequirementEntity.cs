using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("PPM_B_BATCH_PRODUCED_REQUIREMENT")] 
    public class BatchProducedRequirementEntity : EntityBase
    {
        public BatchProducedRequirementEntity()
        {
        }
           /// <summary>
           /// Desc:批次ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="BATCH_ID")]
        public string BatchId { get; set; }
           /// <summary>
           /// Desc:订单工序产出需求ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PO_PRODUCED_REQUIREMENT_ID")]
        public string PoProducedRequirementId { get; set; }
           /// <summary>
           /// Desc:数量
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="QUANTITY")]
        public decimal Quantity { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

    }
}