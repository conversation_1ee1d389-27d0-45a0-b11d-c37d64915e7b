using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("V_PPM_LOGSHEET_HISTORY_LIST_VIEW")] 
    public class LogsheetHistoryListViewRequireEntity : EntityBase
    {
        public LogsheetHistoryListViewRequireEntity()
        {
        }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PO_EXECUTION_ID")]
        public string PoExecutionId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PRODUCTION_ORDER_NO")]
        public string ProductionOrderNo { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="BATCH_ID")]
        public string BatchId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="BATCH_NUM")]
        public string BatchNum { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="STATUS")]
        public int Status { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="APPROVEUSERID")]
        public string Approveuserid { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="APPROVEDATE")]
        public DateTime? Approvedate { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="QA_ADUIT")]
        public string QaAduit { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="EQUIPMENT_ID")]
        public string EquipmentId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="EQUIPMENT_NAME")]
        public string EquipmentName { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PARAMETER_GROUP_ID")]
        public string ParameterGroupId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="GROUP_NAME")]
        public string GroupName { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NAME")]
        public string Name { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "COMMENT")]
        public string Comment { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "Formula")]
		public string Formula { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "TYPE")]
		public int? Type { get; set; }
		/// <summary>
		/// Desc:计划开始时间
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "PLAN_START_TIME")]
		public DateTime? PlanStartTime { get; set; }
		/// <summary>
		/// Desc:顺序号
		/// Default: 
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "Sequence")]
		public long Sequence { get; set; }
		/// <summary>
		/// Desc:QAStatus
		/// Default: 
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "QAStatus")]
		public string QAStatus { get; set; }
		/// <summary>
		/// Desc:QAStatus
		/// Default: 
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "FormulaNo")]
		public string FormulaNo { get; set; }
		/// <summary>
		/// Desc:
		/// Default: 
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "Count")]
		public int Count { get; set; }
		/// <summary>
		/// Desc:
		/// Default: 
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "SAPDATE")]
		public DateTime? SapDate { get; set; }
		/// <summary>
		/// Desc:顺序号
		/// Default: 
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "FormulaSequence")]
		public int FormulaSequence { get; set; }
		/// <summary>
		/// Desc:
		/// Default: 
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "TestingTime")]
		public DateTime? TestingTime { get; set; }
		/// <summary>
		/// Desc:
		/// Default: 
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "TestingUser")]
		public string TestingUser { get; set; }
		/// <summary>
		/// Desc:ProductionOrderId
		/// Default: 
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "PRODUCTION_ORDER_ID")]
		public string ProductionOrderId { get; set; }
	}
}