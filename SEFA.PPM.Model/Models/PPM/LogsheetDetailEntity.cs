using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("PPM_B_LOGSHEET_DETAIL")] 
    public class LogsheetDetailEntity : EntityBase
    {
        public LogsheetDetailEntity()
        {
        }
           /// <summary>
           /// Desc:日志表ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="LOGSHEET_ID")]
        public string LogsheetId { get; set; }
           /// <summary>
           /// Desc:模板ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="TEMPLATE_ID")]
        public string TemplateId { get; set; }
           /// <summary>
           /// Desc:值
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="VALUE")]
        public string Value { get; set; }
           /// <summary>
           /// Desc:评论
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="COMMENT")]
        public string Comment { get; set; }
           /// <summary>
           /// Desc:图像
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="IMAGE")]
        public string Image { get; set; }
           /// <summary>
           /// Desc:是否选填
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="IS_NULL")]
        public string IsNull { get; set; }
           /// <summary>
           /// Desc:是否可编辑
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="IS_EDITABLE")]
        public string IsEditable { get; set; }
           /// <summary>
           /// Desc:单位ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="UNIT_ID")]
        public string UnitId { get; set; }
        /// <summary>
        /// Desc:数据类型（2.Int、3.Decimal、1.Text）
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName="ENTRY_TYPE")]
        public string EntryType { get; set; }
        /// <summary>
        /// Desc:参数类型（1.input、2.list）
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PARAMTER_TYPE")]
        public string ParamterType { get; set; }
        /// <summary>
        /// Desc:外部链接
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName="EXTERNAL_URL")]
        public string ExternalUrl { get; set; }
           /// <summary>
           /// Desc:是否有效
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="IS_PASS")]
        public string IsPass { get; set; }
        /// <summary>
        /// Desc:参数名
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PARAMETER_NAME")]
        public string ParameterName { get; set; }
        /// <summary>
        /// Desc:最大值
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MAX_VALUE")]
        public decimal? MaxValue { get; set; }
        /// <summary>
        /// Desc:最小值
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MIN_VALUE")]
        public decimal? MinValue { get; set; }
    }
}