using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("PPM_B_BATCH")] 
    public class BatchEntity : EntityBase
    {
        public BatchEntity()
        {
        }
           /// <summary>
           /// Desc:产线ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="LINE_ID")]
        public string LineId { get; set; }
           /// <summary>
           /// Desc:订单ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PRODUCTION_ORDER_ID")]
        public string ProductionOrderId { get; set; }
           /// <summary>
           /// Desc:订单工序需求ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PO_SEGMENT_REQUIREMENT_ID")]
        public string PoSegmentRequirementId { get; set; }
           /// <summary>
           /// Desc:订单工序生产需求ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PO_PRODUCED_REQUIREMENT_ID")]
        public string PoProducedRequirementId { get; set; }
           /// <summary>
           /// Desc:批次序号
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NUMBER")]
        public string Number { get; set; }
           /// <summary>
           /// Desc:批次号
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="BATCH_CODE")]
        public string BatchCode { get; set; }
           /// <summary>
           /// Desc:执行设备ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RUN_EQUIPMENT_ID")]
        public string RunEquipmentId { get; set; }
           /// <summary>
           /// Desc:物料ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_ID")]
        public string MaterialId { get; set; }
           /// <summary>
           /// Desc:物料版本ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_VERSION_ID")]
        public string MaterialVersionId { get; set; }
           /// <summary>
           /// Desc:物料编码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_CODE")]
        public string MaterialCode { get; set; }
           /// <summary>
           /// Desc:物料描述
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_DESCRIPTION")]
        public string MaterialDescription { get; set; }
           /// <summary>
           /// Desc:目标数量
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="TARGET_QUANTITY")]
        public decimal? TargetQuantity { get; set; }
           /// <summary>
           /// Desc:计量单位ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="UNIT_ID")]
        public string UnitId { get; set; }
           /// <summary>
           /// Desc:生产状态
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="STATUS")]
        public string Status { get; set; }
           /// <summary>
           /// Desc:准备状态
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PREP_STATUS")]
        public string PrepStatus { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

    }
}