using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("PPM_M_CIP_SWITCHTYPE")] 
    public class CipSwitchtypeEntity : EntityBase
    {
        public CipSwitchtypeEntity()
        {
        }
        /// <summary>
        /// Desc:工厂
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "FACTORY")]
        public string Factory { get; set; }
        /// <summary>
        /// Desc:前配方ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName="PREMAT_ID")]
        public string PrematId { get; set; }
           /// <summary>
           /// Desc:前配方名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PREMAT_NAME")]
        public string PrematName { get; set; }
           /// <summary>
           /// Desc:后配方ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="POSTMAT_ID")]
        public string PostmatId { get; set; }
           /// <summary>
           /// Desc:后配方名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="POSTMAT_NAME")]
        public string PostmatName { get; set; }
           /// <summary>
           /// Desc:切换方式ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="SWITCHID")]
        public string Switchid { get; set; }
           /// <summary>
           /// Desc:切换方式名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="SWITCHNAME")]
        public string Switchname { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }
        /// <summary>
        /// Desc:前配方编码
        /// Default:
        /// Nullable:False
        /// </summary>

        [SugarColumn(IsIgnore = true)]
        public string PrematCode { get; set; }
        /// <summary>
        /// Desc:后配方编码
        /// Default:
        /// Nullable:False
        /// </summary>

        [SugarColumn(IsIgnore = true)]
        public string PostmatCode { get; set; }

        /// <summary>
        /// Desc:前配方编码
        /// Default:
        /// Nullable:False
        /// </summary>

        [SugarColumn(IsIgnore = true)]
        public string PreMtrCode { get; set; }
        /// <summary>
        /// Desc:后配方编码
        /// Default:
        /// Nullable:False
        /// </summary>

        [SugarColumn(IsIgnore = true)]
        public string PostMtrCode { get; set; }

    }
}