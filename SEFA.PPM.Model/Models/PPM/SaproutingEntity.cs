using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("PPM_I_SapRouting")] 
    public class SaproutingEntity : EntityBase
    {
        public SaproutingEntity()
        {
        }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MANDT")]
        public string Mandt { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="CRNAM")]
        public string Crnam { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="SERNR")]
        public string Sernr { get; set; }
           /// <summary>
           /// Desc:物料编码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MATNR")]
        public string Matnr { get; set; }
           /// <summary>
           /// Desc:物料描述
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MAKTX")]
        public string Maktx { get; set; }
           /// <summary>
           /// Desc:配方
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NORMT")]
        public string Normt { get; set; }
           /// <summary>
           /// Desc:规格
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MAGRV")]
        public string Magrv { get; set; }
           /// <summary>
           /// Desc:工单号
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="AUFNR")]
        public string Aufnr { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OBJNR")]
        public string Objnr { get; set; }
           /// <summary>
           /// Desc:工厂
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DWERK")]
        public string Dwerk { get; set; }
           /// <summary>
           /// Desc:工单工作中心
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ARBPL")]
        public string Arbpl { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RARBPL")]
        public string Rarbpl { get; set; }
           /// <summary>
           /// Desc:成本中心
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="KOSTL")]
        public string Kostl { get; set; }
           /// <summary>
           /// Desc:标准文本码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="KTSCH")]
        public string Ktsch { get; set; }
           /// <summary>
           /// Desc:标准文本码描述
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="KTSTX")]
        public string Ktstx { get; set; }
           /// <summary>
           /// Desc:MRP controller
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DISPO")]
        public string Dispo { get; set; }
           /// <summary>
           /// Desc:开始日期
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="GSTRP")]
        public string Gstrp { get; set; }
           /// <summary>
           /// Desc:基本单位
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MEINS")]
        public string Meins { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="AMEIN")]
        public string Amein { get; set; }
           /// <summary>
           /// Desc:计划数量
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PSMNG")]
        public decimal Psmng { get; set; }
           /// <summary>
           /// Desc:收货数量
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="WEMPF")]
        public string Wempf { get; set; }
           /// <summary>
           /// Desc:确认数量
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="LMNGA")]
        public decimal Lmnga { get; set; }
           /// <summary>
           /// Desc:工序
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="VORNR")]
        public string Vornr { get; set; }
           /// <summary>
           /// Desc:标准控制码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="STEUS")]
        public string Steus { get; set; }
           /// <summary>
           /// Desc:计算成本标识
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="SELKZ")]
        public string Selkz { get; set; }
           /// <summary>
           /// Desc:作业类型
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="LAR01")]
        public string Lar01 { get; set; }
           /// <summary>
           /// Desc:作业类型单位
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="VGE01")]
        public string Vge01 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TTM01")]
        public decimal Ttm01 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ILE01")]
        public string Ile01 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="ISM01")]
        public decimal Ism01 { get; set; }
           /// <summary>
           /// Desc:标准值
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="VGW01")]
        public decimal Vgw01 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="RAT01")]
        public decimal Rat01 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RAT01_T")]
        public string Rat01_T { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="LAR02")]
        public string Lar02 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="VGE02")]
        public string Vge02 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TTM02")]
        public decimal Ttm02 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ILE02")]
        public string Ile02 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="ISM02")]
        public decimal Ism02 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="VGW02")]
        public decimal Vgw02 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="RAT02")]
        public decimal Rat02 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RAT02_T")]
        public string Rat02_T { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="LAR03")]
        public string Lar03 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="VGE03")]
        public string Vge03 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TTM03")]
        public decimal Ttm03 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ILE03")]
        public string Ile03 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="ISM03")]
        public decimal Ism03 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="RAT03")]
        public decimal Rat03 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RAT03_T")]
        public string Rat03_T { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="LAR04")]
        public string Lar04 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="VGE04")]
        public string Vge04 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TTM04")]
        public decimal Ttm04 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ILE04")]
        public string Ile04 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="ISM04")]
        public decimal Ism04 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="RAT04")]
        public decimal Rat04 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RAT04_T")]
        public string Rat04_T { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="LAR05")]
        public string Lar05 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="VGE05")]
        public string Vge05 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TTM05")]
        public decimal Ttm05 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ILE05")]
        public string Ile05 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="ISM05")]
        public decimal Ism05 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="RAT05")]
        public decimal Rat05 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RAT05_T")]
        public string Rat05_T { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="LAR06")]
        public string Lar06 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="VGE06")]
        public string Vge06 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TTM06")]
        public decimal Ttm06 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ILE06")]
        public string Ile06 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="ISM06")]
        public decimal Ism06 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="RAT06")]
        public decimal Rat06 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RAT06_T")]
        public string Rat06_T { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ACTTF")]
        public string Acttf { get; set; }
           /// <summary>
           /// Desc:基本单位
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="BMSCH")]
        public decimal Bmsch { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="GAMNG")]
        public decimal Gamng { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="IGMNG")]
        public decimal Igmng { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="GMEIN")]
        public string Gmein { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="BDMNG")]
        public decimal Bdmng { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="KZEAR")]
        public string Kzear { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="KZKUP")]
        public string Kzkup { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="LGORT")]
        public string Lgort { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="XLOEK")]
        public string Xloek { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DLT")]
        public string Dlt { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="ENMNG")]
        public decimal Enmng { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RMEIN")]
        public string Rmein { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DMENG")]
        public decimal Dmeng { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DRATE")]
        public decimal Drate { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DRATE_T")]
        public string Drate_T { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="GIQTY")]
        public decimal Giqty { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="AMENG")]
        public decimal Ameng { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="BTMIX")]
        public string Btmix { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="AMENG01")]
        public decimal Ameng01 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="WRATE")]
        public decimal Wrate { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="WRATE_T")]
        public string Wrate_T { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="RRATE")]
        public decimal Rrate { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RRATE_T")]
        public string Rrate_T { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="SRATE")]
        public decimal Srate { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="SRATE_T")]
        public string Srate_T { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="RRATE01")]
        public decimal Rrate01 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RRATE_T01")]
        public string Rrate_T01 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="SRATE01")]
        public decimal Srate01 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="SRATE_T01")]
        public string Srate_T01 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="IDNRK")]
        public string Idnrk { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="COMTZ")]
        public string Comtz { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="WEGSA")]
        public decimal Wegsa { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MNGFS")]
        public decimal Mngfs { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="TGUOM")]
        public string Tguom { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MATGI")]
        public string Matgi { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="TZMGI")]
        public string Tzmgi { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="WEGGI")]
        public decimal Weggi { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MNGGI")]
        public decimal Mnggi { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="SFUOM")]
        public string Sfuom { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="RATE")]
        public decimal Rate { get; set; }
           /// <summary>
           /// Desc:工单状态 可以排除TECO或DLEF
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="STATU")]
        public string Statu { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="LN_COLOR")]
        public string Ln_Color { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="CRDAT")]
        public string Crdat { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="CRTIM")]
        public DateTime Crtim { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="FEVOR")]
        public string Fevor { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="TXT04")]
        public string Txt04 { get; set; }
           /// <summary>
           /// Desc:工序描述
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="TXT")]
        public string Txt { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="S_BOM")]
        public decimal S_Bom { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="E_BOM")]
        public decimal E_Bom { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PERIO")]
        public string Perio { get; set; }

    }
}