using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("PPM_M_THROATADDITION")] 
    public class ThroatadditionEntity : EntityBase
    {
        public ThroatadditionEntity()
        {
        }
           /// <summary>
           /// Desc:喉头配方ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MAT_ID")]
        public string MatId { get; set; }
           /// <summary>
           /// Desc:喉头配方名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MAT_NAME")]
        public string MatName { get; set; }
           /// <summary>
           /// Desc:可添加配方ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MAT_ADDABLE_ID")]
        public string MatAddableId { get; set; }
           /// <summary>
           /// Desc:可添加配方名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MAT_ADDABLE_NAME")]
        public string MatAddableName { get; set; }
           /// <summary>
           /// Desc:添加比例
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="RATE")]
        public decimal Rate { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }


        /// <summary>
        /// Desc:喉头配方物料编码
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string MaterialCode { get; set; }

        /// <summary>
        /// Desc:喉头配方代码
        /// Default:
        /// Nullable:False
        /// </summary>

        [SugarColumn(IsIgnore = true)]
        public string FormulaCode { get; set; }
        /// <summary>
        /// Desc:可添加配方代码
        /// Default:
        /// Nullable:False
        /// </summary>

        [SugarColumn(IsIgnore = true)]
        public string AddableFormulaCode { get; set; }

        /// <summary>
        /// Desc:可添加配方代码
        /// Default:
        /// Nullable:False
        /// </summary>


        [SugarColumn(IsIgnore = true)]
        public string AddableMaterialCode { get; set; }

    }
}