using System.Collections.Generic;

namespace SEFA.PPM.Model.Models.Interface.WMS;

public class InventoryQueryRequest
{
    
    /// <summary>
    /// 库存查询类型：正常、IBC配置成品
    /// </summary>
    public string Type { get; set; }

    /// <summary>
    /// 仓库编号
    /// </summary>
    public string WarehouseCode { get; set; }

    /// <summary>
    /// 工厂
    /// </summary>
    public string Plant { get; set; }

    /// <summary>
    /// 物料编码
    /// </summary>
    public string MaterialCode { get; set; }

    /// <summary>
    /// 物料版本号
    /// </summary>
    public string MaterialVersionCode { get; set; }

    /// <summary>
    /// 母托盘号
    /// </summary>
    public string PalletNo { get; set; }

    /// <summary>
    /// 批次号
    /// </summary>
    public string BatchNo { get; set; }

    /// <summary>
    /// 物料唯一标签码
    /// </summary>
    public string BarCode { get; set; }

    /// <summary>
    /// 质检状态列表：待检、合格、不合格
    /// </summary>
    public List<string> QualityStateList { get; set; }

    /// <summary>
    /// 使用状态列表：报废、禁止使用、正常放行、让步放行、待退
    /// </summary>
    public List<string> UseState { get; set; }

}