using System;
using System.ComponentModel.DataAnnotations;
using SEFA.Base.Model.BASE;
using SqlSugar;

namespace SEFA.PPM.Model.Models.Interface.WMS
{
    /// <summary>
    /// WMS接口调用日志实体
    /// </summary>
    [SugarTable("MKM_B_WMS_INTERFACE_LOG")]
    public class WmsInterfaceLogEntity : EntityBase
    {
        /// <summary>
        /// 主键标识
        /// </summary>
        [SugarColumn(ColumnName = "ID", IsPrimaryKey = true)]
        [MaxLength(50)]
        public string ID { get; set; }

        /// <summary>
        /// 接口名称
        /// </summary>
        [SugarColumn(ColumnName = "INTERFACENAME")]
        [Required]
        [MaxLength(100)]
        public string InterfaceName { get; set; }

        /// <summary>
        /// 请求参数
        /// </summary>
        [SugarColumn(ColumnName = "REQUESTDATA", ColumnDataType = "NTEXT")]
        public string RequestData { get; set; }

        /// <summary>
        /// 响应结果
        /// </summary>
        [SugarColumn(ColumnName = "RESPONSEDATA", ColumnDataType = "NTEXT")]
        public string ResponseData { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        [SugarColumn(ColumnName = "ISSUCCESS")]
        [Required]
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        [SugarColumn(ColumnName = "ERRORMESSAGE")]
        [MaxLength(500)]
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 调用方向（0:我们调用WMS，1:WMS调用我们）
        /// </summary>
        [SugarColumn(ColumnName = "DIRECTION")]
        [Required]
        public int Direction { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnName = "CREATEDATE")]
        [Required]
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        [SugarColumn(ColumnName = "CREATEUSERID")]
        [Required]
        [MaxLength(50)]
        public string CreateUserId { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        [SugarColumn(ColumnName = "MODIFYDATE")]
        public DateTime? ModifyDate { get; set; }

        /// <summary>
        /// 修改人ID
        /// </summary>
        [SugarColumn(ColumnName = "MODIFYUSERID")]
        [MaxLength(50)]
        public string ModifyUserId { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        [SugarColumn(ColumnName = "UPDATETIMESTAMP")]
        public byte[] UpdateTimestamp { get; set; }

        /// <summary>
        /// 逻辑删除标记，0：未删除，1：已删除
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; } = 0;
    }
}