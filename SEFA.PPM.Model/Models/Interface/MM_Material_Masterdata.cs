using System;
using System.Collections.Generic;

namespace SEFA.PPM.Model.Models.Interface
{
	public class MM_Material_Masterdata
	{
		/// <summary>
		/// 酱料类别
		/// </summary>
		public string matca { get; set; }
		/// <summary>
		/// 酱料系列
		/// </summary>
		public string matse { get; set; }
		/// <summary>
		/// 物料编码
		/// </summary>
		public string matnr { get; set; }
		/// <summary>
		/// 工厂
		/// </summary>
		public string werks { get; set; }
		/// <summary>
		/// 工厂级 DF
		/// </summary>
		public string lvorm { get; set; }
		/// <summary>
		/// 批次
		/// </summary>
		public string xcha { get; set; }
		/// <summary>
		/// P-S 物料状态 
		/// </summary>
		public string mmsta { get; set; }
		/// <summary>
		/// 有效起始期 
		/// </summary>
		public DateTime? mmstd { get; set; }
		/// <summary>
		/// 采购组 
		/// </summary>
		public string ekgrp { get; set; }
		/// <summary>
		/// 发货单位
		/// </summary>
		public string ausme { get; set; }
		/// <summary>
		/// MRP 类型
		/// </summary>
		public string dismm { get; set; }
		/// <summary>
		/// MRP 控制者 
		/// </summary>
		public string dispo { get; set; }
		/// <summary>
		/// 计划交货时间
		/// </summary>
		public decimal? plifz { get; set; }
		/// <summary>
		/// 收货处理时间
		/// </summary>
		public decimal? webaz { get; set; }
		/// <summary>
		/// 批量大小
		/// </summary>
		public string disls { get; set; }
		/// <summary>
		/// 采购
		/// </summary>
		public string beskz { get; set; }
		/// <summary>
		/// 特殊采购
		/// </summary>
		public string sobsl { get; set; }
		/// <summary>
		/// 安全库存
		/// </summary>
		public decimal? eisbe { get; set; }
		/// <summary>
		/// 最小批量大小 
		/// </summary>
		public decimal? bstmi { get; set; }
		/// <summary>
		/// 最大批量 
		/// </summary>
		public decimal? bstma { get; set; }
		/// <summary>
		/// 固定批量大小
		/// </summary>
		public decimal? bstfe { get; set; }
		/// <summary>
		/// 舍入值 
		/// </summary>
		public decimal? bstrf { get; set; }
		/// <summary>
		/// 独立/集中
		/// </summary>
		public string sbdkz { get; set; }
		/// <summary>
		/// 选择方法
		/// </summary>
		public string altsl { get; set; }
		/// <summary>
		/// 非连续标识 
		/// </summary>
		public string kzaus { get; set; }
		/// <summary>
		/// 中断日期
		/// </summary>
		public DateTime? ausdt { get; set; }
		/// <summary>
		/// 后继物料
		/// </summary>
		public string nfmat { get; set; }
		/// <summary>
		/// 反冲
		/// </summary>
		public string rgekz { get; set; }
		/// <summary>
		/// 生产管理员 
		/// </summary>
		public string fevor { get; set; }
		/// <summary>
		/// 过帐到检验库存
		/// </summary>
		public string insmk { get; set; }
		/// <summary>
		/// QM 控制码 
		/// </summary>
		public string ssqss { get; set; }
		/// <summary>
		/// 生产仓储地点
		/// </summary>
		public string lgpro { get; set; }
		/// <summary>
		/// MRP组 
		/// </summary>
		public string disgr { get; set; }
		/// <summary>
		/// 组件报废
		/// </summary>
		public decimal? kausf { get; set; }
		/// <summary>
		/// 外部采购仓储地
		/// </summary>
		public string lgfsb { get; set; }
		/// <summary>
		/// QM 物料授权
		/// </summary>
		public string qmata { get; set; }
		/// <summary>
		/// 安全时间标识
		/// </summary>
		public string shflg { get; set; }
		/// <summary>
		/// 安全时间
		/// </summary>
		public decimal? shzet { get; set; }
		/// <summary>
		/// 超量交货容差限制
		/// </summary>
		public decimal? ueeto { get; set; }
		/// <summary>
		/// Material Type
		/// </summary>
		public string mtart { get; set; }
		/// <summary>
		/// Mkt owner
		/// </summary>
		public string ferth { get; set; }
		/// <summary>
		/// FactoryOrigin
		/// </summary>
		public string extwg { get; set; }
		/// <summary>
		/// Formula Code
		/// </summary>
		public string normt { get; set; }
		/// <summary>
		/// Material Group
		/// </summary>
		public string matkl { get; set; }
		/// <summary>
		/// General Item Category Group
		/// </summary>
		public string mtpos { get; set; }
		/// <summary>
		/// Packing Unit
		/// </summary>
		public string vhart { get; set; }
		/// <summary>
		/// Sales Container
		/// </summary>
		public string magrv { get; set; }
		/// <summary>
		/// Base Unit
		/// </summary>
		public string meins { get; set; }
		/// <summary>
		/// Shelf life (Months)
		/// </summary>
		public string mhdhb { get; set; }
		/// <summary>
		/// Net Weight
		/// </summary>
		public decimal? ntgew { get; set; }
		/// <summary>
		/// Net Weight unit
		/// </summary>
		public string gewei { get; set; }
		/// <summary>
		/// Size/dimensions
		/// </summary>
		public string groes { get; set; }
		/// <summary>
		/// Dim. Code
		/// </summary>
		public string aeszn { get; set; }
		/// <summary>
		/// X-plant matl status
		/// </summary>
		public string mstae { get; set; }
		/// <summary>
		/// X-distr.chain status
		/// </summary>
		public string mstav { get; set; }
		/// <summary>
		/// market segment
		/// </summary>
		public string labor { get; set; }
		/// <summary>
		/// Gross Weight
		/// </summary>
		public decimal? brgew { get; set; }
		/// <summary>
		/// Old material number
		/// </summary>
		public string bismt { get; set; }
		/// <summary>
		/// EAN_UPC
		/// </summary>
		public string ean11 { get; set; }
		/// <summary>
		/// EANCategory
		/// </summary>
		public string numtp { get; set; }
		/// <summary>
		/// Length
		/// </summary>
		public decimal? laeng { get; set; }
		/// <summary>
		/// Width
		/// </summary>
		public decimal? breit { get; set; }
		/// <summary>
		/// Height
		/// </summary>
		public decimal? hoehe { get; set; }
		/// <summary>
		/// PrintedLang
		/// </summary>
		public string blatt { get; set; }
		/// <summary>
		/// SAP Product Description (Eng)
		/// </summary>
		public string maktx_eng { get; set; }
		/// <summary>
		/// SAP Product Description (Chi)
		/// </summary>
		public string maktx_chi { get; set; }
		/// <summary>
		/// 维修备件Storage Bin
		/// </summary>
		public string lgpbe { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string createdate { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string iprkz { get; set; }
		public string KSCHL_1_M{ get; set; }
		public string KSCHL_2_M { get; set; }
		public string KSCHL_1_E { get; set; }
		public string KSCHL_2_E { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string matkl_en { get; set; }
		/// <summary>
		/// 正常包裝
		/// </summary>
		public string matkl_zh { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string uniquecode { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int id { get; set; }
		/// <summary>
		/// MSG取数标记 空：MES未读取 Y：MES已读取
		/// </summary>
		public string flag_mes { get; set; }
	}
}