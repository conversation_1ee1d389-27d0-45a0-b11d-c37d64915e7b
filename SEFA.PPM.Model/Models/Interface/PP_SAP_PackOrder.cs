using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.Models.Interface
{
    public class PP_SAP_PackOrder
    {
        /// <summary>
        /// 
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 工单号 KEY
        /// </summary>
        public string AUFNR { get; set; }
        /// <summary>
        /// 工单类型/灌注工单
        /// </summary>
        public string PDTYPE { get; set; }
        /// <summary>
        /// 工厂
        /// </summary>
        public string WERKS { get; set; }
        /// <summary>
        /// 订单类型
        /// </summary>
        public string AUART { get; set; }
        /// <summary>
        /// 物料编号
        /// </summary>
        public string MATNR { get; set; }
        /// <summary>
        ///  物料描述  味蠔鮮蠔油 7KGX2_工業定制
        /// </summary>
        public string MAKTX { get; set; }
        /// <summary>
        /// 计划生产量
        /// </summary>
        public decimal PSMNG { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        public string AMEIN { get; set; }
        /// <summary>
        /// 工作中心
        /// </summary>
        public string ARBPL { get; set; }
        /// <summary>
        /// 生产线
        /// </summary>
        public string VERAN { get; set; }
        /// <summary>
        /// 工单开始生产日期
        /// </summary>
        public DateTime? GSTRP { get; set; }
        /// <summary>
        /// 工单完成生产日期
        /// </summary>
        public DateTime? GLTRP { get; set; }
        /// <summary>
        /// 对应灌装工单-物料编码
        /// </summary>
        public string MATNR2 { get; set; }
        /// <summary>
        /// 对应灌装工单-物料描述
        /// </summary>
        public string MAKTX2 { get; set; }
        /// <summary>
        /// 对应灌装工单-工单号
        /// </summary>
        public string AUART_FILL { get; set; }
        /// <summary>
        /// 煮料组件编码
        /// </summary>
        public string MATNR_COMP { get; set; }
        /// <summary>
        /// 煮料组件描述
        /// </summary>
        public string MAKTX_COMP { get; set; }
        /// <summary>
        /// 煮料组件用量
        /// </summary>
        public decimal PSMNG_COMP { get; set; }
        /// <summary>
        /// 煮料组件单位
        /// </summary>
        public string AMEIN_COMP { get; set; }
        /// <summary>
        /// 销售订单
        /// </summary>
        public string KDAUF { get; set; }
        /// <summary>
        /// 销售订单项目
        /// </summary>
        public string KDPOS { get; set; }
        /// <summary>
        /// 收货仓库
        /// </summary>
        public string LGORT { get; set; }
        /// <summary>
        /// 售达方
        /// </summary>
        public string KUNNR3 { get; set; }
        /// <summary>
        /// 售达方描述
        /// </summary>
        public string KUNNR1 { get; set; }
        /// <summary>
        /// 送达方
        /// </summary>
        public string KUNNR4 { get; set; }
        /// <summary>
        /// 送达方描述
        /// </summary>
        public string KUNNR2 { get; set; }
        /// <summary>
        /// 每层数量
        /// </summary>
        public int PLPER { get; set; }
        /// <summary>
        /// 每板层数
        /// </summary>
        public int PLODD { get; set; }
        /// <summary>
        /// 每板数量
        /// </summary>
        public int PLQTY { get; set; }
        /// <summary>
        /// 采购订单
        /// </summary>
        public string EBELN { get; set; }
        /// <summary>
        /// 采购订单项目
        /// </summary>
        public string EBELP { get; set; }
        /// <summary>
        /// MRP Ctrlr
        /// </summary>
        public string DISPO { get; set; }
        /// <summary>
        /// 工单状态
        /// </summary>
        public string Status { get; set; }
        /// <summary>
        /// 订购工厂
        /// </summary>
        public string WERKS2 { get; set; }
        /// <summary>
        /// 每箱PU单支数
        /// </summary>
        public decimal MNG_PU { get; set; }
        /// <summary>
        /// 工单PU单支数
        /// </summary>
        public decimal MNG_PUO { get; set; }
        /// <summary>
        /// 配方
        /// </summary>
        public string NORMT { get; set; }
        /// <summary>
        /// 净重
        /// </summary>
        public decimal NTGEW { get; set; }
        /// <summary>
        /// BOM 长报文
        /// </summary>
        public string LTEXT1 { get; set; }
        /// <summary>
        /// 工单长报文
        /// </summary>
        public string LTEXT2 { get; set; }
        /// <summary>
        /// 物料长报文
        /// </summary>
        public string LTEXT3 { get; set; }
        /// <summary>
        /// PO Header 长文本
        /// </summary>
        public string LTEXT4 { get; set; }
        /// <summary>
        /// Routing Long Text
        /// </summary>
        public string LTEXT5 { get; set; }
        /// <summary>
        /// Prodtn Memo Long Text
        /// </summary>
        public string LTEXT6 { get; set; }
        /// <summary>
        /// GR收货数量
        /// </summary>
        public decimal WEMNG { get; set; }
        /// <summary>
        /// 销售容器
        /// </summary>
        public string MAGRV { get; set; }
        /// <summary>
        /// 销售容器描述
        /// </summary>
        public string BEZEI { get; set; }
        /// <summary>
        /// 包装规格
        /// </summary>
        public string VHART { get; set; }
        /// <summary>
        /// 包装规格描述
        /// </summary>
        public string VTEXT { get; set; }
        /// <summary>
        /// 类别
        /// </summary>
        public string CATEGORY { get; set; }
        /// <summary>
        /// 批次
        /// </summary>
        public string BATCH_FW { get; set; }
        /// <summary>
        /// 保质期到期日
        /// </summary>
        public string SHELF_FW { get; set; }
        /// <summary>
        /// 生产线代码
        /// </summary>
        public string VERAN_FW { get; set; }
        /// <summary>
        /// 纸箱及招纸
        /// </summary>
        public string MAKTX_C_FW { get; set; }
        /// <summary>
        /// 包装比例
        /// </summary>
        public string LHMG1_FW { get; set; }
        /// <summary>
        /// 收货数量及批次
        /// </summary>
        public string MENGE_C_FW { get; set; }
        /// <summary>
        /// 工单产品物料组
        /// </summary>
        public string MATKL { get; set; }
        /// <summary>
        /// 产品保质期
        /// </summary>
        public decimal MHDHB { get; set; }
        /// <summary>
        /// 保质期单位
        /// </summary>
        public string IPRKZ { get; set; }
        /// <summary>
        /// 市场所有者        /// </summary>
        public string FERTH { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Flag_MES { get; set; }
        /// <summary>
        /// Sales Mkt 销售市场
        /// </summary>
        public string SLMKT { get; set; }
        /// <summary>
        /// Country/Region 国家/地区(EN)
        /// </summary>
        public string LANDX { get; set; }
        /// <summary>
        /// Country/Region 国家/地区(ZF)
        /// </summary>
        public string LANDZ { get; set; }
        /// <summary>
        /// Regulatory Market
        /// </summary>
        public string RGMKT { get; set; }
        /// <summary>
        /// Zone
        /// </summary>
        public string VKBUR { get; set; }
        /// <summary>
        /// Zone Desc
        /// </summary>
        public string ZNDSC { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ORDERJARCLEAR { get; set; }
        /// <summary>
        /// Cylinder Clearing Cd
        /// </summary>
        public string ZCYLCD { get; set; }
        /// <summary>
        /// Cylinder Clearing Description
        /// </summary>
        public string CODTX { get; set; }
        /// <summary>
        /// Sort Sequence
        /// </summary>
        public int SORTF { get; set; }
        /// <summary>
        /// 报价日期  
        /// </summary>
        public string IHRAN { get; set; }
        /// <summary>
        /// UPC/EAU 
        /// </summary>
        public string EAN11 { get; set; }
        /// <summary>
        /// 优先排产要求
        /// </summary>
        public string FGPRI  { get; set; }

        /// <summary>
        /// 走柜期
        /// </summary>
        public DateTime? ETDAT { get; set; }
        /// <summary>
        /// Cust PO num
        /// </summary>
        public string BSTNK { get; set; }
        /// <summary>
        /// Sales org  
        /// </summary>
        public string VKORG { get; set; }
    }
}
