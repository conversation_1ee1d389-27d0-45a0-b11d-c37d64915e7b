using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Nodes;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.Models.Interface
{
	/// <summary>
	/// QA状态同步_MES
	/// </summary>
	public class MMI_QAStatusSync
	{
		/// <summary>
		///执行设备ID
		/// </summary>
		public string EquipmentId { get; set; }
		/// <summary>
		///QA状态 1：QA处理中；2：QA放行：3：QA结果异常，物料锁定
		/// </summary>
		public int QAStatus { get; set; }

		/// <summary>
		///工单号
		/// </summary>
		public string ProductionNo { get; set; }

		/// <summary>
		///产品ID
		/// </summary>
		public string ProductId { get; set; }

		/// <summary>
		///产品名称
		/// </summary>
		public string ProductName { get; set; }

		/// <summary>
		///产品版本信息
		/// </summary>
		public string ProductVersion { get; set; }

		/// <summary>
		///第x批
		/// </summary>
		public string BatchIndex { get; set; }

	}
}
