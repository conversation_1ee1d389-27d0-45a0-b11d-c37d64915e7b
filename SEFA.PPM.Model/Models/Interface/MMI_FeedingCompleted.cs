using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Nodes;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.Models.Interface
{
	/// <summary>
	/// 投料完成_MES
	/// </summary>
	public class MMI_FeedingCompleted
	{
		/// <summary>
		///执行设备ID
		/// </summary>
		public string EquipmentId { get; set; }

		/// <summary>
		///投料状态 1：投料中；2：投料结束
		/// </summary>
		public int TippingStatus { get; set; }

		/// <summary>
		///工单号
		/// </summary>
		public string ProductionNo { get; set; }

		/// <summary>
		///产品ID
		/// </summary>
		public string ProductId { get; set; }

		/// <summary>
		///产品名称
		/// </summary>
		public string ProductName { get; set; }

		/// <summary>
		///产品版本信息
		/// </summary>
		public string ProductVersion { get; set; }

		/// <summary>
		///第x批
		/// </summary>
		public string BatchIndex { get; set; }

	}
}
