using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.Models.Interface
{
    public class PP_SAP_CKORD_AsynRequest
    {
        /// <summary>
        /// MES创建工单时的计划号
        /// </summary>
        public string MESNum { get; set; }
        /// <summary>
        /// 工单类型 P-煮制计划工单， A-煮制工单
        /// </summary>
        public string ORDTYP { get; set; }
        /// <summary>
        /// 数据清单
        /// </summary>
        public List<CKORDAsynReqItem> CKORD { get; set; }
    }
    /// <summary>
    /// 工单清单
    /// </summary>
    public class CKORDAsynReqItem
    {
        /// <summary>
        /// MES工单号
        /// </summary>
        public string ZAFNR_MES_CK { get; set; }
        /// <summary>
        /// SAP工单号
        /// </summary>
        public string AUFNR { get; set; }
        /// <summary>
        /// 状态字段 
        /// </summary>
        public string STTXT { get; set; }
        /// <summary>
        /// 工艺长文本 用MESNum 作为KEY解密
        /// </summary>
        public string BOMLTX { get; set; }
        /// <summary>
        /// 工艺长文本 用MESNum 作为KEY 解密
        /// </summary>
        public string BOMALTX { get; set; }
        /// <summary>
        /// 类型
        /// </summary>
        public string TYPE { get; set; }
        /// <summary>
        /// 返回消息
        /// </summary>
        public string RETMSG { get; set; }
    }

    /// <summary>
    /// 请求参数
    /// </summary>
    public class SapRequestModel
    {
        /// <summary>
        /// 工厂代码
        /// </summary>
        public string factory { get; set; }
        /// <summary>
        /// 工单类型
        /// </summary>
        public string type { get; set; }
        /// <summary>
        /// 工单号
        /// </summary>
        public List<string> orderNo { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime start { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime end { get; set; }
    }
}
