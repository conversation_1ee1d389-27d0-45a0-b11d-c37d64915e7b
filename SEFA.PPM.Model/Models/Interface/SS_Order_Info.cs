using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using System;
using System.Collections.Generic;

namespace SEFA.PPM.Model.Models.Interface
{
	public class SS_Order_Info
	{
		/// <summary>
		/// 请求唯一ID
		/// </summary>
		public string MesKey { get; set; }
		/// <summary>
		/// 工单生产日期
		/// </summary>
		public string OrderDate { get; set; }
		/// <summary>
		/// 工单号
		/// </summary>
		public string OrderCode { get; set; }
		/// <summary>
		/// 工作中心
		/// </summary>
		public string WorkCenter { get; set; }
		/// <summary>
		/// 物料编码
		/// </summary>
		public string MaterialCode { get; set; }
		/// <summary>
		/// 瓶规格
		/// </summary>
		public string SalesContainer { get; set; }
		/// <summary>
		/// 需求瓶数量
		/// </summary>
		public int Quantity { get; set; }
		/// <summary>
		/// 单位
		/// </summary>
		public string Unit { get; set; }
		/// <summary>
		/// 单托数量
		/// </summary>
		public int PackQty { get; set; }
		/// <summary>
		/// 需要指定的物料批次
		/// </summary>
		public string LotCode { get; set; }
		/// <summary>
		/// 业务类型
		/// 0（工单释放）
		/// 3（资料更新）
		/// </summary>
		public int ActionType { get; set; }
	}
}