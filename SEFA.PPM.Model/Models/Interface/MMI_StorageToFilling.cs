using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Nodes;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.Models.Interface
{
	/// <summary>
	/// 储存缸到灌装机出料_Proleit
	/// </summary>
	public class MMI_StorageToFilling
	{
		/// <summary>
		///执行设备ID
		/// </summary>
		public string EquipmentId { get; set; }
		/// <summary>
		///目标设备ID
		/// </summary>
		public string DestinationEquipmentId { get; set; }
		/// <summary>
		///出料状态 1：出料开始；2：出料结束3：储缸Empty
		/// </summary>
		public int TransferStatus { get; set; }

		/// <summary>
		/// 工单号
		/// </summary>
		public string ProductionNO { get; set; }

		/// <summary>
		/// 第x批
		/// </summary>
		public string BatchIndex { get; set; }

	}
}
