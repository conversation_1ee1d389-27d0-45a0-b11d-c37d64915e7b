using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class KpitgtRequestModel : RequestPageModelBase
    {
        public KpitgtRequestModel()
        {
        }
           /// <summary>
           /// Desc:数据名称
           /// Default:
           /// Nullable:False
           /// </summary>
        public string DataName { get; set; }
           /// <summary>
           /// Desc:数据类型
           /// Default:
           /// Nullable:False
           /// </summary>
        public string DateType { get; set; }
           /// <summary>
           /// Desc:对应物理模型区域
           /// Default:
           /// Nullable:False
           /// </summary>
        public string ModelRef { get; set; }
           /// <summary>
           /// Desc:年
           /// Default:
           /// Nullable:False
           /// </summary>
        public int? Year { get; set; }
           /// <summary>
           /// Desc:月
           /// Default:
           /// Nullable:False
           /// </summary>
        public int? Month { get; set; }
           /// <summary>
           /// Desc:目标值
           /// Default:
           /// Nullable:False
           /// </summary>
        public decimal Tgt { get; set; }
           /// <summary>
           /// Desc:单位
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Unit { get; set; }
        /// <summary>
        /// Desc:开始时间
        /// Default:
        /// Nullable:False
        /// </summary>
        public  DateTime? StartTime { get; set; }
        /// <summary>
        /// Desc:结束时间
        /// Default:
        /// Nullable:False
        /// </summary>
        public DateTime? EndTime { get; set; }

    }
}