using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class ProdtgtContainerTableModel
    {
        public ProdtgtContainerTableModel()
        {
        }
        /// <summary>
        /// Desc:生产线
        /// Default:
        /// Nullable:False
        /// </summary>
        public string LineName { get; set; }
        /// <summary>
        /// Desc:规格
        /// Default:
        /// Nullable:False
        /// </summary>
        public string SalesContainer { get; set; }
        /// <summary>
        /// Desc:规格所属群组
        /// Default:
        /// Nullable:False
        /// </summary>
        public string SalesContainerGrpName { get; set; }
        /// <summary>
        /// Desc:一月
        /// Default:
        /// Nullable:False
        /// </summary>
        public string January { get; set; }
        /// <summary>
        /// Desc:二月
        /// Default:
        /// Nullable:False
        /// </summary>
        public string February { get; set; }
        /// <summary>
        /// Desc:三月
        /// Default:
        /// Nullable:False
        /// </summary>
        public string March { get; set; }
        /// <summary>
        /// Desc:四月
        /// Default:
        /// Nullable:False
        /// </summary>
        public string April { get; set; }
        /// <summary>
        /// Desc:五月
        /// Default:
        /// Nullable:False
        /// </summary>
        public string May { get; set; }
        /// <summary>
        /// Desc:六月
        /// Default:
        /// Nullable:False
        /// </summary>
        public string June { get; set; }
        /// <summary>
        /// Desc:七月
        /// Default:
        /// Nullable:False
        /// </summary>
        public string July { get; set; }
        /// <summary>
        /// Desc:八月
        /// Default:
        /// Nullable:False
        /// </summary>
        public string August { get; set; }
        /// <summary>
        /// Desc:九月
        /// Default:
        /// Nullable:False
        /// </summary>
        public string September { get; set; }
        /// <summary>
        /// Desc:十月
        /// Default:
        /// Nullable:False
        /// </summary>
        public string October { get; set; }
        /// <summary>
        /// Desc:十一月
        /// Default:
        /// Nullable:False
        /// </summary>
        public string November { get; set; }
        /// <summary>
        /// Desc:十二月
        /// Default:
        /// Nullable:False
        /// </summary>
        public string December { get; set; }

    }
}