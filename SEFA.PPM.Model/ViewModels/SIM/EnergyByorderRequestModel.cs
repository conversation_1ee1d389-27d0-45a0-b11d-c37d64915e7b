using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels.SIM
{
    public class EnergyByorderRequestModel : RequestPageModelBase
    {
        public EnergyByorderRequestModel()
        {
        }
        /// <summary>
        /// Desc:工单执行表ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ExecutionId { get; set; }
        /// <summary>
        /// Desc:仪表名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MeterName { get; set; }
        /// <summary>
        /// Desc:仪表编码
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MeterCode { get; set; }
        /// <summary>
        /// Desc:能源类型
        /// Default:
        /// Nullable:False
        /// </summary>
        public string EnergyType { get; set; }
        /// <summary>
        /// Desc:能耗数值
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal EnergyQty { get; set; }
        /// <summary>
        /// Desc:单位
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Unit { get; set; }

    }
}