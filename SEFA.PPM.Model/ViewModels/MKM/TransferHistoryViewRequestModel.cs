using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.MKM.Model.ViewModels
{
    ///<summary>
    ///
    ///</summary>

    public class TransferHistoryViewRequestModel : RequestPageModelBase
    {
        public string Types { get; set; }
        
        public string SapNo { get; set; }
        public string OldProBatch { get; set; }
        public string NewProBatch { get; set; }
        public string StartTime { get; set; }

        public string ConNameOld { get; set; }

        public string ConNameNew { get; set; }

       
        public string EndTime { get; set; }

        public string SourceMaterial { get; set; }

        public string DestinationMaterial { get; set; }

        public String OldBatch { get; set; }
        public String NewBatch { get; set; }
        public String OLD_SUB_LOT_ID { get; set; }
        public String NEW_SUB_LOT_ID { get; set; }

        public String OldSoureID { get; set; }
        public String OldSoureBin { get; set; }
        public String NewDestinationID { get; set; }
        public String NewDestinationBin { get; set; }

        public String MaterialID { get; set; }

        public String SAP { get; set; }

        public String ConID { get; set; }

        public String Key { get; set; }
    }
}