using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("V_MATERIAL_PV")]
    public class PvEntity //: //EntityBase
    {
        public PvEntity()
        {
        }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_ID")]
        public string MaterialId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "CODE")]
        public string Code { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "NAME")]
        public string Name { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "DESCRIPTION")]
        public string Description { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "FullBagWeight")]
        public string Fullbagweight { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "NeedWeighingCheck")]
        public string Needweighingcheck { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PreweighToleranceMaxPercent")]
        public string Preweightolerancemaxpercent { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PreweighToleranceMinPercent")]
        public string Preweightoleranceminpercent { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "RequiresPreWeigh")]
        public string Requirespreweigh { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "WeighingCheck_HighTolerance")]
        public string WeighingcheckHightolerance { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "WeighingCheck_LowTolerance")]
        public string WeighingcheckLowtolerance { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PrepByPO")]
        public string Prepbypo { get; set; }
    }
}