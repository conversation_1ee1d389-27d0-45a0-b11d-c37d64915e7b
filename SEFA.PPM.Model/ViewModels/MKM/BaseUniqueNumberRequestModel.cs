using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.MKM.Model.ViewModels
{
    public class BaseUniqueNumberRequestModel : RequestPageModelBase
    {
        public BaseUniqueNumberRequestModel()
        {
        }
           /// <summary>
           /// Desc:类型
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Type { get; set; }
           /// <summary>
           /// Desc:下一个值
           /// Default:
           /// Nullable:False
           /// </summary>
        public string NextCode { get; set; }
           /// <summary>
           /// Desc:最大值
           /// Default:
           /// Nullable:False
           /// </summary>
        public string MaxCode { get; set; }
           /// <summary>
           /// Desc:最小值
           /// Default:
           /// Nullable:False
           /// </summary>
        public string MinCode { get; set; }
           /// <summary>
           /// Desc:前缀
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Prefix { get; set; }
           /// <summary>
           /// Desc:关联表名
           /// Default:
           /// Nullable:True
           /// </summary>
        public string TableName { get; set; }
           /// <summary>
           /// Desc:关联表ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string TableId { get; set; }
           /// <summary>
           /// Desc:生成规则
           /// Default:
           /// Nullable:False
           /// </summary>
        public string SequenceType { get; set; }
           /// <summary>
           /// Desc:重置周期
           /// Default:
           /// Nullable:False
           /// </summary>
        public string ResetType { get; set; }
           /// <summary>
           /// Desc:功能
           /// Default:
           /// Nullable:False
           /// </summary>
        public string FeatureId { get; set; }

    }
}