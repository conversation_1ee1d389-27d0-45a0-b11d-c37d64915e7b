using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.ViewModels.MKM.PrintView
{
    public class TeamplatePrintPreMin
    {
        //工单号
        public string OrderNo { get; set; }
        //物料名称
        public string Material_Name { get; set; }
        //物料编码
        public string Material_Code { get; set; }
        //批次
        public string Lot_Code { get; set; }
        //配方名称
        public string Production_Name { get; set; }
        //配方
        public string Production_Desc { get; set; }
        //数量
        public string Material_Inventory_Qty { get; set; }
        //单位
        public string Unit_Code { get; set; }
        //缸重
        public string Plan_Qty { get; set; }
        //生产线
        public string Line_Code { get; set; }
        //追溯码
        public string Sublot_Code { get; set; }
        //二维码内容
        public string Trace_Code { get; set; }
        //操作人
        public string User { get; set; }
    }
}
