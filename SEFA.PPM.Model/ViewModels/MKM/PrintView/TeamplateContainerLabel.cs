using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.ViewModels.MKM.PrintView
{
    public class TeamplateContainerLabel
    {
        //备料大标签
        //厂
        public string Plant { get; set; }
        //工单号码
        public string PONumber { get; set; }
        //配方名称
        public string MaterialName { get; set; }
        //生产线
        public string LineCode { get; set; }
        //缸序
        public string TankOrder { get; set; }
        //配方
        public string Formula { get; set; }
        //缸重
        public string TankWeight { get; set; }
        //备料日期
        public string PreparationDate { get; set; }
        //板数
        public string NUM { get; set; }
        //计划日期
        public string PlanDate { get; set; }
        //水
        public string WaterWeight { get; set; }
        //单位
        public string Unit { get; set; }
        //备注
        public string Remark { get; set; }
        //二维码
        public string Pallet_Number { get; set; }
        //操作员
        public string Operator { get; set; }
        //复核员
        public string Reviewer { get; set; }

        public string Fill_Code { get; set; }
    }
}
