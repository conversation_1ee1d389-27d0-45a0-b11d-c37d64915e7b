using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.MKM.Model.ViewModels
{
    public class ProductionSummaryDetailsViewRequestModel : RequestPageModelBase
    {
        public ProductionSummaryDetailsViewRequestModel()
        {
        }
        /*
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public string LotId { get; set; }//批次ID
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Loction { get; set; }//目的地
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        public string MaterialCode { get; set; }//物料代码
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MaterialName { get; set; }//物料名称
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        public string LotCode { get; set; }//批号
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public decimal? Totalquantitys { get; set; }//总计产出
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Totalunit { get; set; }//总计产出单位
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public decimal? Inventory { get; set; }//库存剩余量
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public string InventoryUnit { get; set; }//库存剩余单位
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public int? Sublotcount { get; set; }//子批次计数
        */
    }
}