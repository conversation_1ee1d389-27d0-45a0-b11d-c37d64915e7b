using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class TippingWorkOrderViewRequestModel : RequestPageModelBase
    {
        public TippingWorkOrderViewRequestModel()
        {
        }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
        public decimal PlanQty { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
        public string MaterialVersionNumber { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public string RunEquipmentId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
        public string ProductionOrderId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
        public string PoSegmentRequirementId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public int? SortOrder { get; set; }
    }
}