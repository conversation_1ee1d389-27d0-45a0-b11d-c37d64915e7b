using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class StorageInventoryRequestModel : RequestPageModelBase
    {
        public StorageInventoryRequestModel()
        {
        }
           /// <summary>
           /// Desc:仓库编码
           /// Default:
           /// Nullable:True
           /// </summary>
        public string StorageCode { get; set; }
           /// <summary>
           /// Desc:仓库ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string StorageId { get; set; }
           /// <summary>
           /// Desc:物料ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MaterialId { get; set; }
           /// <summary>
           /// Desc:物料编码
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MaterialCode { get; set; }
           /// <summary>
           /// Desc:物料名称
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MaterialName { get; set; }
           /// <summary>
           /// Desc:物料版本ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MaterialVersionId { get; set; }
           /// <summary>
           /// Desc:物料版本号
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MaterialVersionCode { get; set; }
           /// <summary>
           /// Desc:单位ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string UnitId { get; set; }
           /// <summary>
           /// Desc:单位
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Unit { get; set; }
           /// <summary>
           /// Desc:数量
           /// Default:
           /// Nullable:True
           /// </summary>
        public decimal? Quantity { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
        public int Deleted { get; set; }

    }
}