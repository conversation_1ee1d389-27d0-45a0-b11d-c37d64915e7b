using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class MaterialPropertyViewRequestModel : RequestPageModelBase
    {
        public MaterialPropertyViewRequestModel()
        {
        }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Name { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Fulltype { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Bagweight { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Maxpercent { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Minpercent { get; set; }
    }
}