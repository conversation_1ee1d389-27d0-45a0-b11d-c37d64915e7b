using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.ViewModels.MKM.View
{
    public class CallModel
    {
        /// <summary>
        /// 数值
        /// </summary>
        public string Data { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string unit { get; set; }

        /// <summary>
        /// 正负数
        /// </summary>
        public string ValueType { get; set; }

        public string OldData { get; set; }


        public string ResultData { get; set; }

    }

    public class InventObj
    {
        /// <summary>
        /// 数值
        /// </summary>
        public string ID { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string SbSscc { get; set; }
        public decimal InQuantity { get; set; }
        /// <summary>
        /// 正负数
        /// </summary>
        public string SubId { get; set; }
        public string Remark { get; set; }



    }
}
