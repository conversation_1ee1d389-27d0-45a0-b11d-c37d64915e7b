using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.MKM.Model.ViewModels.View;

public class ContainerModel
{

    //string id, string equipmentId, string name
    public string ID { get; set; }
    public string EquipmentId { get; set; }
    public string Name { get; set; }

    //public string Quanlity { get; set; }
}