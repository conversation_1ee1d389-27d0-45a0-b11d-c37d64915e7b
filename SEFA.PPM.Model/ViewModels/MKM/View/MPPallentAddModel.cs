using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.MKM.Model.ViewModels.View;

public class MPPallentAddModel
{


    //int tareWeight, string uomID, string proBatchID, string proRequestID, string equipMentID, string materialId, string lotID, string subLotID

    //string id, string equipmentId, string name
    public decimal TareWeight { get; set; }
    public string UomID { get; set; }
    public string[] Pids { get; set; }
     
    public string BatchId { get; set; }
    
    public string ProBatchID { get; set; }

    public string EquipMentID { get; set; }

    public string MaterialId { get; set; }

    public string ProRequestID { get; set; }


    //备注
    public string Remark { get; set; }
    //订单ID
    public string PID { get; set; }
    //容器号
    public string ContainerName { get; set; }
    //单位
    public string unit { get; set; }
    //总托数
    public string Totalts { get; set; }
    //序号
    public string XHNumber { get; set; }
    //托数
    public string TS { get; set; }

    public string WNumber { get; set; }

    public string WNumberText { get; set; }

    public string EquipmentId { get; set; }
    //打印机
    public string PrintID { get; set; }

    public string ContainerID { get; set; }

    
    //public string LotID { get; set; }

    // public string SubLotID { get; set; }

    //public string Quanlity { get; set; }
}