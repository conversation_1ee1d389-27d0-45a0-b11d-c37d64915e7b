using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.ViewModels.MKM.View
{
    public class FullAmountModel
    {
       
        /// <summary>
        /// Desc:
        /// Default:子批次
        /// Nullable:True
        /// </summary>
        public string subID { get; set; }
        /// <summary>
        /// Desc:
        /// Default:选中的容器ID
        /// Nullable:True
        /// </summary>
        public string containerID { get; set; }
        /// <summary>
        /// Desc:
        /// Default:工单ID
        /// Nullable:True
        /// </summary>
        public string proOrderID { get; set; }
        /// <summary>
        /// Desc:
        /// Default:批次ID
        /// Nullable:True
        /// </summary>
        public string batchID { get; set; }

        public string equpmentID { get; set; }

        public string PrintId { get; set; }

    }
}
