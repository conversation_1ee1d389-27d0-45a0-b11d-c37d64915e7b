using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.ViewModels.MKM.View
{
    public class EquipmentStatusModel
    {
        //产线
        public string LineName { get; set; }
        /// <summary>
        /// 设备1状态
        /// </summary>
        public string EquipmentStatus1 { get; set; }
        /// <summary>
        /// 设备1名称
        /// </summary>
        public string EquipmentName1 { get; set; }
        /// <summary>
        /// 设备2状态
        /// </summary>
        public string EquipmentStatus2 { get; set; }
        /// <summary>
        /// 设备2名称
        /// </summary>
        public string EquipmentName2 { get; set; }
        /// <summary>
        /// 设备3状态
        /// </summary>
        public string EquipmentStatus3 { get; set; }
        /// <summary>
        /// 设备3名称
        /// </summary>
        public string EquipmentName3 { get; set; }
        /// <summary>
        /// 设备4状态
        /// </summary>
        public string EquipmentStatus4 { get; set; }
        /// <summary>
        /// 设备4名称
        /// </summary>
        public string EquipmentName4 { get; set; }
        /// <summary>
        /// 物料名称1
        /// </summary>
        public string m1 { get; set; }
        /// <summary>
        /// 水位状态1
        /// </summary>
        public string s1 { get; set; }
        /// <summary>
        /// 物料名称2
        /// </summary>
        public string m2 { get; set; }
        /// <summary>
        /// 水位状态2
        /// </summary>
        public string s2 { get; set; }
        /// <summary>
        /// 物料名称3
        /// </summary>
        public string m3 { get; set; }
        /// <summary>
        /// 水位状态3
        /// </summary>
        public string s3 { get; set; }
        /// <summary>
        /// 物料名称4
        /// </summary>
        public string m4 { get; set; }
        /// <summary>
        /// 水位状态4
        /// </summary>
        public string s4 { get; set; }
        /// <summary>
        /// 物料名称5
        /// </summary>
        public string m5 { get; set; }
        /// <summary>
        /// 水位状态5
        /// </summary>
        public string s5 { get; set; }
        /// <summary>
        /// 物料名称6
        /// </summary>
        public string m6 { get; set; }
        /// <summary>
        /// 水位状态6
        /// </summary>
        public string s6 { get; set; }
        /// <summary>
        /// 物料名称7
        /// </summary>
        public string m7 { get; set; }
        /// <summary>
        /// 水位状态7
        /// </summary>
        public string s7 { get; set; }
        /// <summary>
        /// 物料名称8
        /// </summary>
        public string m8 { get; set; }
        /// <summary>
        /// 水位状态8
        /// </summary>
        public string s8 { get; set; }
        /// <summary>
        /// OEE
        /// </summary>
        public decimal OEE { get; set; }
        /// <summary>
        /// OEEChain右下角
        /// </summary>
        public decimal OEEChain { get; set; }
        /// <summary>
        /// 表现性
        /// </summary>
        public decimal Expressive { get; set; }
        /// <summary>
        /// 表现性右下角
        /// </summary>
        public decimal ExpressiveChain { get; set; }
        /// <summary>
        /// 灌装机实时速度
        /// </summary>
        public decimal RealtSpeed { get; set; }
        /// <summary>
        /// 灌装机标准速度
        /// </summary>
        public decimal StandardSpeed { get; set; }
        /// <summary>
        /// 当天灌装工单时间
        /// </summary>
        public string LastOverTime { get; set; }
        /// <summary>
        /// 是否+1
        /// </summary>
        public int day { get; set; }

    }
    /// <summary>
    /// 设备信息对象
    /// </summary>
    public class L2Models
    {
        /// <summary>
        /// 设备状态
        /// </summary>
        public string EquipmentStatus { get; set; }
        /// <summary>
        /// 设备名称
        /// </summary>
        public string EquipmentName { get; set; }
    }
}
