using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using Magicodes.ExporterAndImporter.Core;
using MiniExcelLibs.Attributes;
namespace SEFA.MKM.Model.Models
{  
    public class ConsumModels//: EntityBase
    
    {
        /// <summary>
        /// Desc:工单
        /// Default:
        /// Nullable:False
        /// </summary>
        [ImporterHeader(Name = "工单")]
        [ExporterHeader(DisplayName = "工单")]
        [ExcelColumnName("工单")]
        public string ProductionOrderNo { get; set; }

        /// <summary>
        /// Desc:物料编码
        /// Default:
        /// Nullable:False
        /// </summary>
        [ImporterHeader(Name = "物料编码")]
        [ExporterHeader(DisplayName = "物料编码")]
        [ExcelColumnName("物料编码")]
        public string MCode { get; set; }
        /// <summary>
        /// Desc:物料名称
        /// Default:
        /// Nullable:False
        /// </summary>
        [ImporterHeader(Name = "物料名称")]
        [ExporterHeader(DisplayName = "物料名称")]
        [ExcelColumnName("物料名称")]
        public string MName { get; set; }


        /// <summary>
        /// Desc:批次
        /// Default:
        /// Nullable:False
        /// </summary>
        [ImporterHeader(Name = "批次")]
        [ExporterHeader(DisplayName = "批次")]
        [ExcelColumnName("批次")]
        public string BatchCode { get; set; }



        /// <summary>
        /// Desc:数量
        /// Default:
        /// Nullable:False
        /// </summary>
        [ImporterHeader(Name = "数量")]
        [ExporterHeader(DisplayName = "数量")]
        [ExcelColumnName("数量")]
        public decimal IQuantity { get; set; }

        /// <summary>
        /// Desc:单位
        /// Default:
        /// Nullable:False
        /// </summary>
        [ImporterHeader(Name = "单位")]
        [ExporterHeader(DisplayName = "单位")]
        [ExcelColumnName("单位")]
        public string QuantityUnit { get; set; }


        /// <summary>
        /// Desc:位置
        /// Default:
        /// Nullable:False
        /// </summary>
        [ImporterHeader(Name = "来源")]
        [ExporterHeader(DisplayName = "来源")]
        [ExcelColumnName("来源")]
        public string SourceName { get; set; }

        /// <summary>
        /// Desc:SAP位置
        /// Default:
        /// Nullable:False
        /// </summary>
        [ImporterHeader(Name = "SAP位置")]
        [ExporterHeader(DisplayName = "SAP位置")]
        [ExcelColumnName("SAP位置")]
        public string SourceCode { get; set; }
        /// <summary>
        /// Desc:有效期
        /// Default:
        /// Nullable:False
        /// </summary>
        [ImporterHeader(Name = "创建日期")]
        [ExporterHeader(DisplayName = "创建日期")]
        [ExcelColumnName("创建日期")]
        public string CreateTime { get; set; }

    }
}