using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using Magicodes.ExporterAndImporter.Core;
namespace SEFA.MKM.Model.Models
{
    ///<summary>
    ///物料库存管理
    ///</summary>

   
    public class SelectDataModel 
    {
        public SelectDataModel()
        {
        }
        public string ID { get; set; }

        public string KeyValue { get; set; }

        public string KeyText { get; set; }

        public decimal PlanNumber { get; set; }
        public string BatchId { get; set; }
        public string BNumber { get; set; }
        public string UId { get; set; }
        public string UintName { get; set; }
        public string MId { get; set; }
        public string MCode { get; set; }

        public string TagerQty { get; set; }
        public string MName { get; set; }
        public string BagSize { get; set; }

        public string FormulaNo { get; set; }
    }
}