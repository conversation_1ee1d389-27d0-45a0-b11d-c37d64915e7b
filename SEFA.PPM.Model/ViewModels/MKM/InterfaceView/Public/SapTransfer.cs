using SEFA.PPM.Model.Models.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.ViewModels.MKM.InterfaceView
{

    public class SapTransfer
    {
        /// <summary>
        /// 表头文本
        /// </summary>
        public string BKTXT { get; set; }
        /// <summary>
        /// 过账日期
        /// </summary>
        public string BUDAT { get; set; }

        public List<SendTran> SendList { get; set; }

    }
    public class SendTran
    {
        /// <summary>
        /// LINE_ID 待确认
        /// </summary>
        public string LINE_ID { get; set; }
        /// <summary>
        /// 移动类型
        /// </summary>
        public string BWART { get; set; }
        /// <summary>
        /// 物料编码
        /// </summary>
        public string MATNR { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public decimal MENGE { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        public string MEINS { get; set; }
        /// <summary>
        /// 发货工厂代码
        /// </summary>
        public string WERKS { get; set; }
        /// <summary>
        /// 发货库存地点
        /// </summary>
        public string LGORT { get; set; }
        /// <summary>
        /// 发货批次
        /// </summary>
        public string CHARG { get; set; }
        /// <summary>
        /// 接收工厂代码
        /// </summary>
        public string UMWRK { get; set; }
        /// <summary>
        /// 接收库存地点
        /// </summary>
        public string UMLGO { get; set; }
        /// <summary>
        /// 接收批次
        /// </summary>
        public string UMCHA { get; set; }

        /// <summary>
        /// 行文本
        /// </summary>
        public string SGTXT { get; set; }
    }

    public class ReturnStockTran
    {
        /// <summary>
        /// 物料凭证编码
        /// </summary>
        public string Sucess { get; set; }

        /// <summary>
        /// 物料凭证编码
        /// </summary>
        public string Msg { get; set; }

        /// <summary>
        /// 物料凭证编码
        /// </summary>
        public string MCodeNameplateNo { get; set; }
        /// <summary>
        /// 物料凭证编码物料凭证年
        /// </summary>
        public string MCodeNameplateNoYear { get; set; }
        /// <summary>
        /// 物料凭证行号(返回数据行号)
        /// </summary>
        public string BUDAT { get; set; }
        /// <summary>
        /// 待确定
        /// </summary>
        public string LINE_ID { get; set; }
        /// <summary>
        /// 传入的移动类型
        /// </summary>
        public string BWART { get; set; }
        /// <summary>
        /// 物料Code
        /// </summary>
        public string MCode { get; set; }

        /// <summary>
        /// 物料数量
        /// </summary>
        public decimal MENGE { get; set; }
        /// <summary>
        /// 物料单位
        /// </summary>
        public string MEINS { get; set; }


    }


    public class DataModel
    {

        public string Data { get; set; }

        public string Data1 { get; set; }

        public string Data2 { get; set; }

        public string Data3 { get; set; }

        public string Data4 { get; set; }

        public string Data5 { get; set; }


        public decimal Data6 { get; set; }

        public string Data7 { get; set; }


    }

}
