using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.MKM.Model.ViewModels
{
    public class DicBinBpalletRequestModel : RequestPageModelBase
    {
        public DicBinBpalletRequestModel()
        {
        }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
        public string SCode { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
        public string EquipmentCode { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
        public string EquipmentName { get; set; }

    }
}