using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class VerifiyDetailRequestModel : RequestPageModelBase
    {
        public VerifiyDetailRequestModel()
        {
        }
        public string VerifiyListID { get; set; }
        
        public string Qty { get; set; }

        public string[] IDS { get; set; }


        public string types { get; set; }

        public string Difference { get; set; }

        public string VID { get; set; }

        public string SSCC { get; set; }
        public string ID { get; set; }
        /// <summary>
        /// Desc:物料ID
        /// Default:
        /// Nullable:False
        /// </summary>

        public string Reason { get; set; }

        /// <summary>
        /// Desc:库存数量
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal CurrentQuantity { get; set; }
        /// <summary>
        /// Desc:盘点实数
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal ActualQuantity { get; set; }

    }
}