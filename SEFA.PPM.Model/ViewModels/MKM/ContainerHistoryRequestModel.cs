using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;
namespace SEFA.MKM.Model.ViewModels
{
    ///<summary>
    ///
    ///</summary>

   
    public class ContainerHistoryRequestModel : RequestPageModelBase
    {
        public ContainerHistoryRequestModel()
        {
        }

       
        public string ContainerId { get; set; }
       
        public string Type { get; set; }
       
        public string EquipmentId { get; set; }
        
        public string EquipmentRequirementId { get; set; }
       
        public string State { get; set; }
        
        public string Comment { get; set; }
        
        public string ProductOrderId { get; set; }
      
        public string BatchId { get; set; }
        
        public string MaterialId { get; set; }
       
        public string SublotId { get; set; }
       
        public string Quantity { get; set; }
        
        public string QuantityUomId { get; set; }
       
        public string BatchConsumedRequirementId { get; set; }
       
        public string ConsumedRequirementId { get; set; }
       
        public string ProductionExecutionId { get; set; }
       
        public string Status { get; set; }
       
        public string ContainerCode { get; set; }
      
        public string MaterialProducedActualId { get; set; }
       
        public string MaterialConsumedActualId { get; set; }
       
        public string LotId { get; set; }
        
        public DateTime? ExpirationDate { get; set; }



    }
}