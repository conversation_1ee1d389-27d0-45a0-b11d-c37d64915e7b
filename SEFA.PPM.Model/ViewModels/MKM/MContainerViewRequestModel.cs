using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;
namespace SEFA.MKM.Model.ViewModels
{
    ///<summary>
    ///
    ///</summary>


    public class MContainerViewRequestModel : RequestPageModelBase
    {
        public MContainerViewRequestModel()
        {
        }
        public string ContainerName { get; set; }
        public string Material { get; set; }
        public string Location { get; set; }
        public string LotId { get; set; }
        public string SubLotId { get; set; }

        public string ProOrder { get; set; }

        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        public string[] ClassId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        public string[] StatusId { get; set; }


    }
}