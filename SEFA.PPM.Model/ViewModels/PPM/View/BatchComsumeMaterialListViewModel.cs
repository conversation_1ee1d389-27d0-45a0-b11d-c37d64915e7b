using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class BatchComsumeMaterialListViewModel : RequestPageModelBase
    {
        public BatchComsumeMaterialListViewModel()
        {
        }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        public string BatchId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        public string BatchCode { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
        public decimal Quantity { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
        public string MaterialCode { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MaterialDescription { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public string UnitName { get; set; }

    }
}