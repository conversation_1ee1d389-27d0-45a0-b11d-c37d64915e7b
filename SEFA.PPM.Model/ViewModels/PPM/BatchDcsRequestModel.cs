using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class BatchDcsRequestModel : RequestPageModelBase
    {
        public BatchDcsRequestModel()
        {
        }
        /// <summary>
        /// Desc:LineID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string LineId { get; set; }
        /// <summary>
        /// Desc:LineCode
        /// Default:
        /// Nullable:False
        /// </summary>
        public string LineCode { get; set; }
        /// <summary>
        /// Desc:LineName
        /// Default:
        /// Nullable:False
        /// </summary>
        public string LineName { get; set; }
        /// <summary>
        /// Desc:工单ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ProductionOrderId { get; set; }
        /// <summary>
        /// Desc:工单号
        /// Default:
        /// Nullable:False
        /// </summary>
        public string PoNo { get; set; }
        /// <summary>
        /// Desc:批次ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string BatchtId { get; set; }
        /// <summary>
        /// Desc:批次号
        /// Default:
        /// Nullable:False
        /// </summary>
        public string BatchtNo { get; set; }
        /// <summary>
        /// Desc:物料ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialId { get; set; }
        /// <summary>
        /// Desc:物料代码
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialCode { get; set; }
        /// <summary>
        /// Desc:物料名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialName { get; set; }
        /// <summary>
        /// Desc:物料版本ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string MaterialVer { get; set; }
        /// <summary>
        /// Desc:单位
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Unit { get; set; }
        /// <summary>
        /// Desc:单位ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string UnitId { get; set; }
        /// <summary>
        /// Desc:标准需求数量
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal StandardQuantity { get; set; }
        /// <summary>
        /// Desc:计划数量
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal PlanQuantity { get; set; }
        /// <summary>
        /// Desc:状态
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Status { get; set; }
        /// <summary>
        /// Desc:下发数据
        /// Default:
        /// Nullable:True
        /// </summary>
        public string SendData { get; set; }
        /// <summary>
        /// Desc:返回数据
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ResponseData { get; set; }
        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Remark { get; set; }

    }
}