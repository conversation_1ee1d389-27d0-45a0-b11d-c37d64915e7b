using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class FormulalossRequestModel : RequestPageModelBase
    {
        public FormulalossRequestModel()
        {
        }
           /// <summary>
           /// Desc:配方ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MatId { get; set; }
           /// <summary>
           /// Desc:配方名称
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MatName { get; set; }
           /// <summary>
           /// Desc:使用的缸重
           /// Default:
           /// Nullable:True
           /// </summary>
        public decimal? TankWeight { get; set; }
           /// <summary>
           /// Desc:锅数
           /// Default:
           /// Nullable:True
           /// </summary>
        public int? Tankquantity { get; set; }
           /// <summary>
           /// Desc:预计损耗
           /// Default:
           /// Nullable:False
           /// </summary>
        public decimal EstimateLoss { get; set; }
           /// <summary>
           /// Desc:生产日期
           /// Default:
           /// Nullable:False
           /// </summary>
        public DateTime Productdate { get; set; }
           /// <summary>
           /// Desc:产线ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string LineId { get; set; }
           /// <summary>
           /// Desc:产线名称
           /// Default:
           /// Nullable:False
           /// </summary>
        public string LineName { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
        public int Deleted { get; set; }

    }
}