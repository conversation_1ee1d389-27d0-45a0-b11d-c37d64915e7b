using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class WorkorderthroatRequestModel : RequestPageModelBase
    {
        public WorkorderthroatRequestModel()
        {
        }
           /// <summary>
           /// Desc:工单ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string OrderId { get; set; }
           /// <summary>
           /// Desc:工单代码
           /// Default:
           /// Nullable:True
           /// </summary>
        public string OrderCode { get; set; }
           /// <summary>
           /// Desc:喉头ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MatId { get; set; }
           /// <summary>
           /// Desc:喉头name
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MatName { get; set; }
           /// <summary>
           /// Desc:喉头编码
           /// Default:
           /// Nullable:False
           /// </summary>
        public string ThroatCode { get; set; }
           /// <summary>
           /// Desc:喉头批号 
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Throatformorder { get; set; }

        /// <summary>
        /// Desc:追溯码
        /// Default:
        /// Nullable:TRUE
        /// </summary>
        public string SSCC { get; set; }
        /// <summary>
        /// Desc:喉头添加重量
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal Inweight { get; set; }
           /// <summary>
           /// Desc:喉头添加比例
           /// Default:
           /// Nullable:False
           /// </summary>
        public decimal Rate { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
        public int Deleted { get; set; }

        /// <summary>
        /// Desc:标准最大比例
        /// Default:
        /// Nullable:True
        /// </summary>
        public decimal? StandardRate { get; set; }

        /// <summary>
        /// Desc:SAP配方
        /// Default:
        /// Nullable:True
        /// </summary>
        public string SapFormula { get; set; }

        /// <summary>
        /// Desc:开始日期
        /// Default:
        /// Nullable:True
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// Desc:开始日期
        /// Default:
        /// Nullable:True
        /// </summary>
        public DateTime? EndTime { get; set; }

    }
}