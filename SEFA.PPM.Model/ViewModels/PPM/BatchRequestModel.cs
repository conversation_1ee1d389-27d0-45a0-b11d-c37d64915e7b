using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class BatchRequestModel : RequestPageModelBase
    {
        public BatchRequestModel()
        {
        }
           /// <summary>
           /// Desc:产线ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string LineId { get; set; }
           /// <summary>
           /// Desc:订单ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string ProductionOrderId { get; set; }
           /// <summary>
           /// Desc:订单工序需求ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string PoSegmentRequirementId { get; set; }
           /// <summary>
           /// Desc:订单工序生产需求ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string PoProducedRequirementId { get; set; }
           /// <summary>
           /// Desc:批次序号
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Number { get; set; }
           /// <summary>
           /// Desc:批次号
           /// Default:
           /// Nullable:True
           /// </summary>
        public string BatchCode { get; set; }
           /// <summary>
           /// Desc:执行设备ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string RunEquipmentId { get; set; }
           /// <summary>
           /// Desc:物料ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MaterialId { get; set; }
           /// <summary>
           /// Desc:物料版本ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MaterialVersionId { get; set; }
           /// <summary>
           /// Desc:物料编码
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MaterialCode { get; set; }
           /// <summary>
           /// Desc:物料描述
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MaterialDescription { get; set; }
           /// <summary>
           /// Desc:目标数量
           /// Default:
           /// Nullable:True
           /// </summary>
        public decimal? TargetQuantity { get; set; }
           /// <summary>
           /// Desc:计量单位ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string UnitId { get; set; }
           /// <summary>
           /// Desc:生产状态
           /// Default:
           /// Nullable:True
           /// </summary>
        public int? Status { get; set; }
           /// <summary>
           /// Desc:准备状态
           /// Default:
           /// Nullable:True
           /// </summary>
        public int? PrepStatus { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
        public int Deleted { get; set; }

    }
}