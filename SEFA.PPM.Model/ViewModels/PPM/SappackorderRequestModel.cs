using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class SappackorderRequestModel : RequestPageModelBase
    {
        public SappackorderRequestModel()
        {
        }
           /// <summary>
           /// Desc:包装工单/灌装工单
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Pdtype { get; set; }
           /// <summary>
           /// Desc:工厂
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Werks { get; set; }
           /// <summary>
           /// Desc:工单号
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Aufnr { get; set; }
           /// <summary>
           /// Desc:订单类型
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Auart { get; set; }
           /// <summary>
           /// Desc:物料编码
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Matnr { get; set; }
           /// <summary>
           /// Desc:物料描述
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Maktx { get; set; }
           /// <summary>
           /// Desc:计划生产量
           /// Default:0
           /// Nullable:False
           /// </summary>
        public decimal Psmng { get; set; }
           /// <summary>
           /// Desc:单位
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Amein { get; set; }
           /// <summary>
           /// Desc:工作中心
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Arbpl { get; set; }
           /// <summary>
           /// Desc:生产线
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Veran { get; set; }
           /// <summary>
           /// Desc:工单开始生产日期
           /// Default:
           /// Nullable:True
           /// </summary>
        public DateTime? Gstrp { get; set; }
           /// <summary>
           /// Desc:工单完成生产日期
           /// Default:
           /// Nullable:True
           /// </summary>
        public DateTime? Gltrp { get; set; }
           /// <summary>
           /// Desc:对应灌装工单-物料编码
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Matnr2 { get; set; }
           /// <summary>
           /// Desc:对应灌装工单-物料描述
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Maktx2 { get; set; }
           /// <summary>
           /// Desc:对应灌装工单-工单号
           /// Default:
           /// Nullable:True
           /// </summary>
        public string AuartFill { get; set; }
           /// <summary>
           /// Desc:煮料组件编码
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MatnrComp { get; set; }
           /// <summary>
           /// Desc:煮料组件描述
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MaktxComp { get; set; }
           /// <summary>
           /// Desc:煮料组件用量
           /// Default:0
           /// Nullable:False
           /// </summary>
        public decimal PsmngComp { get; set; }
           /// <summary>
           /// Desc:煮料组件单位
           /// Default:
           /// Nullable:True
           /// </summary>
        public string AmeinComp { get; set; }
           /// <summary>
           /// Desc:销售订单
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Kdauf { get; set; }
           /// <summary>
           /// Desc:销售订单项目
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Kdpos { get; set; }
           /// <summary>
           /// Desc:收货仓库
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Lgort { get; set; }
           /// <summary>
           /// Desc:售达方
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Kunnr3 { get; set; }
           /// <summary>
           /// Desc:售达方描述
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Kunnr1 { get; set; }
           /// <summary>
           /// Desc:送达方
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Kunnr4 { get; set; }
           /// <summary>
           /// Desc:送达方描述
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Kunnr2 { get; set; }
           /// <summary>
           /// Desc:每层数量
           /// Default:0
           /// Nullable:False
           /// </summary>
        public int Plper { get; set; }
           /// <summary>
           /// Desc:每板层数
           /// Default:0
           /// Nullable:False
           /// </summary>
        public int Plodd { get; set; }
           /// <summary>
           /// Desc:每板数量
           /// Default:0
           /// Nullable:False
           /// </summary>
        public int Plqty { get; set; }
           /// <summary>
           /// Desc:采购订单
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Ebeln { get; set; }
           /// <summary>
           /// Desc:采购订单项目
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Ebelp { get; set; }
           /// <summary>
           /// Desc:MRP Ctrlr
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Dispo { get; set; }
           /// <summary>
           /// Desc:工单状态
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Status { get; set; }
           /// <summary>
           /// Desc:订购工厂
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Werks2 { get; set; }
           /// <summary>
           /// Desc:每箱PU单支数
           /// Default:0
           /// Nullable:False
           /// </summary>
        public decimal MngPu { get; set; }
           /// <summary>
           /// Desc:工单PU单支数
           /// Default:0
           /// Nullable:False
           /// </summary>
        public decimal MngPuo { get; set; }
           /// <summary>
           /// Desc:配方
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Normt { get; set; }
           /// <summary>
           /// Desc:净重
           /// Default:0
           /// Nullable:False
           /// </summary>
        public decimal Ntgew { get; set; }
           /// <summary>
           /// Desc:BOM Long Text
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Ltext1 { get; set; }
           /// <summary>
           /// Desc:工单Long Text
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Ltext2 { get; set; }
           /// <summary>
           /// Desc:Material Text
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Ltext3 { get; set; }
           /// <summary>
           /// Desc:PO Header Text
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Ltext4 { get; set; }
           /// <summary>
           /// Desc:Routing Long Text
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Ltext5 { get; set; }
           /// <summary>
           /// Desc:Prodtn Memo Long Text
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Ltext6 { get; set; }
           /// <summary>
           /// Desc:GR收货数量
           /// Default:0
           /// Nullable:False
           /// </summary>
        public decimal Wemng { get; set; }
           /// <summary>
           /// Desc:销售容器
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Magrv { get; set; }
           /// <summary>
           /// Desc:销售容器描述
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Bezei { get; set; }
           /// <summary>
           /// Desc:包装规格
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Vhart { get; set; }
           /// <summary>
           /// Desc:包装规格描述
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Vtext { get; set; }
           /// <summary>
           /// Desc:类别
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Category { get; set; }
           /// <summary>
           /// Desc:批次
           /// Default:
           /// Nullable:True
           /// </summary>
        public string BatchFw { get; set; }
           /// <summary>
           /// Desc:保质期到期日
           /// Default:
           /// Nullable:True
           /// </summary>
        public string ShelfFw { get; set; }
           /// <summary>
           /// Desc:生产线代码
           /// Default:
           /// Nullable:True
           /// </summary>
        public string VeranFw { get; set; }
           /// <summary>
           /// Desc:纸箱及招纸
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MaktxCFw { get; set; }
           /// <summary>
           /// Desc:包装比例
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Lhmg1Fw { get; set; }
           /// <summary>
           /// Desc:收货数量及批次
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MengeCFw { get; set; }
           /// <summary>
           /// Desc:工单产品物料组
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Matkl { get; set; }
           /// <summary>
           /// Desc:产品保质期
           /// Default:0
           /// Nullable:False
           /// </summary>
        public decimal Mhdhb { get; set; }
           /// <summary>
           /// Desc:保质期单位
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Iprkz { get; set; }
           /// <summary>
           /// Desc:市场所有者
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Ferth { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public string FlagMes { get; set; }
           /// <summary>
           /// Desc:Sales Mkt 销售市场
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Slmkt { get; set; }
           /// <summary>
           /// Desc:Country/Region 国家/地区(EN)
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Landx { get; set; }
           /// <summary>
           /// Desc:Country/Region 国家/地区(ZF)
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Landz { get; set; }
           /// <summary>
           /// Desc:Regulatory Market
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Rgmkt { get; set; }
           /// <summary>
           /// Desc:Zone
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Vkbur { get; set; }
           /// <summary>
           /// Desc:Zone Desc
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Zndsc { get; set; }
           /// <summary>
           /// Desc:Cylinder Clearing Cd
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Zcylcd { get; set; }
           /// <summary>
           /// Desc:Cylinder Clearing Description
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Codtx { get; set; }
           /// <summary>
           /// Desc:Sort Sequence
           /// Default:0
           /// Nullable:False
           /// </summary>
        public int Sortf { get; set; }
           /// <summary>
           /// Desc:报价日期
           /// Default:
           /// Nullable:True
           /// </summary>
        public DateTime? Ihran { get; set; }
           /// <summary>
           /// Desc:UPC/EAU
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Ean11 { get; set; }
           /// <summary>
           /// Desc:更新时间
           /// Default:
           /// Nullable:True
           /// </summary>
        public DateTime? Updatedate { get; set; }
           /// <summary>
           /// Desc:更新者
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Updateuserid { get; set; }
           /// <summary>
           /// Desc:操作状态
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Opstatus { get; set; }
    }
}