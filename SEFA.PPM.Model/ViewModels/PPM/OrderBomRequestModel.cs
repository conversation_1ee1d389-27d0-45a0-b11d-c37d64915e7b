using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class OrderBomRequestModel : RequestPageModelBase
    {
        public OrderBomRequestModel()
        {
        }
           /// <summary>
           /// Desc:工单号
           /// Default:
           /// Nullable:True
           /// </summary>
        public string ProductionOrderId { get; set; }
           /// <summary>
           /// Desc:订单类型
           /// Default:
           /// Nullable:True
           /// </summary>
        public string ProductionOrderType { get; set; }
           /// <summary>
           /// Desc:工厂
           /// Default:
           /// Nullable:True
           /// </summary>
        public string FactoryCode { get; set; }
           /// <summary>
           /// Desc:物料编码
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MaterialId { get; set; }
           /// <summary>
           /// Desc:物料描述
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MaterialDecs { get; set; }
           /// <summary>
           /// Desc:计划生产量
           /// Default:
           /// Nullable:True
           /// </summary>
        public decimal? Quantity { get; set; }
           /// <summary>
           /// Desc:单位
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Unit { get; set; }
           /// <summary>
           /// Desc:配方名称
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Formula { get; set; }
           /// <summary>
           /// Desc:配方编码
           /// Default:
           /// Nullable:True
           /// </summary>
        public string FormulaCode { get; set; }
           /// <summary>
           /// Desc:组件编码
           /// Default:
           /// Nullable:True
           /// </summary>
        public string ItemId { get; set; }
           /// <summary>
           /// Desc:组件描述
           /// Default:
           /// Nullable:True
           /// </summary>
        public string ItemDecs { get; set; }
           /// <summary>
           /// Desc:组件用量
           /// Default:
           /// Nullable:True
           /// </summary>
        public decimal? ItemQuantityWithLoss { get; set; }
           /// <summary>
           /// Desc:组件用量（不含损耗）
           /// Default:
           /// Nullable:True
           /// </summary>
        public decimal? ItemQuantityWithoutLoss { get; set; }
           /// <summary>
           /// Desc:组件单位
           /// Default:
           /// Nullable:True
           /// </summary>
        public string ItemUnit { get; set; }
           /// <summary>
           /// Desc:组件物料组
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MaterialGroup { get; set; }
           /// <summary>
           /// Desc:发料仓库
           /// Default:
           /// Nullable:True
           /// </summary>
        public string WarehouseId { get; set; }

    }
}