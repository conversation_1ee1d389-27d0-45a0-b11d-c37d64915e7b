using SEFA.Base.Model;
using System;

namespace SEFA.PPM.Model.ViewModels
{
    public class PoConsumeActualRequestModel : RequestPageModelBase
    {
        public PoConsumeActualRequestModel()
        {
        }
        /// <summary>
        /// Desc:订单ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ProductionOrderId { get; set; }
        /// <summary>
        /// Desc:订单工序消耗需求ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string PoConsumeRequirementId { get; set; }
        /// <summary>
        /// Desc:订单工序消耗物料代码
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialCode { get; set; }
        /// <summary>
        /// Desc:订单工序消耗物料名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialName { get; set; }
        /// <summary>
        /// Desc:生产执行记录ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ProductExecutionId { get; set; }
        /// <summary>
        /// Desc:设备ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string EquipmentId { get; set; }
        /// <summary>
        /// Desc:资源存储区ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string SourceEquipmentId { get; set; }
        /// <summary>
        /// Desc:数量
        /// Default:
        /// Nullable:True
        /// </summary>
        public decimal? Quantity { get; set; }
        /// <summary>
        /// Desc:计量单位ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string UnitId { get; set; }
        /// <summary>
        /// Desc:计量单位
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Unit { get; set; }
        /// <summary>
        /// Desc:批次ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string LotId { get; set; }
        /// <summary>
        /// Desc:批次
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Lot { get; set; }
        /// <summary>
        /// Desc:二级批次ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string SubLotId { get; set; }
        /// <summary>
        /// Desc:二级批次
        /// Default:
        /// Nullable:True
        /// </summary>
        public string SubLot { get; set; }
        /// <summary>
        /// Desc:二级批次状态
        /// Default:
        /// Nullable:True
        /// </summary>
        public int? SubLotStatus { get; set; }
        /// <summary>
        /// Desc:存储仓库
        /// Default:
        /// Nullable:True
        /// </summary>
        public string StorageBin { get; set; }
        /// <summary>
        /// Desc:存储仓库库位
        /// Default:
        /// Nullable:True
        /// </summary>
        public string StorageLocation { get; set; }
        /// <summary>
        /// Desc:容器ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ContainerId { get; set; }
        /// <summary>
        /// Desc:班组ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string TeamId { get; set; }
        /// <summary>
        /// Desc:班组
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Team { get; set; }
        /// <summary>
        /// Desc:班次ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ShiftId { get; set; }
        /// <summary>
        /// Desc:班次
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Shift { get; set; }
        /// <summary>
        /// Desc:原因代码
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ReasonCode { get; set; }
        /// <summary>
        /// Desc:意见
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Comment { get; set; }
        /// <summary>
        /// Desc:发送状态
        /// Default:
        /// Nullable:True
        /// </summary>
        public int SendExternal { get; set; }
        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:True
        /// </summary>
        public int? Deleted { get; set; }
        /// <summary>
        /// Desc:操作人
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Operator { get; set; }
        /// <summary>
        /// Desc:操作时间
        /// Default:
        /// Nullable:True
        /// </summary>
        public DateTime? OperateDate { get; set; }
    }
}