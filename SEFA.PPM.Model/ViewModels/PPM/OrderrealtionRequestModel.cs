using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class OrderrealtionRequestModel : RequestPageModelBase
    {
        public OrderrealtionRequestModel()
        {
        }
           /// <summary>
           /// Desc:煮制生产工单ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string CookorderId { get; set; }
           /// <summary>
           /// Desc:煮制生产工单代码
           /// Default:
           /// Nullable:True
           /// </summary>
        public string CookorderCode { get; set; }
           /// <summary>
           /// Desc:灌装生产工单ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string FillorderId { get; set; }
           /// <summary>
           /// Desc:灌装生产工单代码
           /// Default:
           /// Nullable:True
           /// </summary>
        public string FillorderCode { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
        public int Deleted { get; set; }

    }
}