using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class PoSegmentRequirementRequestModel : RequestPageModelBase
    {
        public PoSegmentRequirementRequestModel()
        {
        }
           /// <summary>
           /// Desc:订单ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string ProductionOrderId { get; set; }
           /// <summary>
           /// Desc:工序ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string SegmentId { get; set; }
           /// <summary>
           /// Desc:状态
           /// Default:
           /// Nullable:True
           /// </summary>
        public int? Status { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Remark { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
        public int Deleted { get; set; }

    }
}