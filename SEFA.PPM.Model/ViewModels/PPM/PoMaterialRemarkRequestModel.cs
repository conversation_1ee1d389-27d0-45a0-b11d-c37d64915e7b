using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class PoMaterialRemarkRequestModel : RequestPageModelBase
    {
        public PoMaterialRemarkRequestModel()
        {
        }
           /// <summary>
           /// Desc:工单ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string ProducttionOrderId { get; set; }
           /// <summary>
           /// Desc:物料编码
           /// Default:
           /// Nullable:False
           /// </summary>
        public string MaterialCode { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// Desc:物料名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MtrName { get; set; }

    }
}