using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class AutoLogsheetCreatRequestModel
    {
        public AutoLogsheetCreatRequestModel()
        {
        }
           /// <summary>
           /// Desc:批次ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string BatchId { get; set; }
           /// <summary>
           /// Desc:参数组ID（日志表名）
           /// Default:
           /// Nullable:False
           /// </summary>
        public string ParameterGroupId { get; set; }
           /// <summary>
           /// Desc:工单ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string PoId { get; set; }
        /// <summary>
        /// Desc:执行工单ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string PoExId { get; set; }
        /// <summary>
        /// Desc:设备ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string EquipmentId { get; set; }


    }
}