using Magicodes.ExporterAndImporter.Core;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.ViewModels.PPM
{
    public class CipTimeExcelDto
    {
        /// <summary>
        /// Desc:产线名称
        /// Default:
        /// Nullable:False
        /// </summary>
        [ImporterHeader(Name = "产线代码")]
        [ExporterHeader(DisplayName = "产线代码")]
        public string LineName { get; set; }

        /// <summary>
        /// Desc:切换名称
        /// Default:
        /// Nullable:False
        /// </summary>
        [ImporterHeader(Name = "切换方式")]
        [ExporterHeader(DisplayName = "切换方式")]
        public string Switchname { get; set; }
        /// <summary>
        /// Desc:切换时间
        /// Default:
        /// Nullable:False
        /// </summary>
        [ImporterHeader(Name = "切换时间")]
        [ExporterHeader(DisplayName = "切换时间")]
        public int Switchtime { get; set; }
    }
}
