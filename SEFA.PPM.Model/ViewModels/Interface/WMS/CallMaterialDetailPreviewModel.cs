using System;

namespace SEFA.PPM.Model.ViewModels.Interface.WMS
{
    /// <summary>
    /// 叫料明细预览模型
    /// </summary>
    public class CallMaterialDetailPreviewModel
    {
        /// <summary>
        /// 物料ID
        /// </summary>
        public string MaterialId { get; set; }

        /// <summary>
        /// 物料代码
        /// </summary>
        public string MaterialCode { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        public string MaterialName { get; set; }

        /// <summary>
        /// 物料版本ID
        /// </summary>
        public string MaterialVersionId { get; set; }

        /// <summary>
        /// 物料版本代码
        /// </summary>
        public string MaterialVersionCode { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 需求数量
        /// </summary>
        public decimal? RequiredQty { get; set; }

        /// <summary>
        /// 库存数量
        /// </summary>
        public decimal InventoryQty { get; set; }

        /// <summary>
        /// 叫料数量
        /// </summary>
        public decimal RequestQty { get; set; }

        /// <summary>
        /// 批次号
        /// </summary>
        public string BatchNo { get; set; }
    }
}