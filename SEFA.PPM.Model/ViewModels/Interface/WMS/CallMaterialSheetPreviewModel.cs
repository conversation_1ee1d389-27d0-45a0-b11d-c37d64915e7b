using System;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;

namespace SEFA.PPM.Model.ViewModels.Interface.WMS
{
    /// <summary>
    /// 叫料单预览模型（用于前端确认）
    /// </summary>
    public class CallMaterialSheetPreviewModel
    {
        /// <summary>
        /// 预生成的叫料单号
        /// </summary>
        public string RequestSheetNo { get; set; }

        /// <summary>
        /// 工单ID
        /// </summary>
        public string ProductionOrderId { get; set; }

        /// <summary>
        /// 产线代码
        /// </summary>
        public string LineCode { get; set; }

        /// <summary>
        /// 仓库ID
        /// </summary>
        public string WarehouseId { get; set; }

        /// <summary>
        /// 仓库名称
        /// </summary>
        public string WarehouseName { get; set; }

        /// <summary>
        /// 叫料明细列表
        /// </summary>
        public List<CallMaterialDetailPreviewModel> Details { get; set; }
    }
}