using System;

namespace SEFA.PPM.Model.ViewModels.Interface.WMS
{
    /// <summary>
    /// 叫料申请明细视图模型
    /// </summary>
    public class DistributionMaterialDetailView
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 申请主表ID
        /// </summary>
        public string RequestId { get; set; }

        /// <summary>
        /// 申请单号
        /// </summary>
        public string RequestSheetNo { get; set; }

        /// <summary>
        /// 线边仓编码
        /// </summary>
        public string LineWarehouseCode { get; set; }

        /// <summary>
        /// 工厂
        /// </summary>
        public string Plant { get; set; }

        /// <summary>
        /// 物料ID
        /// </summary>
        public string MaterialId { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        public string MaterialCode { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        public string MaterialName { get; set; }

        /// <summary>
        /// 物料版本ID
        /// </summary>
        public string MaterialVersionId { get; set; }

        /// <summary>
        /// 物料版本编码
        /// </summary>
        public string MaterialVersionCode { get; set; }

        /// <summary>
        /// 批次号
        /// </summary>
        public string BatchNo { get; set; }

        /// <summary>
        /// 托盘号
        /// </summary>
        public string PalletNo { get; set; }

        /// <summary>
        /// 物料唯一标签码
        /// </summary>
        public string BarCode { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// 计量单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 密度
        /// </summary>
        public decimal? Density { get; set; }

        /// <summary>
        /// Coa含量%
        /// </summary>
        public decimal? CoAContent { get; set; }

        /// <summary>
        /// 备注说明
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 是否已入库(0:未入库,1:已入库)
        /// </summary>
        public int IsPutInStorage { get; set; }

        /// <summary>
        /// 是否已入库描述
        /// </summary>
        public string IsPutInStorageDesc => IsPutInStorage == 1 ? "已入库" : "未入库";

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        public string CreateUserId { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyDate { get; set; }

        /// <summary>
        /// 修改人ID
        /// </summary>
        public string ModifyUserId { get; set; }
    }
}
