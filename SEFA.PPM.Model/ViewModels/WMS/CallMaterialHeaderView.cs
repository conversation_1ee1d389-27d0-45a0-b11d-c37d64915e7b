using System;
using System.Collections.Generic;
using SEFA.PPM.Model.Models.Interface.WMS;

namespace SEFA.PPM.Model.ViewModels.WMS
{
    public class CallMaterialHeaderView
    {
        /// <summary>
        /// 叫料单ID
        /// </summary>
        public string ID { get; set; }

        /// <summary>
        /// 工单号
        /// </summary>
        public string ProductionOrder { get; set; }
        
        /// <summary>
        /// 叫料单号
        /// </summary>
        public string RequestSheetNo { get; set; }

        /// <summary>
        /// 工单ID
        /// </summary>
        public string ProductionOrderId { get; set; }

        /// <summary>
        /// 叫料类型，正常：MATERIAL，IBC空桶:EMPTY_IBC
        /// </summary>
        public string RequestType { get; set; }

        /// <summary>
        /// 叫料时间
        /// </summary>
        public DateTime RequestTime { get; set; }

        /// <summary>
        /// 线边仓
        /// </summary>
        public string LineCode { get; set; }

        /// <summary>
        /// 叫料点
        /// </summary>
        public string PositionCode { get; set; }

        /// <summary>
        /// 叫料状态 (0-未叫料 1-已叫料 2-已完成)
        /// </summary>
        public string CallStatus { get; set; }

        /// <summary>
        /// 操作类型，1：叫料，-1 ：取消叫料
        /// </summary>
        public string OperationType { get; set; }
        
        /// <summary>
        /// 叫料明细
        /// </summary>
        public List<CallMaterialDetailEntity> Details { get; set; }

        /// <summary>
        /// 逻辑删除标记(0:未删除 1:已删除)
        /// </summary>
        public int Deleted { get; set; }
        
        /// <summary>
        /// 创建人ID
        /// </summary>
        public string CreateUserId { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateDate { get; set; }
    }
}