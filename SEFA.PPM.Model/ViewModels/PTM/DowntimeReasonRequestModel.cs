using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
	public class DowntimeReasonRequestModel : RequestPageModelBase
	{
		public DowntimeReasonRequestModel()
		{
		}
		/// <summary>
		/// Desc:Search
		/// Default:
		/// Nullable:False
		/// </summary>
		public string Search { get; set; }
		/// <summary>
		/// Desc:分组ID
		/// Default:
		/// Nullable:False
		/// </summary>
		public string GroupId { get; set; }
		/// <summary>
		/// Desc:描述
		/// Default:
		/// Nullable:False
		/// </summary>
		public string Description { get; set; }
		/// <summary>
		/// Desc:关联上游
		/// Default:
		/// Nullable:False
		/// </summary>
		public string LookUpstream { get; set; }
		/// <summary>
		/// Desc:关联下游
		/// Default:
		/// Nullable:False
		/// </summary>
		public string LookDownstream { get; set; }
		/// <summary>
		/// Desc:扩展编码
		/// Default:
		/// Nullable:True
		/// </summary>
		public string ExternalCode { get; set; }
		/// <summary>
		/// Desc:状态
		/// Default:
		/// Nullable:False
		/// </summary>
		public string Status { get; set; }
		/// <summary>
		/// Desc:可编辑
		/// Default:
		/// Nullable:False
		/// </summary>
		public string IsEditable { get; set; }
		/// <summary>
		/// Desc:是否需要评论
		/// Default:
		/// Nullable:False
		/// </summary>
		public string IsCommentRequired { get; set; }
		/// <summary>
		/// Desc:启用采集
		/// Default:
		/// Nullable:True
		/// </summary>
		public string EnableAcquisition { get; set; }
		/// <summary>
		/// Desc:小停顿扩展编码
		/// Default:
		/// Nullable:True
		/// </summary>
		public string MinorStopExternalCode { get; set; }
		/// <summary>
		/// Desc:时间分类
		/// Default:
		/// Nullable:False
		/// </summary>
		public string TimeClassification { get; set; }
		/// <summary>
		/// Desc:是否需要重新分类
		/// Default:
		/// Nullable:True
		/// </summary>
		public string ReclassifyRequired { get; set; }
		/// <summary>
		/// Desc:是否灵活换型换产
		/// Default:
		/// Nullable:True
		/// </summary>
		public string VariableChangeover { get; set; }
		/// <summary>
		/// Desc:是否无列表
		/// Default:
		/// Nullable:False
		/// </summary>
		public string IsUnlisted { get; set; }
		/// <summary>
		/// Desc:需要工单
		/// Default:
		/// Nullable:False
		/// </summary>
		public string RequiresWorkOrder { get; set; }
		/// <summary>
		/// Desc:图标
		/// Default:
		/// Nullable:True
		/// </summary>
		public string IconClass { get; set; }
		/// <summary>
		/// Desc:禁止路径
		/// Default:
		/// Nullable:True
		/// </summary>
		public string DisableRoutes { get; set; }

	}
}