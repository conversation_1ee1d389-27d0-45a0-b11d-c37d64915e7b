using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels.PTM
{
	public class DowntimeRequestModel : RequestPageModelBase
	{
		public DowntimeRequestModel()
		{
		}
		/// <summary>
		/// Desc:设备ID
		/// Default:
		/// Nullable:False
		/// </summary>
		public string EquipmentId { get; set; }
		/// <summary>
		/// Desc:事件开始时间
		/// Default:
		/// Nullable:False
		/// </summary>
		public DateTime? StartTimeUtc { get; set; }
		/// <summary>
		/// Desc:事件结束时间
		/// Default:
		/// Nullable:True
		/// </summary>
		public DateTime? EndTimeUtc { get; set; }
		/// <summary>
		/// Desc:工单执行ID
		/// Default:
		/// Nullable:True
		/// </summary>
		public string PoExecutionId { get; set; }
		/// <summary>
		/// Desc:原因ID
		/// Default:
		/// Nullable:False
		/// </summary>
		public string ReasonId { get; set; }
		/// <summary>
		/// Desc:是否运行时
		/// Default:
		/// Nullable:False
		/// </summary>
		public int IsRuntime { get; set; }
		/// <summary>
		/// Desc:是否停机
		/// Default:
		/// Nullable:False
		/// </summary>
		public int IsBreakdown { get; set; }
		/// <summary>
		/// Desc:PLC代码
		/// Default:
		/// Nullable:True
		/// </summary>
		public string PlcCode { get; set; }
		/// <summary>
		/// Desc:评论
		/// Default:
		/// Nullable:True
		/// </summary>
		public string Comment { get; set; }
		/// <summary>
		/// Desc:工单ID
		/// Default:
		/// Nullable:True
		/// </summary>
		public string OrderId { get; set; }
		/// <summary>
		/// Desc:是否瓶颈事件
		/// Default:
		/// Nullable:False
		/// </summary>
		public string IsBottleneckEvent { get; set; }
		/// <summary>
		/// Desc:班次ID
		/// Default:
		/// Nullable:False
		/// </summary>
		public string ShiftId { get; set; }
		/// <summary>
		/// Desc:人员数量
		/// Default:
		/// Nullable:False
		/// </summary>
		public string CrewSize { get; set; }
		/// <summary>
		/// Desc:来源类型
		/// Default:
		/// Nullable:True
		/// </summary>
		public string SourceType { get; set; }
	}
}