using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels.PTM
{
	public class StopPoRequestModel
	{
		public string ExecutionId { get; set; }

		public DateTime? EndTime { get; set; }

		public bool IsComplete { get; set; }

		public string ProduceStatus { get; set; }

		public string Reason { get; set; }

	}
}